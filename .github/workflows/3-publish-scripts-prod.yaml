name: Publish Platform Scripts - Prod
on:
  workflow_call:
    secrets:
      GH_TOKEN:
        required: true
    inputs:
      GH_REPOSITORY:
        required: false
        type: string
        default: 'Humanapis/platform-scripts'
      INPUT_BRANCH:
        required: false
        type: string
        default: 'staging'
      OUTPUT_BRANCH:
        required: false
        type: string
        default: 'main'

jobs:
  publish:
    name: Create PR and merge it
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    env:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      GH_REPOSITORY: ${{ inputs.GH_REPOSITORY }}
      INPUT_BRANCH: ${{ inputs.INPUT_BRANCH }}
      OUTPUT_BRANCH: ${{ inputs.OUTPUT_BRANCH }}
    steps:
      - name: Clone repository
        uses: actions/checkout@v4
        with:
          repository: ${{ env.GH_REPOSITORY }}
          token: ${{ env.GH_TOKEN }}
          ref: ${{ env.OUTPUT_BRANCH }}

      - name: Create and merge PR
        run: |
          git fetch origin
          if git diff --quiet origin/${{env.OUTPUT_BRANCH}}..origin/${{env.INPUT_BRANCH}}; then
              echo "No differences between ${{env.OUTPUT_BRANCH}} and ${{env.INPUT_BRANCH}}."
          else
              echo "Differences detected between ${{env.OUTPUT_BRANCH}} and ${{env.INPUT_BRANCH}}."
              echo "Proceeding with PR creation."
              NOW=$(date +'%Y-%m-%dT%H:%M:%S')
              gh pr create --title "Sync Staging scripts to Prod - $NOW" --body "Sync Staging scripts to Prod - $NOW" --base ${{env.OUTPUT_BRANCH}} --head ${{env.INPUT_BRANCH}} --no-maintainer-edit --repo ${{ env.GH_REPOSITORY }}
              PR_NUMBER=$(gh pr list --repo ${{ env.GH_REPOSITORY }} --head ${{env.INPUT_BRANCH}} --state open --json number --jq '.[0].number')
              if [ -z "$PR_NUMBER" ]; then
                echo "Failed to create PR"
                exit 1
              else
                echo "Created PR $PR_NUMBER"
                echo "Merging PR $PR_NUMBER"
                gh pr merge $PR_NUMBER --repo ${{ env.GH_REPOSITORY }} --merge --admin
                echo "PR $PR_NUMBER merged"
              fi
          fi
