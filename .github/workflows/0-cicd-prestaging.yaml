name: PRE-STAGING Batuta Soar CICD - Build Push Docker Deploy ArgoCD
on:
  pull_request:
    branches:
      - 'pre-staging'
    types:
      - closed
    workflow_dispatch:

jobs:
  call-ci-workflow-staging:
    if: github.ref == 'refs/heads/pre-staging'
    uses: ./.github/workflows/1-build-image-prestaging.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
    with:
      ECR_REPOSITORY: batuta-staging-us-east-1-ecr-soar
      AWS_REGION: us-east-1
      AWS_ACCOUNT_ID_STAGING: ${{vars.AWS_ACCOUNT_ID_STAGING }}
