name: PROD Batuta Soar CICD - Build Push Docker Deploy ArgoCD
on:
  pull_request:
    branches:
      - 'main'
    types:
      - closed

concurrency: ${{ github.ref == 'main' }}

jobs:
  call-ci-workflow-prod:
    if: github.ref == 'refs/heads/main' && github.event.pull_request.merged == true
    uses: ./.github/workflows/1-build-image-prod.yaml
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
    with:
      ECR_REPOSITORY: batuta-prod-us-east-1-ecr-soar
      AWS_REGION: us-east-1
      AWS_ACCOUNT_ID_PROD: ${{vars.AWS_ACCOUNT_ID_PROD }}

  tag-docker-image-prod:
    if: github.ref == 'refs/heads/main' && github.event.pull_request.merged == true
    uses: ./.github/workflows/2-cd-update-tag-prod.yaml
    needs: call-ci-workflow-prod
    with:
      environment: 'prod'
      service: batuta-soar
      tag: ${{needs.call-ci-workflow-prod.outputs.tag}}
      values-path: application/prod/clients
      tag-path: 'image.tag'
    secrets:
      token: ${{ secrets.GH_TOKEN }}

  publish-scripts-prod:
    if: github.head_ref == 'staging' && github.ref == 'refs/heads/main' && github.event.pull_request.merged == true
    uses: ./.github/workflows/3-publish-scripts-prod.yaml
    secrets:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
