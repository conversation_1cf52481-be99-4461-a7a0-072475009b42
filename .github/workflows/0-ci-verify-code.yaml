name: Verify code - PR and Pushes on Batuta Soar

on:
  pull_request:
    branches:
      - 'main'
      - 'staging'
      - 'dev'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  verify-code:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}

      - name: Update
        run: sudo apt-get update

      - name: Install extra dependencies
        run: |
          sudo apt-get install -y build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version: 'lts/*'
          cache: 'yarn'

      - name: Show node version
        run: node -v

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run lint
        run: yarn lint

      - name: Ts check
        run: yarn check:types

      - name: Build project
        run: yarn build
