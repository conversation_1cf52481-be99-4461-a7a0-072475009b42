import { QueuesTask } from '@root/services/Queues/schemas';

import { CANCELLED, EXPIRED, FINISHED } from '@root/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';

const up = async () => {
  await QueuesTask.deleteMany({
    name: TASK_NAMES.UNINSTALL_BATUTA,
    status: { $in: [CANCELLED, EXPIRED, FINISHED] },
  });
};

module.exports = {
  up,
  down: async () => {},
};
