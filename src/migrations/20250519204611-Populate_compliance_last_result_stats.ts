import { ControlResultHost, PolicyHostLastResult } from '@root/services/Compliance/schemas';
import { Logger } from '@root/shared/helpers/classes/logger.class';

const up = async () => {
  const policiesLastResults = await PolicyHostLastResult.find({}, '_id policyRunHost');

  Logger.info(`Populating stats for ${policiesLastResults.length} policies last results.`);

  await Promise.all(
    policiesLastResults.map(async (policyLastResult) => {
      const results = await ControlResultHost.find(
        { policyRunHost: policyLastResult.policyRunHost },
        'testResult'
      );

      const totalControls = results.length || 0;
      const passedControls = results.filter((result) => result.testResult === 'Passed').length || 0;
      let successPercentage = 0;
      if (totalControls > 0 && Number.isFinite(passedControls)) {
        successPercentage = parseFloat(((passedControls / totalControls) * 100).toFixed(2));
      }

      return PolicyHostLastResult.findByIdAndUpdate(policyLastResult._id, {
        totalControls,
        passedControls,
        successPercentage,
      });
    })
  );

  Logger.info('Population of last host results stats finished.');
};

module.exports = {
  up,
  down: async () => {},
};
