import { User } from '@root/shared/schemas';

const PERMISSIONS_TO_RENAME = {
  'read:queues.tasks': 'read:queues.task',
  'modify:queues.tasks': 'modify:queues.task',
  'create:queues.tasks': 'create:queues.task',
  'export:queues.tasks': 'export:queues.task',
};

const up = async () => {
  for (const [oldPermission, newPermission] of Object.entries(PERMISSIONS_TO_RENAME)) {
    await User.updateMany(
      { permissions: oldPermission },
      { $set: { 'permissions.$': newPermission } }
    );
  }
};

module.exports = {
  up,
  down: async () => {},
};
