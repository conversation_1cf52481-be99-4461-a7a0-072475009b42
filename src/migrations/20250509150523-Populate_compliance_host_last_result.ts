import { Policy, PolicyRunHost, PolicyHostLastResult } from '@root/services/Compliance/schemas';
import { Logger } from '@root/shared/helpers/classes/logger.class';

const up = async () => {
  const policies = await Policy.find({ deleted: false }, '_id');

  Logger.info(`Populating last host result for ${policies.length} policies.`);

  const lastResults = await PolicyRunHost.aggregate([
    {
      $match: {
        policy: { $in: policies.map((p) => p._id) },
      },
    },
    // Sorting documents by date in descending order
    { $sort: { ran_at: -1 } },
    // Grouping by host and selecting only the first (most recent) document for each host
    {
      $group: {
        _id: { host: '$host', policy: '$policy' },
        document: { $first: '$$ROOT' },
      },
    },
    // Replacing the root of the document with the most recent document for each host
    { $replaceRoot: { newRoot: '$document' } },
  ]);

  await Promise.all(
    lastResults.map((lastResult) =>
      PolicyHostLastResult.findOneAndUpdate(
        {
          policy: lastResult.policy,
          host: lastResult.host,
        },
        {
          policy: lastResult.policy,
          host: lastResult.host,
          policyRun: lastResult.policy_run,
          policyRunHost: lastResult._id,
          ran_at: lastResult.ran_at,
          status: lastResult.status,
          updatedAt: lastResult.updatedAt,
          createdAt: lastResult.createdAt,
        },
        {
          upsert: true,
        }
      )
    )
  );

  Logger.info('Population of last host results finished.');
};

module.exports = {
  up,
  down: async () => {},
};
