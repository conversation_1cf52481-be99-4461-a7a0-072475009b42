import { isEmpty } from 'lodash';

import { QueuesTask } from '@root/services/Queues/schemas';

import { PENDING, IN_PROGRESS, VALIDATING } from '@root/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';

const up = async () => {
  const tasks: any[] = await QueuesTask.find({
    name: TASK_NAMES.RUN_COMMAND,
    status: { $in: [PENDING, IN_PROGRESS, VALIDATING] },
  }).lean();

  const updatedTasks = tasks
    .filter(
      (task) => !task.params.hasOwnProperty('affectedHosts') || !isEmpty(task.previous_summary)
    )
    .map((task) => {
      let finishedClientIds = [];
      if (!isEmpty(task.previous_summary)) {
        finishedClientIds = task.previous_summary.finishedClientIds;
      }

      // Move `finishedClientIds` to `params.affectedHosts`
      task.params.affectedHosts = finishedClientIds;

      // Combine `finishedClientIds` with `params.clientIds` into `params.selectedHosts`
      task.params.selectedHosts = [...(task.params.clientIds || []), ...finishedClientIds];

      // Remove previous_summary
      task.previous_summary = {};

      return task;
    });

  await Promise.all(
    updatedTasks.map(async (task) => await QueuesTask.updateOne({ _id: task._id }, task))
  );
};

module.exports = {
  up,
  down: async () => {},
};
