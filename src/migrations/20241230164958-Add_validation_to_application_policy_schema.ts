import { ApplicationPolicy } from '@root/services/Inventory/schemas';

const up = async () => {
  await ApplicationPolicy.updateMany(
    { 'policies.vendor': { $exists: true, $not: { $type: 'string' } } },
    { $set: { 'policies.$[elem].vendor': '' } },
    { arrayFilters: [{ 'elem.vendor': { $exists: true, $not: { $type: 'string' } } }] }
  );
};

module.exports = {
  up,
  down: async () => {},
};
