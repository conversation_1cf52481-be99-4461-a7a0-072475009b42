import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
import { QueuesTask } from '@root/services/Queues/schemas/task.schema';

const up = async () => {
  await QueuesTask.updateMany(
    { name: TASK_NAMES.SCAN_CLIENT },
    { $rename: { 'params.clientId': 'params.hostId' } }
  );
  await QueuesTask.updateMany(
    { name: TASK_NAMES.UNINSTALL_SOFTWARE },
    {
      $rename: {
        'params.rportIds': 'params.selectedHosts',
        'params.finishedRportIds': 'params.affectedHosts',
      },
    }
  );
};

module.exports = {
  up,
  down: async () => {
    await QueuesTask.updateMany(
      { name: TASK_NAMES.SCAN_CLIENT },
      { $rename: { 'params.hostId': 'params.clientId' } }
    );
    await QueuesTask.updateMany(
      { name: TASK_NAMES.UNINSTALL_SOFTWARE },
      {
        $rename: {
          'params.selectedHosts': 'params.rportIds',
          'params.affectedHosts': 'params.finishedRportIds',
        },
      }
    );
  },
};
