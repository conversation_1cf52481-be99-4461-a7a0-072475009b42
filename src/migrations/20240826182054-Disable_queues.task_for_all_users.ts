import { User } from '@root/shared/schemas';

const PERMISSIONS_TO_REMOVE = [
  'read:queues.tasks',
  'read:queues.task',
  'modify:queues.tasks',
  'modify:queues.task',
  'create:queues.tasks',
  'create:queues.task',
  'export:queues.tasks',
  'export:queues.task',
];

const up = async () => {
  for (const permission of PERMISSIONS_TO_REMOVE) {
    await User.updateMany({ permissions: permission }, { $pull: { permissions: permission } });
  }
};

module.exports = {
  up,
  down: async () => {},
};
