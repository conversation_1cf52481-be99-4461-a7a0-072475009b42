import { getStatusSnapshot } from '@root/services/Compliance/helpers/queries';
import { getHistoryPeriods } from '@root/services/Compliance/helpers/utils';
import { Policy, PolicyResultsSnapshot } from '@root/services/Compliance/schemas';
import { getHostIdsFromGroup } from '@root/services/Rport/helpers/functions';
import { RportClient } from '@root/services/Rport/schemas';
import { Logger } from '@root/shared/helpers/classes/logger.class';

const up = async () => {
  const policies = await Policy.find({ deleted: false }, '_id createdAt group');

  Logger.info(`Populating results snapshots for ${policies.length} policies.`);

  const results = [];
  for (const policy of policies) {
    // Getting rport id group members
    const rportHostIds = await getHostIdsFromGroup(policy.group);
    // Getting mongo id group members
    const targetedHostIds = await RportClient.find({
      rportId: { $in: rportHostIds },
      deleted: false,
    }).distinct('_id');
    // Getting last 90 days period for snapshots
    const periods = getHistoryPeriods(policy.createdAt);
    for (const period of periods) {
      // Getting snapshot
      const metrics = await getStatusSnapshot(policy._id, targetedHostIds, new Date(period));
      results.push({
        policy: policy._id,
        date: new Date(period),
        compliance_status: metrics.compliance_status,
      });
    }
  }

  await PolicyResultsSnapshot.insertMany(results);

  Logger.info('Population of results snapshots finished.');
};

module.exports = {
  up,
  down: async () => {},
};
