import { BASIC_SERVICES } from '@root/shared/constants/basic-services';
import { Service, UserDocument } from '@root/shared/schemas';
import { getFunctionalityByInternalNameOrThrow } from '@root/shared/utils/functionalities';
import { Logger } from '../classes/logger.class';

export const userHasPermissionToFunctionality = async (
  user: UserDocument,
  permission: string
): Promise<boolean> => {
  try {
    if (!user) return false;

    const [actionService, functionality] = permission.split('.');
    if (!actionService || !functionality) return false;

    const [action, service] = actionService.split(':');
    if (!action || !service) return false;

    // check services that do not require permission checks
    if (BASIC_SERVICES.includes(service)) return true;

    const serviceDB = await Service.findOne({ internalName: service, enabled: true });
    if (!serviceDB) return false;

    //check functionality
    const functionalityDB = await getFunctionalityByInternalNameOrThrow(functionality);
    if (!functionalityDB || !functionalityDB.enabled) return false;

    if (user.isAdmin || user.isManager) return true;

    if (user.permissions.includes(permission))
      // check permission
      return true;

    return false;
  } catch (error) {
    Logger.error(error);
    return false;
  }
};
