import { BatchData, BatchMessage } from '@shared/types';

export class BatchAccumulator {
  private cache: Map<string, BatchData>;

  constructor() {
    this.cache = new Map();
  }

  /**
   * Adds a batch message to the accumulator.
   * If all parts of the batch are received, returns the combined batch output.
   * Otherwise, returns null.
   * @param message - Batch message to add.
   * @param key - Batch key.
   * @returns Combined batch output or null if not complete.
   */
  public addMessage<T>(message: BatchMessage<T>, key: string): any[] | null {
    let accumulator = this.cache.get(key);

    if (!accumulator) {
      accumulator = {
        totalBatches: message.totalBatches,
        parts: new Map<number, any[]>(),
        received: 0,
      };
      this.cache.set(key, accumulator);
    }

    // Add the batch part only if it hasn't been added yet
    if (!accumulator.parts.has(message.batchIndex)) {
      accumulator.parts.set(message.batchIndex, message.outputBatch);
      accumulator.received++;
    }

    // When all parts have been received, combine and return the complete batch
    if (accumulator.received === accumulator.totalBatches) {
      const combinedOutput = Array.from(accumulator.parts.entries())
        .sort((a, b) => a[0] - b[0])
        .reduce((acc: any[], [_, batch]) => acc.concat(batch), []);

      // Remove the accumulator once processed
      this.cache.delete(key);
      return combinedOutput;
    }

    return null;
  }

  /**
   * Clears all stored batch data.
   */
  public clear(): void {
    this.cache.clear();
  }
}
