import { Consumer, EachMessagePayload, Kafka } from 'kafkajs';

import { Logger } from '@shared/helpers/classes/logger.class';
import { KAFKA_BROKERS, KAFKA_AUTH_USER, KAFKA_AUTH_PASS, SOAR_ID } from '@shared/constants/env';
import { KAFKA_ASSET_DISCOVERY_QUEUE_NAME } from '@services/AssetDiscovery/helpers/constants/queue.constants';
import { KAFKA_COMPLIANCE_RESULTS_QUEUE_NAME } from '@services/Compliance/helpers/constants/queue.constant';
import { processScanResult } from '@services/AssetDiscovery/consumers/scannedHost.consumers';
import { processPolicyRunResults } from '@services/Compliance/consumers/hostResults.consumers';

export const MESSAGE_PROCESSORS = {
  [KAFKA_COMPLIANCE_RESULTS_QUEUE_NAME]: processPolicyRunResults,
  [KAFKA_ASSET_DISCOVERY_QUEUE_NAME]: processScanResult,
};

export const KAFKA_TOPICS = Object.keys(MESSAGE_PROCESSORS);

export class KafkaConsumerService {
  private consumer: Consumer;
  private isConnected: boolean = false;

  constructor() {
    Logger.info(`Initializing KafkaConsumer for topics: ${KAFKA_TOPICS}`);
    const kafka = new Kafka({
      clientId: 'soar-api',
      brokers: KAFKA_BROKERS.split(','),
      retry: {
        initialRetryTime: 300,
        retries: 10,
      },
      ssl: false,
      ...(KAFKA_AUTH_PASS && KAFKA_AUTH_USER
        ? {
            sasl: {
              mechanism: 'plain',
              username: KAFKA_AUTH_USER,
              password: KAFKA_AUTH_PASS,
            },
          }
        : {}),
    });

    this.consumer = kafka.consumer({
      groupId: `soar-api-${SOAR_ID}`,
      allowAutoTopicCreation: true,
    });
  }

  async connect(): Promise<void> {
    try {
      await this.consumer.connect();
      for (const topic of KAFKA_TOPICS) {
        await this.consumer.subscribe({ topic, fromBeginning: true });
      }
      this.isConnected = true;
      Logger.info(`KafkaConsumer connected successfully to topics: ${KAFKA_TOPICS}`);
    } catch (error) {
      Logger.error('Failed to connect KafkaConsumer:', error);
    }
  }

  async disconnect(): Promise<void> {
    if (this.isConnected) {
      await this.consumer.disconnect();
      this.isConnected = false;
      Logger.info('KafkaConsumer disconnected');
    }
  }

  async startConsuming(): Promise<void> {
    if (!this.isConnected) {
      throw new Error('KafkaConsumer not connected.');
    }

    await this.consumer.run({
      partitionsConsumedConcurrently: 3,

      eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {
        if (!MESSAGE_PROCESSORS[topic]) {
          Logger.warning(`No processor found for topic: ${topic}`);
          return;
        }

        const maxRetries = 3;
        let retryCount = 0;
        let processed = false;

        while (!processed && retryCount < maxRetries) {
          try {
            const messageValue = message.value ? JSON.parse(message.value.toString()) : null;

            Logger.info(`Processing partition ${partition} of topic ${topic}`);
            await MESSAGE_PROCESSORS[topic](messageValue);
            processed = true;
          } catch (error) {
            retryCount++;
            Logger.error(
              `Error processing Kafka message (attempt ${retryCount}/${maxRetries}):`,
              error
            );

            if (retryCount < maxRetries) {
              await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
            } else {
              Logger.error(`Failed to process message after ${maxRetries} attempts, moving on`);
            }
          }
        }
      },
    });

    Logger.info(`KafkaConsumer started successfully for topics: ${KAFKA_TOPICS}`);
  }
}
