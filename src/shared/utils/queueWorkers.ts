import { KafkaConsumerService } from '@root/shared/helpers/classes/queue-worker.class';
import { KAFKA_BROKERS } from '@shared/constants/env';
import { Logger } from '@shared/helpers/classes/logger.class';

let kafkaConsumer: KafkaConsumerService;

export const initQueueConsumer = async () => {
  if (!KAFKA_BROKERS) {
    Logger.warning('Kafka environment variables not set, skipping consumer initialization.');
    return;
  }

  if (!kafkaConsumer) {
    kafkaConsumer = new KafkaConsumerService();
  }

  await kafkaConsumer.connect();
  kafkaConsumer.startConsuming();
};
