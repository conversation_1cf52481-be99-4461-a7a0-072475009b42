import { ValidationErrors } from '../types/validation-errors.types';

import { HttpError } from '../models';

import {
  MAX_TAGS_PER_HOST,
  MAX_TAGS_TENANT,
  MAX_TAG_CHARS,
} from '@root/services/Rport/helpers/constants';
import { MetadataType } from '../models/http-error.model';

function serializeValue(value: string) {
  return value.trim().replace(/\s/g, '_').toUpperCase();
}

export const generateMetadataError = (
  message: string,
  status: number,
  code: string,
  metadata: MetadataType
) => generateError(message, status, code, undefined, undefined, metadata);

export const generateError = (
  message: string,
  status: number,
  code?: string,
  validationErrors?: ValidationErrors,
  errors?: [Error],
  metadata?: MetadataType
) => new HttpError(message, status, code, validationErrors, errors, metadata);

export const generateMissingParamsError = (parameterList: string[]) => {
  const errors: ValidationErrors = parameterList.reduce((acc: ValidationErrors, param) => {
    acc[param] = {
      type: 'REQUIRED',
      value: null,
    };
    return acc;
  }, {});

  return generateError('Validation Error', 422, 'VALIDATION_ERROR', errors);
};

export const errors = {
  // Server errors (500-599)
  internal_error: () => generateError('Internal Server Error', 500, 'INTERNAL_SERVER_ERROR'),
  crowdstrike_token_error: () =>
    generateError('Could not get token from Crowdstrike', 503, 'CROWDSTRIKE_TOKEN_ERROR'),
  crowdstrike_communication_error: () =>
    generateError('Could not communicate with Crowdstrike', 503, 'CROWDSTRIKE_COMMUNICATION_ERROR'),
  crowdstrike_device_connection: (id: string) =>
    generateMetadataError(
      `Could not stablish connection with the device ${id}`,
      400,
      'CROWDSTRIKE_DEVICE_CONNECTION_ERROR',
      { deviceId: id }
    ),
  crowdstrike_authentication: () =>
    generateError('Access denied, invalid bearer token', 400, 'CROWDSTRIKE_AUTHENTICATION_ERROR'),
  crowdstrike_invalid_device: (id: string) =>
    generateMetadataError(
      `Device ${id} not found on CrowdStrike`,
      400,
      'CROWDSTRIKE_INVALID_DEVICE',
      { deviceId: id }
    ),
  crowdstrike_device_not_found: () =>
    generateError(`Device not found on CrowdStrike`, 400, 'CROWDSTRIKE_INVALID_DEVICE'),
  crowdstrike_execution_error: () =>
    generateError('Could not send the command to the device', 400, 'CROWDSTRIKE_EXECUTION_ERROR'),
  crowdstrike_custom_error: (message: string, code: number) =>
    generateError(message, code, 'CROWDSTRIKE_RTR_ERROR'),
  pentestools_communication_error: () => {
    generateError(
      'Could not communicate with PentestTools',
      503,
      'PENTESTOOLS_COMMUNICATION_ERROR'
    );
  },
  updating_issue_error: () =>
    generateError('Could not update the issue status', 503, 'JIRA_UPDATING_ERROR'),
  creating_issue_error: () =>
    generateError('Could not create the issue', 503, 'JIRA_CREATING_ERROR'),
  creating_comment_error: () =>
    generateError('Could not push the comment into the issue', 503, 'JIRA_CREATING_COMMENT_ERROR'),
  searching_issue_error: () =>
    generateError('Could not find the issue', 503, 'JIRA_SEARCHING_ERROR'),
  apt_token_error: () => generateError('Could not get token from APT', 503, 'APT_TOKEN_ERROR'),
  invalid_image_dimensions: () =>
    generateError('Invalid image dimensions For the PDF', 500, 'INVALID_IMAGE'),

  // Unauthorized access errors (403)
  not_authenticated: () => generateError('User not authenticated.', 401, 'NOT_AUTHENTICATED'),

  // Unauthorized access errors (403)
  not_allowed: (reason?: string) =>
    generateMetadataError(
      'Operation not allowed.' + (!!reason && !!process.env.DEV ? `\n${reason}` : ''),
      403,
      'NOT_ALLOWED',
      { reason: !!reason && !!process.env.DEV ? reason : undefined }
    ),
  not_permitted: (permission?: string) =>
    generateMetadataError(
      'Your account does not have the required permission to perform this operation.' +
        (!!permission && !!process.env.DEV ? `\n${permission}` : ''),
      403,
      'NOT_PERMITTED',
      {
        permission: !!permission && !!process.env.DEV ? permission : undefined,
      }
    ),
  account_disabled: () =>
    generateError('Your account is disabled or deleted.', 403, 'ACCOUNT_DISABLED_OR_DELETED'),
  telegram_id_not_found: () =>
    generateError('Telegram user not found.', 403, 'TELEGRAM_USER_NOT_FOUND'),

  // Bad request errors (400)
  params: (missing: string[] = []) =>
    generateMetadataError(
      `Please provide all required parameters. ${
        missing.length > 0 ? `Missing ${missing.map((x) => `'${x}'`).join(', ')}` : ''
      }`,
      400,
      'PARAMS_REQUIRED',
      { missing }
    ),
  wrong_type: (values: string[] = []) =>
    generateMetadataError(
      `Please provide the correct type of parameters. ${
        values.length > 0 ? `Wrong type parameters: ${values.map((x) => `'${x}'`).join(', ')}` : ''
      }`,
      400,
      'PARAMS_WRONG_TYPE',
      { values }
    ),
  protected_document: () => generateError('Protected document', 400, 'PROTECTED_DOCUMENT'),
  invalid_command_output: () =>
    generateError(
      "The stdout from check status command wasn't correct",
      400,
      'INVALID_COMMAND_OUTPUT'
    ),
  missing_file: () =>
    generateError(
      'You have to provide a file on the body of the request',
      400,
      'FILE_MISSING_ON_BODY'
    ),
  fail_uploading_file: () =>
    generateError(
      'There has been an error uploading the file to the cdn',
      400,
      'ERROR_UPLOADING_FILE'
    ),
  fail_removing_file: () =>
    generateError(
      'There has been an error removing the file from the cdn',
      400,
      'ERROR_REMOVING_FILE'
    ),
  invalid_platform_config: (type: string) =>
    generateMetadataError(
      `${type} must have either "servicename" or "commandline"`,
      400,
      'INVALID_PLATFORM_CONFIG',
      { type }
    ),
  platform_without_template: () =>
    generateError(
      `The platform must have a template to be not custom`,
      400,
      'PLATFORM_WITHOUT_TEMPALTE'
    ),
  platform_api_not_available: () =>
    generateError(
      `Custom platforms does not have API available`,
      400,
      'PLATFORM_API_NOT_AVAILABLE'
    ),
  platform_update_not_valid: () =>
    generateError(
      `To update a platform throught this route it must be custom`,
      400,
      'PLATFORM_UPDATE_NOT_VALID'
    ),
  email_not_valid: () => generateError('The email is not valid.', 400, 'EMAIL_NOT_VALID'),
  token_not_valid: () => generateError('Token invalid or expired!', 400, 'TOKEN_NOT_VALID'),
  twofa_not_valid: () => generateError('2FA information is not valid.', 400, '2FA_NOT_VALID'),
  type_not_valid: (message = 'The type selected is not valid') =>
    generateError(message, 400, 'TYPE_NOT_VALID'),
  input_not_valid: (message = 'User input is not valid') =>
    generateError(message, 400, 'INPUT_NOT_VALID'),
  days_not_valid: () => generateError('The selected days are not valid', 400, 'DAYS_NOT_VALID'),
  start_hour_not_valid: () =>
    generateError('The start hour is not valid', 400, 'START_HOUR_NOT_VALID'),
  end_hour_not_valid: () => generateError('The end hour is not valid', 400, 'END_HOUR_NOT_VALID'),
  device_already_registered: () =>
    generateError('Device already registered', 400, 'DEVICE_ALREADY_REGISTERED'),
  device_not_valid: () => generateError('Device not valid', 400, 'DEVICE_NOT_VALID'),
  device_not_eligible_for: (action = '') =>
    generateMetadataError(
      `Device not eligible for ${action}`,
      400,
      'DEVICE_NOT_ELIGIBLE_FOR_ACTION',
      { action }
    ),
  detection_not_valid: (cause = 'Invalid payload format') =>
    generateError(`Detection not valid: Cause: ${cause}`, 400, 'DETECTION_NOT_VALID'),
  detection_already_sent: () =>
    generateError('Detection already sent', 200, 'DETECTION_ALREADY_SENT'),
  severities_list_not_valid: () =>
    generateError('Severities list not valid', 400, 'SEVERITIES_LIST_NOT_VALID'),
  configuration_not_valid: () =>
    generateError('Configuration not valid', 400, 'CONFIGURATION_NOT_VALID'),
  containment_action_not_valid: () =>
    generateError('Containment action not valid', 400, 'CONTAINMENT_ACTION_NOT_VALID'),
  schedule_already_exists: () =>
    generateError('Schedule already exists', 400, 'SCHEDULE_ALREADY_EXISTS'),
  ad_already_in_action: () =>
    generateError('Ad already exists in the action', 400, 'AD_ALREADY_IN_ACTION'),
  ad_not_in_action: () => generateError('Ad is not in this action', 400, 'AD_NOT_IN_ACTION'),
  cron_already_exists: () => generateError('Cron already exists', 400, 'CRON_ALREADY_EXISTS'),
  invalid_cron_type: () => generateError('Cron type invalid', 400, 'INVALID_CRON_TYPE'),
  action_already_exists: () => generateError('Action already exists', 400, 'ACTION_ALREADY_EXISTS'),
  action_expired: () => generateError('The action requested has expired', 400, 'ACTION_EXPIRED'),
  auto_containment_executed: () =>
    generateError(
      'Device not eligible for auto containment lifting',
      400,
      'INVALID_AUTO_CONTAINMENT'
    ),
  cs_action_already_exists: () =>
    generateError('Crowdstrike action already exists', 400, 'CS_ACTION_ALREADY_EXISTS'),
  empty_devices_ids: () =>
    generateError('Could not find any devices for the user input', 400, 'EMPTY_DEVICES_IDS'),
  no_tags_found: () => generateError('No valid tags were found', 400, 'IOC_TAGS_NOT_FOUND'),
  console_already_exists: () =>
    generateError('Console already exists', 400, 'CONSOLE_ALREADY_EXISTS'),
  webhook_already_exists: () =>
    generateError('Webhook already exists', 400, 'WEBHOOK_ALREADY_EXISTS'),
  manager_already_exists: () =>
    generateError('Wazuh manager already exists', 400, 'WAZUH_MANAGER_ALREADY_EXISTS'),
  invalid_alert_level: () =>
    generateError(
      'Alert levels must be an array of number, between 1 and 15',
      400,
      'INVALID_ALERT_LEVELS'
    ),
  alert_not_valid: () => generateError('Alert not valid', 400, 'INVALID_ALERT_BODY'),
  alert_level_underestimate: () =>
    generateError('Alert level underestimate', 400, 'ALERT_LEVEL_UNDERESTIMATE'),
  alert_already_exists: (id: string = 'NaN') =>
    generateMetadataError(
      `The alert ${id} has already been registered`,
      400,
      'ALERT_ALREADY_EXISTS',
      { id }
    ),
  active_response_already_exists: () =>
    generateError(
      'Wazuh Active Response already exists',
      400,
      'WAZUH_ACTIVE_RESPONSE_ALREADY_EXISTS'
    ),

  os_not_supported: () =>
    generateError("This platform does not support the client's OS", 400, 'OS_NOT_SUPPORTED'),
  not_same_kernel: () =>
    generateError('One of the host does not have the same kernel', 400, 'NOT_SAME_KERNEL'),
  command_not_supported: () =>
    generateError(
      'The specified command is not supported in this platform',
      400,
      'COMMAND_NOT_SUPPORTED'
    ),
  check_command_not_supported: () =>
    generateError(
      'The check status command is not supported in this platform',
      400,
      'COMMAND_NOT_SUPPORTED'
    ),
  platform_not_in_client: (client?: string) =>
    generateMetadataError(
      `${client ?? 'This client'} doesn't have the selected platform installed`,
      400,
      'PLATFORM_NOT_IN_CLIENT',
      { client }
    ),
  token_expired: () => generateError('OneTimeToken has expired', 400, 'OTT_EXPIRED'),
  rport_instance_not_found: () =>
    generateError('Rport instance has not been initialized', 400, 'RPORT_INSTANCE_ERROR'),
  rport_host_not_found: () => generateError('Rport host not found', 400, 'RPORT_HOST_NOT_FOUND'),
  invalid_uninstalling_host: () =>
    generateError(
      'Host has not been marked as a candidate to be removed',
      400,
      'INVALID_UNINSTALLING_CALL'
    ),
  host_already_uninstalling: () =>
    generateError(
      'Host is already uninstalling please wait for the process to finish or cancel it first',
      400,
      'HOST_ALREADY_UNINSTALLING'
    ),
  host_is_connected: () =>
    generateError(
      'You can not remove a host that is connected to Batuta. Please disconnect it first.',
      400,
      'HOST_IS_CONNECTED'
    ),
  host_not_enabled: () =>
    generateError(
      'You can not handle a host that is not enabled. Please enable it first.',
      400,
      'HOST_NOT_ENABLED'
    ),
  rport_host_disconnected: () =>
    generateError('Rport host is not active', 400, 'RPORT_HOST_DISCONNECTED'),
  rport_host_is_connected: () =>
    generateError('Active Rport Host can not be disabled', 400, 'RPORT_HOST_ACTIVE'),
  rport_config_not_found: () => generateError('Rport config not found', 400, 'RPORT_CONFIG_ERROR'),
  deployment_client_list_empty: () =>
    generateError('Client list is empty', 400, 'DEPLOYMENT_CLIENT_LIST_EMPTY'),
  batuta_uninstall_client_list_empty: () =>
    generateError(
      'Clients connected to uninstall list is empty and not use queue',
      400,
      'BATUTA_UNINSTALL_CLIENT_LIST_EMPTY'
    ),
  batuta_uninstall_clients_disconnected_and_not_use_queue: () =>
    generateError(
      'Clients disconnected but not use queue',
      400,
      'BATUTA_UNINSTALL_CLIENTS_DISCONNECTED_AND_NOT_USE_QUEUE'
    ),
  // RPORT GROUP
  group_already_exists: () => generateError('Group already exists', 400, 'GROUP_ALREADY_EXISTS'),
  invalid_assignation: () =>
    generateError('Group type does not allow this action', 400, 'INVALID_ASSIGNATION'),
  invalid_static_group_action: () =>
    generateError('Invalid action on static group', 400, 'INVALID_STATIC_GROUP_ACTION'),
  // RPORT TAGS
  invalid_tag_action: () => generateError('Invalid action on tags', 400, 'INVALID_ACTION_TAGS'),
  maximum_tags_tenant: () =>
    generateError(`Maximum tags per tenant reached (${MAX_TAGS_TENANT})`, 400, 'MAX_TAGS_TENANT'),
  maximum_tags_host: () =>
    generateError(`Maximum tags per host reached (${MAX_TAGS_PER_HOST})`, 400, 'MAX_TAGS_HOST'),
  maximum_tag_length: () =>
    generateError(`Maximum tag length reached (${MAX_TAG_CHARS} characters)`, 400, 'MAX_TAG_CHARS'),
  // RPORT JOBS
  job_could_not_run: () =>
    generateError('The job could not run in the chosen hosts', 400, 'JOB_COULD_NOT_RUN'),

  // Automox errors
  automox_devices_not_found: () =>
    generateError('Automox devices not found', 400, 'AUTOMOX_DEVICES_ERROR'),
  automox_device_not_found: () =>
    generateError('Automox device not found', 400, 'AUTOMOX_DEVICE_ERROR'),
  // Halcyon errors
  halcyon_authentication_failed: () =>
    generateError('Halcyon accessKey is invalid', 400, 'HALCYON_AUTHENTICATION_ERROR'),
  // Generic service auth error
  service_authentication_failed: (service: string) =>
    generateMetadataError(`${service} authentication failed`, 400, 'SERVICE_AUTHENTICATION_ERROR', {
      service,
    }),

  // Documentation errors
  invalid_documentation_page: () =>
    generateError('Documentation page is invalid', 400, 'DOCUMENTATION_PAGE_FORMAT_INVALID'),

  // Compliance errors
  policy_could_not_run: () =>
    generateError('The Policy could not run in the chosen hosts', 400, 'POLICY_COULD_NOT_RUN'),
  policy_quantity_reached: () =>
    generateError('The quantity of policies has reached its limit', 400, 'POLICY_QUANTITY_REACHED'),
  failing_host_count_failed: () =>
    generateError(
      'We could not calculate the number of non compliance host',
      400,
      'DASHBOARD_METRIC_ERROR'
    ),

  // Passive Scan errors
  already_running: (name: string = 'Process') =>
    generateError(`${name} is already running`, 400, `${name.toUpperCase()}_ALREADY_RUNNING`),

  // VCISO errors
  text_streaming_failed: () =>
    generateError(
      'There was a connection error between SOAR API and VCISO API',
      503,
      'VCISO_TEXT_STREAMING_FAILED'
    ),

  // BATUTA CLOUD API
  credentials_not_found: () =>
    generateError('Batuta API Credentials are not configured', 503, 'CREDENTIALS_NOT_CONFIGURED'),

  // ENTRAID errors
  entraid_authentication_error: () =>
    generateError('Could not authenticate with EntraID', 400, 'ENTRAID_AUTHENTICATION_ERROR'),

  // Report Errors
  report_already_available: () =>
    generateError('Report already available', 400, 'REPORT_ALREADY_AVAILABLE'),
  report_cannot_be_cancelled: () =>
    generateError('Report cannot be cancelled', 400, 'REPORT_CANNOT_BE_CANCELLED'),
  report_cannot_be_deleted: () =>
    generateError('Report cannot be deleted', 400, 'REPORT_CANNOT_BE_DELETED'),
  report_cannot_be_send: () => generateError('Report cannot be send', 400, 'REPORT_CANNOT_BE_SEND'),
  report_cancelled: () =>
    generateError('Report has been cancelled', 400, 'REPORT_HAS_BEEN_CANCELLED'),

  // Not found error (404)
  not_found: (name = 'Resource') =>
    generateMetadataError(`${name} not found.`, 404, `${serializeValue(name)}_NOT_FOUND`, {
      code: 'NOT_FOUND',
      name: serializeValue(name),
    }),
  already_exists: (name = 'Resource') =>
    generateMetadataError(
      `${name} already exists.`,
      404,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_ALREADY_EXISTS`,
      {
        code: 'ALREADY_EXISTS',
        name: serializeValue(name),
      }
    ),
  temp_not_found: () => generateError('This option has already expired', 404, 'TEMP_NOT_FOUND'),
  // not enabled error (404)
  not_enabled: (name = 'Service') =>
    generateMetadataError(
      `${name} not available.`,
      404,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_NOT_AVAILABLE`,
      {
        code: 'NOT_AVAILABLE',
        name: serializeValue(name),
      }
    ),
  disabled: (name = 'Resource') =>
    generateMetadataError(
      `${name} is disabled.`,
      404,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_DISABLED`,
      {
        code: 'DISABLED',
        name: serializeValue(name),
      }
    ),
  not_valid: (name = 'Resource', explanation = '') =>
    generateMetadataError(
      `${name} not valid. ${explanation}`,
      404,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_NOT_VALID`,
      {
        code: 'NOT_VALID',
        name: serializeValue(name),
      }
    ),
  not_supported: (name = 'Resource') =>
    generateMetadataError(
      `${name} not supported.`,
      404,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_NOT_SUPPORTED`,
      {
        code: 'NOT_SUPPORTED',
        name: serializeValue(name),
      }
    ),
  not_ready: (name = 'Resource') =>
    generateMetadataError(
      `${name} is not ready.`,
      404,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_NOT_READY`,
      { code: 'NOT_READY', name: serializeValue(name) }
    ),
  environment_variable_not_set: (name = 'Resource') =>
    generateMetadataError(
      `${name} not set.`,
      500,
      `${name.trim().replace(/\s/g, '_').toUpperCase()}_NOT_SET`,
      { code: 'NOT_SET', name: serializeValue(name) }
    ),
  max_elements_exceeded: (name = 'Resource', elements = 500) =>
    generateMetadataError(
      `You can not affect more than ${elements} ${name} at a time`,
      404,
      `MAX_${name.trim().replace(/\s/g, '_').toUpperCase()}_EXCEEDED`,
      { code: 'MAX_ELEMENTS_EXCEEDED', name: serializeValue(name), elements }
    ),
  empty_list: (name = 'Resource') =>
    generateMetadataError(`${name} list is empty`, 400, 'EMPTY_LIST', {
      code: 'EMPTY_LIST',
      name: serializeValue(name),
    }),
  disposable_email: () =>
    generateError(`Disposable emails are not valid for Batuta Account`, 422, 'DISPOSABLE_EMAIL'),
  max_entities_tenant: (name = 'Resources', max = 500) =>
    generateMetadataError(
      `Maximum ${name} per tenant reached (${max})`,
      400,
      `MAX_${name.toUpperCase().replace(' ', '_')}_TENANT`,
      { code: 'MAX_ENTITIES_TENANT', name: serializeValue(name), max }
    ),
  max_length_field: (name = 'Field', max = 500) =>
    generateMetadataError(
      `Maximum ${name} length reached (${max} characters)`,
      400,
      `MAX_${name.toUpperCase()}_CHARS`,
      { code: 'MAX_LENGTH_FIELD', name: serializeValue(name), max }
    ),
  max_assignments: (name = 'Resources', target = '', max = 500) =>
    generateMetadataError(
      `You can not assign more than ${max} ${name} to a ${target} at a time`,
      400,
      `MAX_${name.toUpperCase()}_ASSIGNMENTS`,
      { code: 'MAX_ASSIGNMENTS', name: serializeValue(name), target, max }
    ),
  training_finished: () =>
    generateError(
      `This assignment has already been completed.`,
      400,
      `AWARENESS_TRAINING_ALREADY_FINISHED`
    ),

  // Invalid request errors (422)
  invalid_field_value: (field: string, value: string, validValues: string[]) => {
    return generateMetadataError(
      `Invalid value '${value}' for field '${field}'. Valid values are: ${validValues.join(', ')}`,
      422,
      'INVALID_FIELD_VALUE',
      { field, value, validValues }
    );
  },
};
