// Windows Shell Types
export type WindowsShellType = 'powershell' | 'cmd';
// Unix Shell Types
export type UnixShellType = 'bash';
// Shell Types
export type ShellType = WindowsShellType | UnixShellType;

// Shell Types list by kernel
export const ShellTypesByKernel: Record<string, ShellType[]> = {
  windows: ['powershell', 'cmd'],
  linux: ['bash'],
};

// Shell Types list
export const ShellTypes: ShellType[] = Object.values(ShellTypesByKernel).flat();
