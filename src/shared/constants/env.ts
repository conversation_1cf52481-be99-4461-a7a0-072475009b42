export const NODE_ENV = process.env.NODE_ENV;
export const PORT = process.env.PORT;

export const MONGO_URI = <string>process.env.MONGO_URI;
export const DB_CERT_REQUIRED = process.env.DB_CERT_REQUIRED;
export const DB_CERT = process.env.DB_CERT;

export const DEFAULT_USERS_CONFIG = <string>process.env.DEFAULT_USERS_CONFIG;

export const BOT_TOKEN = <string>process.env.BOT_TOKEN;
export const CHAT_ID = <string>process.env.CHAT_ID;
export const BOT_USERNAME = <string>process.env.BOT_USERNAME;

export const CLIENT_NAME = <string>process.env.CLIENT_NAME;
export const RUN_CRON_JOBS = process.env.RUN_CRON_JOBS;
export const ENABLE_MONITORING = process.env.ENABLE_MONITORING;

export const AUTOMOX_API_KEY = <string>process.env.AUTOMOX_API_KEY;
export const AUTOMOX_ORG_ID = <string>process.env.AUTOMOX_ORG_ID;
export const AUTOMOX_BASE_URL = <string>process.env.AUTOMOX_BASE_URL;
export const PENTEST_API_KEY = <string>process.env.PENTEST_API_KEY;
export const HALCYON_BASE_URL = <string>process.env.HALCYON_BASE_URL;
export const HALCYON_USERNAME = <string>process.env.HALCYON_USERNAME;
export const HALCYON_PASSWORD = <string>process.env.HALCYON_PASSWORD;
export const HALCYON_TENANT_ID = <string>process.env.HALCYON_TENANT_ID;
export const ALIENVAULT_API_KEY = <string>process.env.ALIENVAULT_API_KEY;
export const MXTOOLS_API_KEY = <string>process.env.MXTOOLS_API_KEY;
export const PWNED_API_KEY = <string>process.env.PWNED_API_KEY;
export const VIRUSTOTAL_API_KEY = <string>process.env.VIRUSTOTAL_API_KEY;
export const JIRA_PROJECT_KEY = <string>process.env.JIRA_PROJECT_KEY;
export const JIRA_PROJECT_NAME = <string>process.env.JIRA_PROJECT_NAME;
export const JIRA_HOSTNAME = <string>process.env.JIRA_HOSTNAME;
export const JIRA_TOKEN = <string>process.env.JIRA_TOKEN;
export const IPINFO_TOKEN = <string>process.env.IPINFO_TOKEN;

export const PAIRING_BASE_URL = <string>process.env.PAIRING_BASE_URL;

export const CDN_BASE_URL = <string>process.env.CDN_BASE_URL;
export const CDN_REGION = <string>process.env.CDN_REGION;
export const DIGITAL_OCEAN_ACCESS_KEY_ID = <string>process.env.DIGITAL_OCEAN_ACCESS_KEY_ID;
export const DIGITAL_OCEAN_SECRET_ACCESS_KEY = <string>process.env.DIGITAL_OCEAN_SECRET_ACCESS_KEY;
export const CDN_S3_BASE_URL = <string>process.env.CDN_S3_BASE_URL;
export const CDN_S3_BUCKET = <string>process.env.CDN_S3_BUCKET;
export const CDN_S3_REGION = <string>process.env.CDN_S3_REGION;

export const RPORT_API_KEY = <string>process.env.RPORT_API_KEY;
export const RPORT_API_PROTOCOL = <string>process.env.RPORT_API_PROTOCOL;
export const RPORT_API_PORT = <string>process.env.RPORT_API_PORT;
export const RPORT_USER = <string>process.env.RPORT_USER;
export const RPORT_DOMAIN = <string>process.env.RPORT_DOMAIN;
export const RPORT_DOMAIN_HOST = <string>process.env.RPORT_DOMAIN_HOST;
export const RPORT_AUTOINSTALL_PLATFORMS = <string>process.env.RPORT_AUTOINSTALL_PLATFORMS;
export const RPORT_AGENT_VERSION = <string>process.env.RPORT_AGENT_VERSION;
export const RPORT_MYSQL_URI = <string>process.env.RPORT_MYSQL_URI;

export const APT_BASE_URL = <string>process.env.APT_BASE_URL;
export const APT_USERNAME = <string>process.env.APT_USERNAME;
export const APT_PASSWORD = <string>process.env.APT_PASSWORD;
export const APT_CUSTOMER_ID = <string>process.env.APT_CUSTOMER_ID;

export const API_BASE_URL = <string>process.env.API_BASE_URL;
export const SOAR_ID = <string>process.env.SOAR_ID;
export const HOST_CLIENT_TOKEN = <string>process.env.HOST_CLIENT_TOKEN;

export const GATEWAY_BASE_URL = <string>process.env.GATEWAY_BASE_URL;
export const GATEWAY_CLIENT_TOKEN = <string>process.env.GATEWAY_CLIENT_TOKEN;
export const GATEWAY_AUTH_KEY = <string>process.env.GATEWAY_AUTH_KEY;
export const GATEWAY_AUTH_TOKEN = <string>process.env.GATEWAY_AUTH_TOKEN;

export const CRASH_SMTP = <string>process.env.CRASH_SMTP;
export const CRASH_PORT = Number(process.env.CRASH_PORT);
export const CRASH_USR = <string>process.env.CRASH_USR;
export const CRASH_PASS = <string>process.env.CRASH_PASS;
export const CRASH_FROM = <string>process.env.CRASH_FROM;
export const CRASH_TO = <string>process.env.CRASH_TO;

export const ONE_SIGNAL_API_KEY = <string>process.env.ONE_SIGNAL_API_KEY;
export const ONE_SIGNAL_APP_ID = <string>process.env.ONE_SIGNAL_APP_ID;

export const BATUTA_DOCS_API_URL = <string>process.env.BATUTA_DOCS_API_URL;
export const BATUTA_DOCS_API_TOKEN = <string>process.env.BATUTA_DOCS_API_TOKEN;

export const VCISO_USER = <string>process.env.VCISO_USER;
export const VCISO_PASSWORD = <string>process.env.VCISO_PASSWORD;
export const VCISO_API_URL = <string>process.env.VCISO_API_URL;

export const ELASTIC_BASE_URL = process.env.ELASTIC_BASE_URL;
export const ELASTIC_API_KEY = process.env.ELASTIC_API_KEY;
export const ELASTIC_INVENTORY_INDEX = `batuta-inventory-${SOAR_ID}`;

export const BATUTA_CLOUD_API_URL = process.env.BATUTA_CLOUD_API_URL;
export const BATUTA_CLOUD_USERNAME = process.env.BATUTA_CLOUD_USERNAME;
export const BATUTA_CLOUD_PASSWORD = process.env.BATUTA_CLOUD_PASSWORD;
export const BATUTA_CLOUD_COMPANY_ID = process.env.BATUTA_CLOUD_COMPANY_ID;

export const ENTRAID_BASE_URL = process.env.ENTRAID_BASE_URL;
export const ENTRAID_CLIENT_ID = process.env.ENTRAID_CLIENT_ID;
export const ENTRAID_CLIENT_SECRET = process.env.ENTRAID_CLIENT_SECRET;
export const ENTRAID_DIRECTORY_ID = process.env.ENTRAID_DIRECTORY_ID;

export const BATUTA_FRONTEND_BASE_URL = process.env.BATUTA_FRONTEND_BASE_URL;

export const AWARENESS_OPEN_AI_KEY = process.env.AWARENESS_OPEN_AI_KEY;
export const AWARENESS_ANSWERING_URL = process.env.AWARENESS_ANSWERING_URL;

export const KAFKA_BROKERS = <string>process.env.KAFKA_BROKERS;
export const KAFKA_AUTH_USER = <string>process.env.KAFKA_AUTH_USER;
export const KAFKA_AUTH_PASS = <string>process.env.KAFKA_AUTH_PASS;

// Vulnerability service
export const VULNERABILITY_SERVICE_URL = <string>process.env.VULNERABILITY_SERVICE_URL;
export const VULNERABILITY_SERVICE_BASE_API_KEY = <string>(
  process.env.VULNERABILITY_SERVICE_BASE_API_KEY
);
export const VULNERABILITY_SERVICE_CLIENT_ID = <string>process.env.VULNERABILITY_SERVICE_CLIENT_ID;

// Environment variables to control if specific functionalities should run or not
// Defaults to false if not assigned and running in DEV
export const ENABLE_SYNC = process.env.ENABLE_SYNC || (NODE_ENV && NODE_ENV !== 'development');
export const ENABLE_APM = process.env.ENABLE_APM || (NODE_ENV && NODE_ENV !== 'development');
