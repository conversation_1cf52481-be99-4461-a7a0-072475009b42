import _ from 'lodash';
import { database, status, up } from 'migrate-mongo';

// Importing Schemas
import { Functionality, Service, Tour } from '@shared/schemas';

import { Compliance, Playbook, Policy } from '@services/Compliance/schemas';
import { Cron } from '@services/Cron/schemas';
import {
  BatutaCommand,
  RportConfig,
  RportGroup,
  RportPlatform,
  RportPlatformTemplate,
} from '@services/Rport/schemas';
import { QueuesQueue } from '@services/Queues/schemas';
import { AptFix, AptFixTag } from '@services/APT/schemas';
import { Query } from './soar/schemas/query.schema';

import { getNextRun } from '@services/Cron/helpers/functions';
import { updateRportPlatforms } from '@services/Rport/helpers/functions';
import { getValidatedFixTags } from '@services/APT/helpers/functions';
import {
  addDefaultSectionToReports,
  deleteOrphanReports,
} from '@services/Reports/helpers/functions';

import {
  API_BASE_URL,
  CDN_BASE_URL,
  HOST_CLIENT_TOKEN,
  NODE_ENV,
  SOAR_ID,
} from '@shared/constants/env';
import { APIConfig, CommandSet, Parameters } from '@services/Rport/helpers/models';

import { Condition } from './shared/models';
import { Logger } from './shared/helpers/classes/logger.class';

import { Importance } from '@services/APT/helpers/constants/fixes';
import { updateAllProactivityScores } from '@services/LevelOfProactivity/helpers/utils';
import { setDefaultGroupsToPassiveScan } from '@services/AssetDiscovery/helpers/functions/passiveScanConfig.functions';

// Importing DB initializer file
// eslint-disable-next-line @typescript-eslint/no-require-imports
const db = require('../service.manifest.json');

/**
 * Only for service initialization
 */

interface FunctionalityManifest {
  name: string;
  internalName: string;
  webhook: boolean;
  requires_permission: boolean;
  permissions: string[];
  utility: boolean;
  tours?: TourManifest[];
  enabled?: boolean;
}

interface ServiceManifest {
  name: string;
  internalName: string;
  type: string;
  functionalities: FunctionalityManifest[];
  enabled: boolean;
}

interface CronManifest {
  name: string;
  type: 'LOOP' | 'MONTHLY' | 'WEEKLY';
  timelapse: number;
  monthDay: 'START' | 'END';
  weekDay: number;
  enabled: boolean;
}

interface TourManifest {
  internalName: string;
  permission?: string;
  enabled?: boolean;
}

interface PlatformTemplateManifest {
  service: string;
  enabled: boolean;
  deleted: boolean;
  commands: CommandSet;
  parameters: Parameters;
  apiConfig: APIConfig;
}

interface ComplianceManifest {
  platform: string;
  command: string;
  parameters: { [key: string]: any };
}

interface BatutaManifest {
  commands: CommandSet;
  parameters: Parameters;
}

interface QueueManifest {
  name: string;
  maximumConcurrency: number;
  tasks: string[];
}

interface Query {
  field: string;
  value: string;
  type: string;
  exclude: boolean;
}

interface QueryManifest {
  identifier: string;
  name: string;
  queries: Query[];
}

interface GroupManifest {
  name: string;
  description: string;
  type: string;
  rules: { [key: string]: Condition[] };
  protected: boolean;
}

interface CompliancePlaybookManifest {
  name: string;
  csvUrl: string;
  protected: boolean;
}

interface CompliancePolicyManifest {
  name: string;
  description: string;
  group: string;
  playbook: string;
  execution_mode: 'CRON' | 'MANUAL';
  life_cycle: {
    start_date: number;
    end_date: number;
  };
  schedule: {
    month: number[];
    week_day: number[];
    runtime_window: {
      start_time: string;
      end_time: string;
    };
  };
  operating_mode: {
    update_results: string;
    results_expiration: string;
  };
  protected: boolean;
}

interface FixTagsManifest {
  name: string;
}

interface FixManifest {
  name: string;
  description: string;
  scripts: {
    test: string;
    fix: string;
    rollback: string;
  };
  importance: Importance;
  shellType: string;
  tags: string[];
}

interface RootObject {
  services: ServiceManifest[];
  crontabs: CronManifest[];
  tours: TourManifest[];
  platform_templates: PlatformTemplateManifest[];
  compliance: ComplianceManifest[];
  batuta: BatutaManifest;
  queues: QueueManifest[];
  queries: QueryManifest[];
  groups: GroupManifest[];
  compliancePlaybooks: CompliancePlaybookManifest[];
  compliancePolicies: CompliancePolicyManifest[];
  fixTags: FixTagsManifest[];
  fixes: FixManifest[];
}

export const initDb = async () => {
  const {
    compliance,
    batuta,
    services,
    crontabs,
    tours,
    platform_templates,
    queues,
    queries,
    groups,
    compliancePlaybooks,
    compliancePolicies,
    fixTags,
    fixes,
  }: RootObject = db;

  // Reset tours
  await Tour.deleteMany();

  // Delete not declared services
  await Service.deleteMany({
    internalName: { $nin: services.map((s) => s.internalName) },
  });

  // Remove functionalities that are not defined in the services
  await Functionality.deleteMany({
    internalName: {
      $nin: services.flatMap((s) =>
        s.functionalities.map((f) => `${s.internalName}.${f.internalName}`)
      ),
    },
  });

  await Promise.all([
    RportConfig.findOneAndUpdate({ running: true }, { running: false }), // Make sure the RportConfig is not stuck in running state
    ...services.map(async (service) => {
      // Check if the service exists
      const existingService = await Service.findOne({ internalName: service.internalName });

      const _service = await Service.findOneAndUpdate(
        { internalName: service.internalName },
        {
          name: service.name,
          internalName: service.internalName,
          type: service.type,
          enabled: existingService ? existingService.enabled : service.enabled || false,
        },
        { upsert: true, new: true }
      );

      await Promise.all([
        ...service.functionalities.map(async (functionality) => {
          const encodedFunctionalityName = `${service.internalName}.${functionality.internalName}`;
          await Functionality.findOneAndUpdate(
            { internalName: encodedFunctionalityName },
            {
              ...functionality,
              permissions: functionality.permissions?.map(
                (permission) => `${permission}:${encodedFunctionalityName}`
              ),
              internalName: encodedFunctionalityName,
            },
            { upsert: true }
          );
          if (functionality.tours) {
            await Promise.all(
              functionality.tours.map(async (tour) => {
                const tourUpdate: any = {
                  internalName: tour.internalName,
                };
                if (tour.permission) {
                  tourUpdate['$addToSet'] = {
                    permissions: `${tour.permission}:${encodedFunctionalityName}`,
                  };
                }
                await Tour.findOneAndUpdate({ internalName: tour.internalName }, tourUpdate, {
                  upsert: true,
                });
              })
            );
          }
        }),
        ...platform_templates
          .filter((t) => t.service === service.internalName)
          .map(async (template) => {
            const existingTemplate = await RportPlatformTemplate.findOne({
              service: _service._id,
            });
            if (!existingTemplate) {
              // Creating template if it does not exists
              await RportPlatformTemplate.create({
                service: _service._id,
                commands: template.commands,
                parameters: template.parameters,
                apiConfig: template.apiConfig,
              });
              // Returning after creation
              return;
            }
            // if there are any differences between current and new, alert user and request the user to submit changes
            await updateRportPlatforms(existingTemplate, template);
            await RportPlatformTemplate.findByIdAndUpdate(
              existingTemplate._id,
              {
                service: _service._id,
                commands: template.commands,
                apiConfig: template.apiConfig,
                parameters: template.parameters,
              },
              { upsert: true }
            );
          }),
      ]);
    }),
    ...tours.map(async (tour) => {
      const tourUpdate: any = {
        internalName: tour.internalName,
      };
      if (tour.permission) {
        tourUpdate['$addToSet'] = { permissions: tour.permission };
      }
      await Tour.findOneAndUpdate(
        { internalName: tour.internalName, enabled: tour.enabled ?? true },
        tourUpdate,
        { upsert: true }
      );
    }),
    ...crontabs.map(async (cron) => {
      // auto completar registro de crontabs
      const existingCron = await Cron.findOne({ name: cron.name });
      const nextRun = existingCron
        ? existingCron.nextRun
        : await getNextRun(cron.type ?? 'LOOP', cron.timelapse, cron.monthDay, cron.weekDay);
      await Cron.findOneAndUpdate(
        { name: cron.name },
        {
          name: cron.name,
          type: cron.type ?? 'LOOP',
          nextRun: nextRun,
          timelapse: cron.timelapse,
          monthDay: cron.monthDay,
          weekDay: cron.weekDay,
          enabled: cron.enabled,
        },
        { upsert: true }
      );
    }),
    ...compliance.map(async (complianceItem) => {
      // Always skip in development to prevent injecting local vars into staging DB
      // Comment out if you absolutely necessary
      if (NODE_ENV === 'development') return;
      await Compliance.findOneAndUpdate(
        {
          platform: complianceItem.platform,
        },
        {
          platform: complianceItem.platform,
          command: complianceItem.command,
          parameters: {
            apiUrl: API_BASE_URL + complianceItem.parameters.apiUrl,
            cdnBaseURL: CDN_BASE_URL + complianceItem.parameters.cdnBaseURL,
            clientToken: HOST_CLIENT_TOKEN,
            soarId: SOAR_ID,
          },
        },
        { upsert: true }
      );
    }),
    await BatutaCommand.findOneAndUpdate(
      {
        name: 'batuta-uninstall',
      },
      {
        commands: batuta.commands,
        parameters: batuta.parameters,
      },
      { upsert: true }
    ),
    ...queues.map(async (queue: QueueManifest) => {
      await QueuesQueue.findOneAndUpdate(
        {
          name: queue.name,
        },
        {
          name: queue.name,
          maximumConcurrency: queue.maximumConcurrency,
          tasks: queue.tasks,
        },
        { upsert: true }
      );
    }),
    ...queries.map(async (query: QueryManifest) => {
      await Query.findOneAndUpdate(
        {
          name: query.name,
          identifier: query.identifier,
        },
        {
          name: query.name,
          identifier: query.identifier,
          provided: true,
          queries: query.queries,
        },
        { upsert: true }
      );
    }),
    ...groups.map(async (group: GroupManifest) => {
      const existingGroup = await RportGroup.findOne({ name: group.name });

      if (existingGroup && !_.isEqual(existingGroup.rules, group.rules)) {
        const newName = `${existingGroup.name}_previous`;
        await RportGroup.findOneAndUpdate(
          { name: group.name },
          { name: newName },
          { bypassProtection: true }
        );
      }

      await RportGroup.findOneAndUpdate(
        { name: group.name },
        {
          name: group.name,
          description: group.description,
          type: group.type,
          rules: group.rules,
          protected: group.protected,
          createdBy: 'SYSTEM',
          updatedBy: 'SYSTEM',
        },
        { upsert: true, bypassProtection: true }
      );
    }),
    ...compliancePlaybooks.map(async (playbook) => {
      await Playbook.findOneAndUpdate(
        { name: playbook.name },
        {
          name: playbook.name,
          csvUrl: playbook.csvUrl,
          protected: playbook.protected,
        },
        { upsert: true, bypassProtection: true }
      );
    }),
    ...fixTags.map(async (tag) => {
      // Creating the tag
      await AptFixTag.findOneAndUpdate(
        { name: tag.name },
        {
          name: tag.name,
        },
        { upsert: true, bypassProtection: true }
      );
    }),
  ]);

  // Second Step - Dependent Entities on the first step
  await Promise.all([
    ...compliancePolicies.map(async (policy) => {
      // Searching for the group id
      const group = await RportGroup.findOne({ name: policy.group });
      if (!group) {
        Logger.error(`Group ${policy.group} not found for policy ${policy.name}`);
        return;
      }

      // Searching for the playbook id
      const playbook = await Playbook.findOne({ name: policy.playbook });
      if (!playbook) {
        Logger.error(`Playbook ${policy.playbook} not found for policy ${policy.name}`);
        return;
      }

      // Creating the policy
      await Policy.findOneAndUpdate(
        { name: policy.name },
        {
          name: policy.name,
          description: policy.description,
          group: group._id,
          playbook: playbook._id,
          execution_mode: policy.execution_mode,
          life_cycle: policy.life_cycle,
          schedule: policy.schedule,
          operating_mode: policy.operating_mode,
          protected: policy.protected,
          deleted: false,
        },
        { upsert: true, bypassProtection: true }
      );
    }),
    ...fixes.map(async (fix) => {
      // Creating the fixes
      await AptFix.findOneAndUpdate(
        { name: fix.name },
        {
          name: fix.name,
          description: fix.description,
          scripts: fix.scripts,
          shellType: fix.shellType,
          importance: fix.importance,
          tags: await getValidatedFixTags(fix.tags),
          updatedBy: 'SYSTEM',
          createdBy: 'SYSTEM',
        },
        { upsert: true, bypassProtection: true }
      );
    }),
  ]);

  await updateAllProactivityScores();

  // Get templates that point to inexistent services
  const _services = await Service.find({}).select('_id');

  // Get platforms that are deleted or point to inexistent templates
  const _templates = await RportPlatformTemplate.find({}).select('_id');

  // Removing Documents
  await Promise.all([
    // Remove any crons not defined in the manifest
    Cron.deleteMany({ name: { $nin: crontabs.map((c) => c.name) } }),
    RportPlatformTemplate.deleteMany({ service: { $nin: _services.map((s) => s._id) } }),
    RportPlatform.deleteMany({
      $or: [{ deleted: true }, { template: { $ne: null, $nin: _templates.map((t) => t._id) } }],
    }),
  ]);

  // Migrations
  const { db: mongodb, client } = await database.connect();

  const migrationStatus = await status(mongodb);
  migrationStatus.forEach(({ fileName, appliedAt }) => {
    Logger.info(`Status ${fileName} applied at ${appliedAt}`);
  });

  const migrated = await up(mongodb, client);
  migrated.forEach((fileName: string) => Logger.info(`Migrated: ${fileName}`));
  await client.close();

  // Remove Orphan Reports from CDN
  await deleteOrphanReports();

  // Add default sections to reports
  await addDefaultSectionToReports();

  // Set Default groups to passive scan if not set
  await setDefaultGroupsToPassiveScan();
};
