import _ from 'lodash';
import { RequestHand<PERSON> } from 'express';

import { User, UserDocument } from '@shared/schemas';

import { GetAllQuery } from '@shared/types';

import { NewUserRequestModel, UserUpdateRequestModel } from '@root/shared/models/user.model';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';
import { Gateway } from '@shared/helpers/classes/gateway.class';

import { getDefaultUserPermissions, isAllowToOperate } from '../helpers/functions/user.functions';
import { startSession } from 'mongoose';
import { isSupportedLanguage, TLanguage } from '@root/shared/types/languages.types';

export const userFilter: RequestHandler = catchAsync(async (req, res) => {
  // Check if user can access the filters
  const skippedFilter = [
    'language',
    'isModerator',
    'isManager',
    'isAdmin',
    'isUserManager',
    'isFrontendUser',
    'permissions',
    'accounts',
  ];
  const [filter, fields] = await User.createFilter(undefined, skippedFilter);

  res.status(200).json({ filter, fields });
});

export const getAllUsers: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery] = User.parseFilter(filter, sort);

  if (!req.user?.isAdmin) {
    // Adding filter for not admin users
    query.$and.push({ isAdmin: false });
  }

  const [filteredResults, filteredResultsCount] = await Promise.all([
    User.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0),
    User.countDocuments(query),
  ]);

  res.status(200).json({
    meta: {
      total: filteredResultsCount,
      resources: filteredResults.length,
      offset: +(offset ?? 0),
    },
    data: filteredResults,
  });
});

export const getMyself: RequestHandler = catchAsync(async (req, res) => {
  // Validate if the user id is present
  const userId = req.user?._id;
  if (!userId) throw errors.not_valid('User ID');

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) throw errors.not_found('User');
  if (!user.enabled) throw errors.not_enabled('User');

  // Return user
  res.status(200).json(user);
});

export const getUser: RequestHandler = catchAsync(async (req, res) => {
  const userId = req.params.id;

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) throw errors.not_found('User');

  res.status(200).json(user);
});

export const createUser: RequestHandler = catchAsync(async (req, res) => {
  // Getting body
  const newUser = req.body as NewUserRequestModel;

  if (!req.user) throw errors.not_allowed();

  // Validate if requester can create the user
  if (!isAllowToOperate(req.user, newUser)) throw errors.not_allowed();

  // Check if the mail is already in use
  const user = await User.findOne({ email: newUser.email });
  if (user) throw errors.already_exists('User');

  // Get default permissions for the user role
  const defaultPermissions = await getDefaultUserPermissions(newUser);

  let createdUser: UserDocument;

  const session = await startSession();
  session.startTransaction();
  try {
    // Create user
    createdUser = await User.create({ ...newUser, permissions: defaultPermissions });
    // Send request to the gateway
    await Gateway.grantTenantAccess(newUser.email, newUser.isFrontendUser, newUser.isAdmin);
    // Commit mongo db transaction
    await session.commitTransaction();
  } catch (error) {
    // Rollback mongo db transaction
    await session.abortTransaction();
    throw error;
  }

  // Return created user
  res.status(201).json(createdUser);
});

export const updateMyself = catchAsync(async (req, res) => {
  // Validate if the user id is present
  const userId = req.user?._id;
  if (!userId) throw errors.not_valid('User ID');

  // Check if user exists
  const user = await User.findById(userId);
  if (!user) throw errors.not_found('User');
  if (!user.enabled) throw errors.not_enabled('User');

  const { language } = req.body as { language?: TLanguage };
  if (language) {
    if (!isSupportedLanguage(language)) throw errors.not_valid('Language');
    // Update user
    const updatedUser = await User.findByIdAndUpdate(userId, { language }, { new: true });
    // Update user on gateway
    await Gateway.updateUserConfiguration(user.email, { language });
    // Return updated user
    return res.status(200).json(updatedUser);
  }

  // Return user
  res.status(200).json(user);
});

export const updateUser = catchAsync(async (req, res) => {
  // Getting body
  let newUser = req.body as UserUpdateRequestModel;

  if (!req.user) throw errors.not_allowed();

  // Validate that the user can not update itself
  if (req.user._id.toString() === req.params.id) throw errors.not_allowed();

  // Validate if requester can create the user
  if (!isAllowToOperate(req.user, newUser)) throw errors.not_allowed();

  // Get current user
  const user = await User.findById(req.params.id);
  if (!user) throw errors.not_found('User');
  if (!user.enabled) throw errors.not_enabled('User');

  newUser = _.pick(newUser, [
    'isAdmin',
    'isManager',
    'isUserManager',
    'isModerator',
    'isFrontendUser',
    'permissions',
  ]);

  let updatedUser: UserDocument | null;

  const session = await startSession();
  session.startTransaction();
  try {
    // Update fields that exists
    updatedUser = await User.findByIdAndUpdate(req.params.id, newUser, { new: true });
    // Update user on gateway
    if (newUser.isAdmin !== undefined) {
      await Gateway.updateUserConfiguration(user.email, { isAdmin: newUser.isAdmin });
    }
    // Commit mongo db transaction
    await session.commitTransaction();
  } catch (error) {
    // Rollback mongo db transaction
    await session.abortTransaction();
    throw error;
  }

  // Check if user exists
  res.status(200).json(updatedUser);
});

export const switchUserStatus = catchAsync(async (req, res) => {
  const userId = req.params.id;

  if (!req.user) throw errors.not_allowed();

  // Validate that the user can not update itself
  if (req.user._id.toString() === req.params.id) throw errors.not_allowed();

  let updatedUser = await User.findById(userId);
  if (!updatedUser) throw errors.not_found('User');

  // Validate if requester can create the user
  if (!isAllowToOperate(req.user, updatedUser)) throw errors.not_allowed();

  // Get current user
  const user = await User.findById(userId);
  if (!user) throw errors.not_found('User');

  // Update user status
  updatedUser = await User.findByIdAndUpdate(
    userId,
    { enabled: !updatedUser.enabled },
    { new: true }
  );
  if (!updatedUser) throw errors.not_found('User');

  // Update user on gatewat
  await Gateway.setTenantAccessStatus(updatedUser.email, updatedUser.enabled);

  // Return updated user
  res.status(200).json(updatedUser);
});

export const removeUser: RequestHandler = catchAsync(async (req, res) => {
  const userId = req.params.id;

  if (!req.user) throw errors.not_allowed();

  // Validate that the user can not update itself
  if (req.user._id.toString() === req.params.id) throw errors.not_allowed();

  const user = await User.findById(userId);
  if (!user) throw errors.not_found('User');
  if (!user.enabled) throw errors.not_enabled('User');

  // Validate if requester can create the user
  if (!isAllowToOperate(req.user, user)) throw errors.not_allowed();

  // Send notification to gateway to check if the user must be removed
  await Gateway.removeTenantAccess(user.email);

  // Remove user
  await User.findByIdAndDelete(userId);

  res.status(204).json({});
});
