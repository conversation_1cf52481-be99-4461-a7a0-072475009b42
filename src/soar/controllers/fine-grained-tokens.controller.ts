import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';

import { errors } from '@shared/utils/app-errors';
import catchAsync from '@shared/utils/catch-async';
import { randomString, lowerChars, numbersChars } from '@shared/utils/strings';
import { FineGrainedToken, FineGrainedTokenDocument } from '../schemas/fineGrainedTokens.schema';
import { Gateway } from '@root/shared/helpers/classes/gateway.class';
import { ServiceResponse } from '@root/shared/models/service-response';
import { UserDocument } from '@root/shared/schemas';
import { isAllowToOperate } from '../helpers/functions/user.functions';

const userHasAgencyOverToken = async (
  user: UserDocument,
  tokenId: string,
  cachedToken?: FineGrainedTokenDocument
) => {
  let token = cachedToken;
  if (token === undefined) {
    token = await FineGrainedToken.findOne({
      _id: tokenId,
      deleted: false,
    });
  }
  if (!token) return false;
  const tokenWithUser = await token.populate<{ user: UserDocument }>('user');
  return isAllowToOperate(user, tokenWithUser.user);
};

// Recursive function to generate a unique prefix among all API tokens
const generateUniquePrefix = async () => {
  let prefix = randomString({ chars: `${lowerChars}${numbersChars}`, length: 10 });
  const count = await FineGrainedToken.countDocuments({ prefix });
  if (count > 0) {
    prefix = await generateUniquePrefix();
  }

  return prefix;
};

export const getAllTokens: RequestHandler = catchAsync(async (req, res) => {
  if (req.user === undefined || req.user === null) throw errors.not_allowed();

  const tokens = (await FineGrainedToken.find({ deleted: false }).populate('user', '+name')).filter(
    (token) => userHasAgencyOverToken(req.user!.id, token.id)
  );

  return ServiceResponse.get(tokens).send(res);
});

export const getTokenById: RequestHandler = catchAsync(async (req, res, next) => {
  const token = await FineGrainedToken.findOne({
    _id: req.params.id,
    deleted: false,
  }).populate('user');

  if (token === null || !userHasAgencyOverToken(req.user!.id, token.id)) throw errors.not_found();

  return ServiceResponse.get(token).send(res);
});

export const createToken: RequestHandler = catchAsync(async (req, res) => {
  // Make sure it has a unique token
  const { prefix, token } = await Gateway.createFineGrainedToken(
    req.user!.email,
    req.body.expiresAt
  );
  await FineGrainedToken.create({
    ...req.body,
    prefix,
    user: req.user,
  });

  return ServiceResponse.post({
    token: `${prefix}.${token}`,
  }).send(res);
});

export const updateToken: RequestHandler = catchAsync(async (req, res) => {
  // Prevent modifying key fields
  delete req.body.prefix;
  delete req.body.token;
  delete req.body.soar;
  delete req.body.user;
  delete req.body.lastUsedAt;
  if (req.body.deleted === false) {
    delete req.body.deleted;
  }
  const fineGrainedToken = await FineGrainedToken.findById(req.params.id);
  if (fineGrainedToken === null) throw errors.not_found('Token');
  if (!userHasAgencyOverToken(req.user!.id, fineGrainedToken.id)) throw errors.not_allowed();

  // In this case, fineGrainedToken is the document as it was BEFORE update was applied
  const updatedToken = await fineGrainedToken.updateOne(req.body, {
    runValidators: true,
    new: true,
  });

  return ServiceResponse.patch(updatedToken).send(res);
});

export const enableToken: RequestHandler = catchAsync(async (req, res, next) => {
  req.body.enabled = true;
  Gateway.updateFineGrainedToken(req.params.prefix, { enabled: true });
  return updateToken(req, res, next);
});

export const disableToken: RequestHandler = catchAsync(async (req, res, next) => {
  req.body.enabled = false;
  Gateway.updateFineGrainedToken(req.params.prefix, { enabled: false });
  return updateToken(req, res, next);
});

export const deleteToken: RequestHandler = catchAsync(async (req, res, next) => {
  req.body.deleted = true;
  Gateway.updateFineGrainedToken(req.params.prefix, { enabled: false, deleted: true });
  return updateToken(req, res, next);
});

export const updateTokenPermissions: (operation: '$push' | '$pull') => RequestHandler = (
  operation
) =>
  catchAsync(async (req, res) => {
    const { permissions } = req.body as { permissions: string[] };

    const fineGrainedToken = await FineGrainedToken.findById(req.params.id);
    if (fineGrainedToken === null || !userHasAgencyOverToken(req.user!.id, fineGrainedToken.id))
      throw errors.not_found();

    // In this case, fineGrainedToken is the document as it was BEFORE update was applied
    const updatedToken = await fineGrainedToken.updateOne(
      {
        permissions: {
          [operation]: permissions,
        },
      },
      {
        runValidators: true,
        new: true,
      }
    );

    return ServiceResponse.patch(updatedToken).send(res);
  });
