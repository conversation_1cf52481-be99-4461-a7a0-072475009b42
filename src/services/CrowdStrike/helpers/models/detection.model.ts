import { Types } from 'mongoose';

export type Severity = 'Critical' | 'High' | 'Medium' | 'Low' | 'Informational';

export interface DetectionDataModel {
  // Core detection fields
  action_taken: string;
  command_line: string;
  detection_id: string;
  file_path: string;
  md5_hash: string;
  objective: string;
  severity: Severity;
  sha256_hash: string;
  tactic: string;
  technique: string;
  url: string;
  user_name: string;

  // System/asset information
  hostname: string;
  local_ip: string;
  os_version: string;
  sensor_id: string;
  tags: any;
}

export interface DetectionMetaModel {
  event_reference_url: string;
  timestamp: string;
  trigger_name: string;
  trigger_category: string;
  workflow_id: string;
}

export interface DetectionModel {
  data: DetectionDataModel;
  meta: DetectionMetaModel;
  detectionId: string;
  console: Types.ObjectId;
}

export interface CrowdstrikePayloadModel extends DetectionModel {
  type: crowdstrikeBody;
}

type crowdstrikeBody = 'new' | 'update' | 'policy';
