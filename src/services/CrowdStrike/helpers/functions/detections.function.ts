import { Types } from 'mongoose';

import { Detection } from '../../schemas';

import { DetectionDataModel, CrowdstrikePayloadModel, DetectionMetaModel } from '../models';
import { SeverityIcons } from '../constants';
import { errors } from '@shared/utils/app-errors';
import { CLIENT_NAME } from '@shared/constants/env';

const parseDetection = (detectionData: DetectionDataModel) => {
  return JSON.parse(
    JSON.stringify(detectionData)
      // Core detection fields
      .replace(/investigatable_investigatable_id/g, 'detection_id')
      .replace(/investigatable_severity/g, 'severity')
      .replace(/investigatable_status/g, 'status')
      .replace(/investigatable_product_epp_behavior_tactic_name/g, 'tactic')
      .replace(/investigatable_product_epp_behavior_technique_name/g, 'technique')
      .replace(/investigatable_product_epp_behavior_pattern_disposition_id/g, 'action_taken')
      .replace(/investigatable_product_epp_behavior_objective/g, 'objective')
      .replace(/investigatable_product_epp_process_md5/g, 'md5_hash')
      .replace(/investigatable_product_epp_process_sha256/g, 'sha256_hash')
      .replace(/investigatable_product_epp_process_command_line/g, 'command_line')
      .replace(/investigatable_product_epp_process_file_path/g, 'file_path')
      .replace(/investigatable_product_epp_process_user_name/g, 'user_name')
      .replace(/investigatable_product_epp_url/g, 'url')

      // System/asset information
      .replace(/investigatable_product_epp_sensor_hostname/g, 'hostname')
      .replace(/investigatable_product_epp_sensor_local_ip/g, 'local_ip')
      .replace(/investigatable_product_epp_sensor_os_version/g, 'os_version')
      .replace(/investigatable_product_epp_sensor_sensor_id/g, 'sensor_id')
      .replace(/investigatable_product_epp_sensor_tags/g, 'tags')

      // Cleanup remaining prefixes
      .replace(/investigatable_product_epp_behavior_/g, '')
      .replace(/investigatable_product_epp_sensor_/g, '')
      .replace(/investigatable_product_epp_process_/g, '')
      .replace(/investigatable_product_epp_/g, '')
      .replace(/detections_/g, '')
      .replace(/devices_/g, '')
      .replace(/workflow_name/g, 'workflowname')
  ) as DetectionDataModel;
};

export const isValidDetection = async (
  detection: { [key: string]: any },
  consoleId: Types.ObjectId
): Promise<CrowdstrikePayloadModel> => {
  if (!detection) {
    throw errors.detection_not_valid();
  }

  if (!detection.meta) {
    throw errors.detection_not_valid('Meta not found');
  }

  if (!detection.data) {
    throw errors.detection_not_valid('Data not found');
  }

  const detectionMeta = detection.meta;
  if (
    !(
      'event_reference_url' in detectionMeta &&
      'timestamp' in detectionMeta &&
      'trigger_name' in detectionMeta &&
      'trigger_category' in detectionMeta &&
      'workflow_id' in detectionMeta
    )
  ) {
    throw errors.detection_not_valid(`Meta not valid: ${JSON.stringify(detectionMeta, null, 2)}`);
  }

  if (detectionMeta.trigger_category !== 'Investigatable/EPP') {
    throw errors.detection_not_valid(
      `Trigger category not valid: ${detectionMeta.trigger_category}`
    );
  }

  if (detectionMeta.trigger_name !== 'Detection') {
    throw errors.detection_not_valid(`Trigger name not valid: ${detectionMeta.trigger_name}`);
  }

  const detectionParsed = {
    data: detection.data,
    meta: detectionMeta,
  };

  let filteredDetection: CrowdstrikePayloadModel;
  if (detectionParsed.data.investigatable_status === 'New') {
    filteredDetection = await newDetectionHandler(detectionParsed, consoleId);
  } else {
    filteredDetection = await updateDetectionHandler(detectionParsed, consoleId);
  }
  return filteredDetection;
};

const newDetectionHandler = async (
  detectionParsed: { data: DetectionDataModel; meta: DetectionMetaModel },
  consoleId: Types.ObjectId
) => {
  const preDetectionData = detectionParsed.data;
  const detectionData: DetectionDataModel = parseDetection(preDetectionData);
  const detectionId = detectionData.detection_id;

  // se busca esa detection en las almacenadas a ver si ya fue enviada
  const existingDetection = await Detection.findOne({
    detectionId: detectionId,
  });

  if (existingDetection) {
    throw errors.detection_already_sent();
  }

  await Detection.create({
    data: detectionData,
    meta: detectionParsed.meta,
    detectionId: detectionId,
    console: consoleId,
  });

  // agregar todo al payload de la detección
  const filteredDetection: CrowdstrikePayloadModel = {
    data: detectionData,
    meta: detectionParsed.meta,
    detectionId: detectionId,
    console: consoleId,
    type: 'new',
  };

  return filteredDetection;
};

const updateDetectionHandler = async (
  detectionParsed: { data: DetectionDataModel; meta: DetectionMetaModel },
  consoleId: Types.ObjectId
) => {
  // parseo el body
  const detectionId = detectionParsed.data.detection_id;
  const preDetectionData = detectionParsed.data;
  const detectionData: DetectionDataModel = parseDetection(preDetectionData);
  // se busca esa detection en las almacenadas a ver si ya fue enviada
  const existingDetection = await Detection.findOne({
    detectionId: detectionId,
  });

  if (!existingDetection) {
    throw errors.not_found('Detection');
  }

  const filteredDetection: CrowdstrikePayloadModel = {
    data: detectionData,
    meta: detectionParsed.meta,
    detectionId: detectionId,
    console: consoleId,
    type: 'update',
  };

  return filteredDetection;
};

// const policiesHandler = async (consoleId: Types.ObjectId) => {};

export const formatTelegramMessage = (
  consoleName: string,
  detection: CrowdstrikePayloadModel,
  needAutoContainmentAlert: boolean,
  containmentTime?: number
) => {
  const severity = detection.data.severity;
  const severityIcon = SeverityIcons[severity];
  let detectionMessage = `[Detección entrante] ${CLIENT_NAME}:

Información general:

Consola: ${consoleName}
Severidad: ${severity} ${severityIcon}
Hostname: ${detection.data.hostname}
OS: ${detection.data.os_version}
IP local: ${detection.data.local_ip}
Tags: ${detection.data.tags}

Detalle:

Objetivo: ${detection.data.objective}
Táctica: ${detection.data.tactic}
Técnica: ${detection.data.technique}
Acción tomada: ${detection.data.action_taken}
Usuario: ${detection.data.user_name}
Dirección de archivo: ${detection.data.file_path}
Comando: ${detection.data.command_line}
SHA256: ${detection.data.sha256_hash}
MD5: ${detection.data.md5_hash}

Información completa: ${detection.data.url}`;

  if (needAutoContainmentAlert) {
    detectionMessage += `

############### ATENCIÓN ###############
El host ${detection.data.hostname} será "contenido" automáticamente de la red en ${
      (containmentTime ?? 3600) / 60
    } minutos.`;
  }

  return detectionMessage;
};

export const formatAppMessage = (consoleName: string, detection: CrowdstrikePayloadModel) => {
  return `Nueva detección para consola ${consoleName} de severidad ${detection.data.severity} en el host: ${detection.data.hostname}`;
};
