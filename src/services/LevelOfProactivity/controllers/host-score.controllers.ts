import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { PipelineStage } from 'mongoose';

import { GetAllQuery } from '@shared/types';
import { ProactivityHostScore } from '../schemas';
import { RportClient } from '@services/Rport/schemas';
import { ServiceResponse } from '@shared/models/service-response';

import { exportFileOf } from '@services/Export/controllers';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';

import { Qualification } from '@services/Reports/helpers/types/proactivity.types';
import {
  getGlobalScore,
  getHostScoreDistribution,
  getSummaryScoreByArea,
  updateAllProactivityScores,
  updateProactivityScores,
} from '../helpers/utils';

type Score = {
  percentage: number;
  qualification: Qualification;
  details: Record<string, any>;
};

type HostScoreAggregationResult = {
  _id: string;
  host: {
    _id: string;
    name: string;
    id: string;
  };
  globalScore: Score;
  scores: {
    platform: Score;
    compliance: Score;
    osUpdates: Score;
  };
  deleted: boolean;
  enabled: boolean;
  protected: boolean;
  createdAt: string;
  updatedAt: Date;
};

const _getAllHostScore = async (
  filter: string = '',
  limit: number = 100,
  offset: number = 0,
  sort: string = ''
) => {
  const [query, sortQuery] = ProactivityHostScore.parseFilter(filter, sort);

  const pipeline: PipelineStage[] = [
    {
      // Avoid operating on disabled or deleted docs
      $match: {
        enabled: true,
        deleted: false,
      },
    },
    {
      // Join the rportclients collection to filter out disabled or deleted hosts
      $lookup: {
        from: 'rportclients',
        localField: 'host',
        foreignField: '_id',
        as: 'host',
      },
    },
    {
      // Filter out disabled or deleted hosts and apply user filters
      // Nice bonus is this allows the user to filter using host properties
      $match: {
        ...query,
        'host.enabled': true,
        'host.deleted': false,
      },
    },
  ];

  const [filteredResults, filteredResultsCount] = await Promise.all([
    ProactivityHostScore.aggregate<HostScoreAggregationResult>([
      ...pipeline,
      {
        // Unwind here since it's not needed for count.
        // lookup returns an array of 1 object, this is equivalent to a first call
        $unwind: {
          path: '$host',
          includeArrayIndex: 'string',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        // Remove unwanted host properties from return object
        $project: {
          _id: 1,
          host: {
            _id: '$host._id',
            name: '$host.name',
            id: '$host._id',
          },
          globalScore: 1,
          scores: 1,
          deleted: 1,
          enabled: 1,
          protected: 1,
          createdAt: 1,
          updatedAt: 1,
        },
      },
      // Sort by user preference or host name
      { $sort: sortQuery || { 'host.name': 1 } },
      // Skip first
      { $skip: offset },
      // Limit second
      { $limit: limit },
    ]),
    ProactivityHostScore.aggregate<{ total: number }>([...pipeline, { $count: 'total' }]),
  ]);

  return { data: filteredResults, count: filteredResultsCount?.[0]?.total || 0 };
};

export const getAllHostScore: RequestHandler = catchAsync(async (req, res) => {
  const { limit = 100, offset = 0, filter = '', sort = '' } = req.query as GetAllQuery;

  const { data, count } = await _getAllHostScore(filter, limit, offset, sort);

  return ServiceResponse.get(data, count, +(offset ?? 0)).send(res);
});

export const exportHostScore: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, _getAllHostScore, ({ data }) => data);
});

export const getProactivityHostScoreByBatutaId: RequestHandler = catchAsync(async (req, res) => {
  // Getting client id from request params
  const { id: clientId } = req.params;
  if (!clientId) throw errors.not_found('Client Batuta Id');

  // Get Host from DB
  const client = await RportClient.findById(clientId);

  // Check if host is enabled and not deleted
  if (!client) throw errors.not_found('Client');
  if (!client.enabled) throw errors.not_enabled('Client');

  // Get ProactivityHostScore from DB
  const hostScore = await ProactivityHostScore.find({ host: client._id });

  // Return the data
  return ServiceResponse.get(hostScore).send(res);
});

export const getProactivityHostScoreByClient: RequestHandler = catchAsync(async (req, res) => {
  // Getting client id from request params
  const { id: clientId } = req.params;
  if (!clientId) throw errors.not_found('Client Id');

  // Get Host from DB
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });

  // Check if host is enabled and not deleted
  if (!client) throw errors.not_found('Client');
  if (!client.enabled) throw errors.not_enabled('Client');

  // Get ProactivityHostScore from DB
  const hostScore = await ProactivityHostScore.find({ host: client._id });

  // Return the data
  return ServiceResponse.get(hostScore).send(res);
});

export const getProactivityHostScoreById: RequestHandler = catchAsync(async (req, res) => {
  // Getting client id from request params
  const { id: scoreId } = req.params;
  if (!scoreId) throw errors.not_found('Score Id');

  const hostScore = await ProactivityHostScore.find({ _id: scoreId });

  // Return the data
  return ServiceResponse.get(hostScore).send(res);
});

export const getProactivityHostScoreFilters: RequestHandler = catchAsync(async (_, res) => {
  const extraFilters = {};
  const skippedFilter: string[] = [];

  const [filter, fields] = await ProactivityHostScore.createFilter(extraFilters, skippedFilter);

  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getProactivitySummaryByArea: RequestHandler = catchAsync(async (_, res) => {
  const summary = await getSummaryScoreByArea();

  return ServiceResponse.get(summary).send(res);
});

export const getProactivityHostDistribution: RequestHandler = catchAsync(async (_, res) => {
  const hostDistribution = await getHostScoreDistribution();

  return ServiceResponse.get(hostDistribution).send(res);
});

export const getProactivityGlobalScore: RequestHandler = catchAsync(async (_, res) => {
  const globalScore = await getGlobalScore();

  return ServiceResponse.get(globalScore).send(res);
});

export const updateProactivityHostScores: RequestHandler = catchAsync(async (_, res) => {
  await updateAllProactivityScores();

  return ServiceResponse.post({ message: 'Proactivity scores updated successfully' }).send(res);
});

export const updateOneProactivityHostScores: RequestHandler = catchAsync(async (req, res) => {
  // Getting client id from request params
  const { id: clientId } = req.params;
  if (!clientId) throw errors.not_found('Client Id');

  // Get Host from DB
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });

  // Check if host is enabled and not deleted
  if (!client) throw errors.not_found('Client');
  if (!client.enabled) throw errors.not_enabled('Client');

  // Update Proactivity Scores
  await updateProactivityScores([client.rportId]);

  // Get ProactivityHostScore from DB
  const hostScore = await ProactivityHostScore.find({ host: client._id });

  // Return the data
  return ServiceResponse.post(hostScore).send(res);
});

export const updateOneProactivityHostScoreByBatutaId: RequestHandler = catchAsync(
  async (req, res) => {
    // Getting client id from request params
    const { id: clientId } = req.params;
    if (!clientId) throw errors.not_found('Client Id');

    // Get Host from DB
    const client = await RportClient.findById(clientId);

    // Check if host is enabled and not deleted
    if (!client) throw errors.not_found('Client');
    if (!client.enabled) throw errors.not_enabled('Client');

    // Update Proactivity Scores
    await updateProactivityScores([client.rportId]);

    // Get ProactivityHostScore from DB
    const hostScore = await ProactivityHostScore.find({ host: client._id });

    // Return the data
    return ServiceResponse.post(hostScore).send(res);
  }
);
