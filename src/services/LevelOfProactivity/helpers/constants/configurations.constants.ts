import manifest from '../../../../../service.manifest.json';
import { Qualification } from '@services/Reports/helpers/types/proactivity.types';
import { AreaConfiguration } from '../models';
import { VALID_LOP_AREA } from '../models/configuration.model';

interface ProactivityQualification {
  level: Qualification;
  score: number;
}

export type ProactivityConfiguration<Area extends VALID_LOP_AREA> = {
  name: Area;
  configuration: AreaConfiguration<Area>;
};

const { levelOfProactivity } = manifest;

export const LOP_QUALIFICATIONS = levelOfProactivity.qualifications as ProactivityQualification[];
export const LOP_CONFIGURATIONS = levelOfProactivity.configurations as [
  ProactivityConfiguration<'compliance'>,
  ProactivityConfiguration<'platform'>,
  ProactivityConfiguration<'osUpdates'>,
];
