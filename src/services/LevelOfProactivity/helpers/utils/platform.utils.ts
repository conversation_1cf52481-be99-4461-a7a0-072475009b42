import _ from 'lodash';
import { SearchResponse } from '@elastic/elasticsearch/lib/api/types';

import {
  AreaConfiguration,
  AreaContext,
  LevelOfProactivityScore,
  PlatformCategory,
} from '../models/';
import { getQualification } from './qualification.utils';
import { RportClientDocument } from '@root/services/Rport/schemas';
import { runElasticSearchQuery } from '@root/services/Inventory/helpers/connections/elastic';

export const buildElasticCategoriesQuery = (clientIds: string[]) => {
  const size = clientIds.length;
  const _source = false;

  const mustQuery = [
    {
      nested: {
        path: 'Applications',
        query: {
          bool: {
            must: [
              {
                exists: {
                  field: 'Applications',
                },
              },
            ],
          },
        },
      },
    },
  ];

  const filterQuery = {
    terms: {
      _id: clientIds,
    },
  };

  const scriptFields = {
    categories: {
      script: {
        source: `
          Set uniqueCategories = new HashSet();
          for (app in params._source.Applications) {
            if (app.Category != null) {
              uniqueCategories.add(app.Category);
            }
          }
          return new ArrayList(uniqueCategories);
        `,
      },
    },
  };

  return {
    size,
    _source,
    query: {
      bool: {
        must: mustQuery,
        filter: filterQuery,
      },
    },
    script_fields: scriptFields,
  };
};

export const getPlatformContextForProactivity = async (clients: RportClientDocument[]) => {
  const clientIds = clients.map((client) => client.rportId.toString());
  const query = buildElasticCategoriesQuery(clientIds);

  // Run query on elastic search
  const response = (await runElasticSearchQuery(
    query._source,
    null,
    clients.length,
    query.query,
    [{}],
    { script_fields: query.script_fields }
  )) as SearchResponse;

  const hits = response.hits ? response.hits.hits : [];

  return hits;
};

export const getCategoriesDetails = (
  categories: string[],
  configCategories: PlatformCategory[]
) => {
  const details = configCategories.map((configCategory) => ({
    ...configCategory,
    present: categories.includes(configCategory.name),
  }));

  return Object.fromEntries(details.map((detail) => [detail.name, detail]));
};

export const getPlatformProactivityScoreByHost = async (
  client: RportClientDocument,
  configuration: AreaConfiguration<'platform'>,
  context: AreaContext<'platform'>
): Promise<LevelOfProactivityScore> => {
  const hits = context;
  const { categories, relevances } = configuration;

  const clientHit = hits.find((hit) => hit._id === client.rportId.toString());

  const clientCategories = clientHit?.fields?.categories || [];

  const weights = clientCategories.map((category: string) => {
    // If the platform is not found, return 0 -> E
    if (!category) return 0;

    const categoryConfig = categories.find(
      ({ name }) => name.toLowerCase() === category.toLowerCase()
    );
    if (!categoryConfig || !categoryConfig.coverage) return 0;

    const categoryRelevance = relevances.find(
      (relevance) => relevance.relevance === categoryConfig.relevance
    );

    return (categoryConfig.weight * (categoryRelevance?.weight ?? 0)) / 100;
  });

  const percentage = _.sum(weights);
  const qualification = getQualification(percentage);
  const details = getCategoriesDetails(clientCategories, categories);

  return {
    percentage,
    qualification,
    details,
  };
};
