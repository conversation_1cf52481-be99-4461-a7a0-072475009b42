export { getQualification } from './qualification.utils';
export {
  getPlatformContextForProactivity,
  getPlatformProactivityScoreByHost,
} from './platform.utils';
export { getOSUpdatesProactivityScoreByHost } from './updates.utils';
export {
  getComplianceContextForProactivity,
  getComplianceProactivityScoreByHost,
} from './compliance.utils';
export {
  calculateGlobalScore,
  getGlobalScore,
  getHostScoreDistribution,
  getProactivityScoreByHost,
  getProactivityScores,
  getSummaryScoreByArea,
  updateAllProactivityScores,
  updateProactivityScores,
} from './score.utils';
