import _ from 'lodash';
import { Types } from 'mongoose';
import { errors } from '@shared/utils/app-errors';

import { RportClient, RportClientDocument } from '@services/Rport/schemas';

import {
  AreaContext,
  LevelOfProactivityScore,
  ProactivityHostScore as ProactivityHostScoreType,
  ProactivityHostScoreModel,
  Scores,
} from '../models/';
import { ProactivityHostScore } from '../../schemas/';
import { getQualification } from './qualification.utils';
import { CONTEXT_BY_AREA, FUNCTION_BY_AREA, LOP_CONFIGURATIONS } from '../constants';
import { LOP_AREAS, VALID_LOP_AREA } from '../models/configuration.model';
import { AreaScoreFunction } from '../constants/areas.constants';

export const calculateGlobalScore = (
  scores: Record<string, LevelOfProactivityScore>
): LevelOfProactivityScore => {
  const scoresWithoutF = Object.entries(scores).filter(([_, score]) => score.qualification !== 'F');
  if (scoresWithoutF.length == 0) return { percentage: 0, qualification: 'F', details: {} };

  const avgPercentage = _.mean(scoresWithoutF.map(([_, score]) => score.percentage));
  return {
    percentage: avgPercentage,
    qualification: getQualification(avgPercentage),
    details: {},
  };
};

export const getProactivityScoreByHost = async (
  client: RportClientDocument,
  context: { [k in VALID_LOP_AREA]: AreaContext<k> }
): Promise<ProactivityHostScoreType> => {
  const scoresByArea = (await Promise.all(
    LOP_AREAS.map(async (area) => {
      const areaConfiguration = LOP_CONFIGURATIONS.find((config) => config.name == area);

      if (!areaConfiguration) throw errors.not_found('Proactivity Configuration');

      const data = await (FUNCTION_BY_AREA[area] as AreaScoreFunction<typeof area>)(
        client,
        areaConfiguration.configuration,
        context[area]
      );
      return { [area]: data };
    })
  )) as Record<VALID_LOP_AREA, LevelOfProactivityScore>[];

  const scores = scoresByArea.reduce(
    (res, areaResults) => ({ ...res, ...areaResults }),
    {}
  ) as Scores;

  const globalScore = calculateGlobalScore(scores);

  return {
    host: client._id,
    globalScore,
    scores,
  };
};

export const getProactivityScores = async (
  clientIds: Types.ObjectId[] | string[]
): Promise<ProactivityHostScoreType[]> => {
  const clients = await RportClient.find({ rportId: { $in: clientIds }, deleted: false }).populate({
    path: 'platforms.id',
    select: 'name',
  });
  if (!clients || clients.length === 0) return [];

  const context = await Promise.all(
    Object.entries(CONTEXT_BY_AREA).map(async ([area, getContextByArea]) => {
      const contextValue = await getContextByArea(clients);
      return [area, contextValue];
    })
  ).then((entries) => Object.fromEntries(entries));

  return Promise.all(clients.map((client) => getProactivityScoreByHost(client, context)));
};

const _saveProactivityScores = async (
  score: ProactivityHostScoreType
): Promise<ProactivityHostScoreModel | null> => {
  const hostScore = await ProactivityHostScore.findOneAndUpdate({ host: score.host }, score, {
    upsert: true,
    new: true,
  });

  await RportClient.updateOne({ _id: score.host }, { $set: { scoring: hostScore._id } });

  return hostScore;
};

export const updateProactivityScores = async (clientIds: Types.ObjectId[] | string[]) => {
  const proactivityScores = await getProactivityScores(clientIds);
  if (!proactivityScores) return;

  await Promise.all(proactivityScores.map(_saveProactivityScores));
};

export const updateAllProactivityScores = async () => {
  const clients = await RportClient.find({ deleted: false });
  if (!clients) return;

  await updateProactivityScores(clients.map((client) => client.rportId));
};

export const getSummaryScoreByArea = async () => {
  const areaSummaries = await ProactivityHostScore.aggregate([
    {
      $project: {
        scores: {
          $objectToArray: '$scores',
        },
      },
    },
    {
      $unwind: '$scores',
    },
    {
      $match: {
        'scores.v.percentage': { $ne: null },
      },
    },
    {
      $group: {
        _id: '$scores.k',
        average: { $avg: '$scores.v.percentage' },
        sum: { $sum: '$scores.v.percentage' },
        count: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
        area: '$_id',
        average: { $round: ['$average', 2] },
        count: 1,
        sum: 1,
      },
    },
  ]);

  const summary = Object.fromEntries(
    areaSummaries.map((areaSummary: Record<string, any>) => [
      areaSummary.area,
      {
        percentage: areaSummary.average,
        qualification: getQualification(areaSummary.average),
      },
    ])
  );

  LOP_CONFIGURATIONS.forEach(({ name }) => {
    if (!summary[name]) {
      summary[name] = {
        percentage: 0,
        qualification: 'F',
      };
    }
  });

  return summary;
};

export const getHostScoreDistribution = async () => {
  const hostDistribution = await ProactivityHostScore.aggregate([
    {
      $group: {
        _id: '$globalScore.qualification',
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$count' },
        distribution: { $push: { qualification: '$_id', count: '$count' } },
      },
    },
    {
      $unwind: '$distribution',
    },
    {
      $project: {
        _id: 0,
        total: 1,
        qualification: '$distribution.qualification',
        count: '$distribution.count',
        percentage: {
          $round: [{ $multiply: [{ $divide: ['$distribution.count', '$total'] }, 100] }, 2],
        },
      },
    },
    {
      $group: {
        _id: null,
        total: { $first: '$total' },
        distribution: {
          $push: {
            qualification: '$qualification',
            count: '$count',
            percentage: '$percentage',
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        total: 1,
        distribution: 1,
      },
    },
  ]);

  if (hostDistribution && hostDistribution.length > 0) {
    return hostDistribution[0];
  }

  return { total: 0, distribution: [] };
};

export const getGlobalScore = async () => {
  const globalScore = await ProactivityHostScore.aggregate([
    {
      $project: {
        scores: {
          $objectToArray: '$scores',
        },
      },
    },
    {
      $unwind: '$scores',
    },
    {
      $group: {
        _id: '$scores.k',
        average: { $avg: '$scores.v.percentage' },
      },
    },
    {
      $project: {
        _id: 0,
        average: 1,
      },
    },
    {
      $group: {
        _id: null,
        average: { $avg: '$average' },
      },
    },
    {
      $project: {
        _id: 0,
        average: { $round: ['$average', 2] },
      },
    },
  ]);

  if (globalScore && globalScore.length > 0) {
    const globalPercentage = globalScore[0]?.average ?? 0;
    const qualification = getQualification(globalPercentage);

    return { percentage: globalPercentage, qualification };
  }

  return { percentage: 0, qualification: 'F' };
};
