import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { RportIP } from './rport-ip.schema';
import { RportClientModel } from '../helpers/models';
import { Logger } from '@shared/helpers/classes/logger.class';
import { Criticality } from '../helpers/constants';

const rportClientSchema = schemaFactory({
  rportId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  address: { type: String, required: true },
  addressHistory: {
    type: [
      {
        date: Date,
        ip: String,
      },
    ],
    default: [],
  },
  os: { type: String, required: true },
  osKernel: { type: String, required: true },
  osVersion: { type: String, required: true },
  osFamily: { type: String, required: true },
  ipv4: [{ type: String, required: true }],
  platforms: [
    {
      id: { type: Types.ObjectId, required: true, ref: 'RportPlatform' },
      status: { type: String, required: false },
      checkedAt: { type: Date, required: false },
      lastChangeAt: { type: Date, required: false },
      agentId: String,
      running: { type: Boolean, required: true, default: false },
      startedAt: { type: Date },
    },
  ],
  tags: [{ type: Types.ObjectId, required: true, ref: 'RportTag' }],
  uninstalling: { type: Boolean, required: true, default: false },
  uninstallingStartingTime: { type: Date, required: false },
  clientEmail: { type: String },
  updatesStatus: [
    {
      title: { type: String, required: true },
      description: { type: String, required: true },
      isSecurityUpdate: { type: Boolean, required: true, default: false },
      rebootRequired: { type: Boolean, required: true, default: false },
    },
  ],
  rebootPending: { type: Boolean, required: false, default: false },
  updatesRefreshedAt: { type: Date, required: false },
  scoring: { type: Types.ObjectId, required: false, ref: 'ProactivityHostScore', default: null },
  criticality: { type: String, enum: Object.values(Criticality), default: Criticality.NotSet },
  monitoringEnabled: { type: Boolean, required: false, default: false },
});

rportClientSchema.index({ name: 1 }, { partialFilterExpression: { deleted: false } });
rportClientSchema.index({ address: 1, deleted: 1 });
rportClientSchema.index({ addressHistory: 1 });
rportClientSchema.index({ os: 1 });
rportClientSchema.index({ osKernel: 1 });
rportClientSchema.index({ osFamily: 1 });
rportClientSchema.index({ osVersion: 1 });
rportClientSchema.index({ platforms: 1 });
rportClientSchema.index({ ipv4: 1 });
rportClientSchema.index({ tags: 1, deleted: 1 });
rportClientSchema.index({ uninstalling: 1 });
rportClientSchema.index({ uninstallingStartingTime: 1 });
rportClientSchema.index({ updatesStatus: 1, deleted: 1 });
rportClientSchema.index({ rebootPending: 1 });
rportClientSchema.index({ updatesRefreshedAt: 1 });
rportClientSchema.index({ deleted: 1, rportId: 1 });
rportClientSchema.index({ deleted: 1, 'platforms.0': 1 });
rportClientSchema.index({ deleted: 1, 'platforms.id': 1 });
rportClientSchema.index({ deleted: 1, name: 1 });
rportClientSchema.index({ deleted: 1, enabled: 1, name: 1 });
rportClientSchema.index({ deleted: 1, enabled: 1, name: 1, 'platforms.id': 1 });
rportClientSchema.index({ criticality: 1 });
rportClientSchema.index({ monitoringEnabled: 1 });
rportClientSchema.index({ deleted: 1, osKernel: 1, monitoringEnabled: 1 });

rportClientSchema.pre('findOneAndUpdate', async function (this: any) {
  const clientData = this.getUpdate();
  const client = await this.model.findOne(this.getQuery());

  if (clientData.address) {
    if (client) {
      if (client.address !== clientData.address) {
        this.set({
          addressHistory: [
            ...(client.addressHistory || []),
            { date: clientData['$set'].updatedAt, ip: clientData.address },
          ],
        });
      }
    } else {
      this.set({
        addressHistory: [{ date: clientData['$set'].updatedAt, ip: clientData.address }],
      });
    }

    try {
      await RportIP.addIP(clientData.address);
    } catch (error) {
      Logger.error(`Error when attempting to add address ${clientData.address}: ${error}`);
    }
  }
});

export type RportClientDocument = RportClientModel & Document & { _id: Types.ObjectId };
export const RportClient = modelFactory<RportClientModel>('RportClient', rportClientSchema);
