import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@root/shared/utils/model-factory';

import { RportScriptModel } from '../helpers/models';

import { errors } from '@root/shared/utils/app-errors';

import {
  MAX_SCRIPTS_NAME,
  MAX_SCRIPTS_DESCRIPTION,
  MAX_SCRIPTS_TENANT,
  MAX_TAGS_PER_SCRIPT,
} from '../helpers/constants/';

const rportScriptSchema = schemaFactory({
  name: { type: String, required: true, maxlength: MAX_SCRIPTS_NAME, minlength: 1, unique: true },
  description: { type: String, maxlength: MAX_SCRIPTS_DESCRIPTION, default: '' },
  script: { type: String, required: true },
  shellType: { type: String, required: true },
  tags: [{ type: String, ref: 'RportScriptTag', default: [] }],
  updatedBy: { type: String, required: true },
  createdBy: { type: String, required: true },
});

// Adding pre-save middleware to the schema
rportScriptSchema.pre<RportScriptModel & Document>('save', async function () {
  // Count the current number of scripts
  const scriptsCount = await this.model('RportScript').countDocuments({});

  // Check if the limit is reached
  if (scriptsCount >= MAX_SCRIPTS_TENANT) {
    throw errors.max_entities_tenant('Custom Scripts', MAX_SCRIPTS_TENANT);
  }

  // Validate max tags per script
  if (this.tags.length > MAX_TAGS_PER_SCRIPT) {
    throw errors.max_assignments('Tags', 'Scripts', MAX_TAGS_PER_SCRIPT);
  }
});

rportScriptSchema.pre('findOneAndUpdate', async function (this: any) {
  // Validate max tags per script
  if (this.getUpdate().tags && this.getUpdate().tags.length > MAX_TAGS_PER_SCRIPT) {
    throw errors.max_assignments('Tags', 'Scripts', MAX_TAGS_PER_SCRIPT);
  }
});

rportScriptSchema.pre('updateOne', function (this: any) {
  // Validate max tags per script
  if (this.getUpdate().tags && this.getUpdate().tags.length > MAX_TAGS_PER_SCRIPT) {
    throw errors.max_assignments('Tags', 'Scripts', MAX_TAGS_PER_SCRIPT);
  }
});

export type RportScriptDocument = (RportScriptModel & Document & { _id: Types.ObjectId }) | null;
export const RportScript = modelFactory<RportScriptModel>('RportScript', rportScriptSchema);
