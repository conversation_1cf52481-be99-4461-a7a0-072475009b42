import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@root/shared/utils/model-factory';

import { RportGroupModel } from '../helpers/models';

import { errors } from '@root/shared/utils/app-errors';

import {
  MAX_GROUPS_TENANT,
  MAX_GROUP_NAME,
  MAX_GROUP_DESCRIPTION,
} from '../helpers/constants/groups';

const rportGroupSchema = schemaFactory({
  name: { type: String, required: true, maxlength: MAX_GROUP_NAME, minlength: 1 },
  description: { type: String, maxlength: MAX_GROUP_DESCRIPTION, default: '' },
  type: { type: String, required: true, default: 'STATIC', enum: ['STATIC', 'DYNAMIC'] },
  rules: {
    type: Object,
    of: [
      {
        condition: {
          type: String,
          required: true,
          default: 'IS',
          enum: ['IS', 'IS_NOT', 'CONTAINS', 'NOT_CONTAINS'],
        },
        value: { type: String, required: true },
      },
    ],
    default: {},
  },
  updatedBy: { type: String, required: true },
  createdBy: { type: String, required: true },
});

rportGroupSchema.index({ type: 1 });
rportGroupSchema.index({ name: 1 }, { partialFilterExpression: { deleted: false } });
rportGroupSchema.index({ name: 1, deleted: 1 });

// Adding pre-save middleware to the schema
rportGroupSchema.pre<RportGroupModel & Document>('save', async function () {
  // Count the current number of groups
  const groupsCount = await this.model('RportGroup').countDocuments({});

  // Check if the limit is reached
  if (groupsCount >= MAX_GROUPS_TENANT) {
    throw errors.max_entities_tenant('groups', MAX_GROUPS_TENANT);
  }

  // Replace blank spaces with underscores
  this.name = this.name.replace(/\s/g, '_');
});

rportGroupSchema.pre('findOneAndUpdate', async function (this: any) {
  if (this.getUpdate().name) {
    this.getUpdate().name = this.getUpdate().name.replace(/\s/g, '_');
  }
});

rportGroupSchema.pre('updateOne', function (this: any) {
  if (this.getUpdate().name) {
    this.getUpdate().name = this.getUpdate().name.replace(/\s/g, '_');
  }
});

export type RportGroupDocument = (RportGroupModel & Document & { _id: Types.ObjectId }) | null;
export const RportGroup = modelFactory<RportGroupModel>('RportGroup', rportGroupSchema);
