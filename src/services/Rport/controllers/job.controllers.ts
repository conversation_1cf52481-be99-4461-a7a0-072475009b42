import { RequestHandler, Request } from 'express';

import { RportClient, RportJob } from '../schemas';

import { GetAllQuery } from '@shared/types';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';
import { RportMultipleJob } from '../helpers/types';
import { QueuesTask } from '@services/Queues/schemas';
import { KnownQueuesTaskDocument, QueuesTaskDocument } from '@services/Queues/schemas/task.schema';

import { getJobFilters } from '../helpers/functions';
import { exportFileOf } from '@services/Export/controllers';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
import { ServiceResponse } from '@root/shared/models/service-response';
import { getClientsFromJob, getJobDetails, getOnlyJobData } from '../helpers/utils/rport-job.utils';
import {
  BooleanRelative,
  FilterType,
  NumberRelative,
  SelectRelative,
  StringRelative,
} from '@root/shared/models';
import { TaskParams } from '@root/services/Queues/helpers/types/task.types';

export const jobFilter: RequestHandler = catchAsync(async (_, res) => {
  const skippedFilter = [
    'jobId',
    'jobIds',
    'command',
    'timeout',
    'is_batuta_command',
    'clients',
    'clientsInQueue',
    'updatedAt',
    'deleted',
    'status',
    'protected',
  ];
  const extraFilters = getJobFilters();
  const [filter, fields] = await RportJob.createFilter(extraFilters, skippedFilter);

  res.status(200).json({ filter, fields });
});

const _getJobs = async (filter: any, limit?: number, offset?: number, sort?: string) => {
  // Building the query based on the constructed filters
  const [query, sortQuery] = RportJob.parseFilter(filter, sort);

  let resultsFinder = RportJob.find(query)
    .sort(sortQuery ?? '-createdAt')
    .skip(offset ?? 0)
    .populate('platform', 'name');
  if (!limit || limit > 0) {
    resultsFinder = resultsFinder.limit(limit ?? 100);
  }

  const [filteredResults, filteredResultsCount] = await Promise.all([
    await resultsFinder,
    await RportJob.countDocuments(query),
  ]);

  return {
    meta: {
      total: filteredResultsCount,
      resources: filteredResults.length,
      offset: +(offset ?? 0),
    },
    data: filteredResults,
  };
};

export const getJobs: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const responseData = await _getJobs(filter, limit, offset, sort);

  return res.status(200).json(responseData);
});

export const exportJobs: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, _getJobs, ({ data }) => data);
});

export const getOneJob: RequestHandler = catchAsync(async (req, res) => {
  const jobIdParam = (req as Request).params.id;
  const job = await RportJob.findById(jobIdParam).populate('platform', 'name');
  if (!job) {
    throw errors.not_found('Job');
  }

  if (!job.jobIds || !job.jobIds.includes(job.jobId)) {
    if (!job.jobIds) job.jobIds = [];

    if (job.jobId !== null) {
      job.jobIds.push(job.jobId);
      await job.save();
    }
  }

  const jobDetails: RportMultipleJob[] = await getJobDetails(job.jobId, job.jobIds);

  const summary: Record<string, number> = {
    running: 0,
    successful: 0,
    failed: 0,
    unknown: 0,
    queued: 0,
  };

  let lastFinishedAt = job.updatedAt.toISOString();
  for (const result of jobDetails) {
    const jobs = result?.jobs || [result];
    for (const job of jobs) {
      summary[job.status] += 1;

      if (job.finished_at && +new Date(job.finished_at) > +new Date(lastFinishedAt)) {
        lastFinishedAt = new Date(job.finished_at).toISOString();
      }
    }
  }

  if (job.useQueue) {
    summary.queued = job.clientsInQueue.length;
  }

  let tasks: QueuesTaskDocument[] = [];

  if (job.clientsInQueue.length > 0) {
    tasks = await QueuesTask.find<KnownQueuesTaskDocument<TASK_NAMES.RUN_COMMAND>>({
      name: TASK_NAMES.RUN_COMMAND,
      params: {
        rportJobId: job.id,
      } as TaskParams<TASK_NAMES.RUN_COMMAND>,
    });
  }

  return ServiceResponse.get({ job, summary, tasks, lastFinishedAt }).send(res);
});

export const deleteJob: RequestHandler = catchAsync(async (req, res) => {
  const jobId = req.params.id;

  const job = await RportJob.findById(jobId);
  if (!job) {
    throw errors.not_found('Job');
  }

  res.status(204).json({});
});

const _getOneJobHosts = async (
  filter?: string,
  limit?: number,
  offset?: number,
  sort?: string,
  req?: Request | any
) => {
  const jobIdParam = (req as Request).params?.id;
  const job = await RportJob.findById(jobIdParam).populate('platform', 'name');
  if (!job) {
    throw errors.not_found('Job');
  }

  const jobDetails: RportMultipleJob[] = await getJobDetails(job.jobId, job.jobIds);
  const jobsData = await getOnlyJobData(jobDetails);

  const { total, data } = await getClientsFromJob(
    job.clients,
    job.clientsInQueue,
    jobsData,
    filter,
    limit,
    offset,
    sort
  );

  return { total, data };
};

export const getOneJobHosts: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const { total, data } = await _getOneJobHosts(filter, limit, offset, sort, req);

  return ServiceResponse.get(data, total, +(offset ?? 0)).send(res);
});

export const exportOneJobHosts: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, _getOneJobHosts, ({ data }) => data);
});

export const getJobHostFilter: RequestHandler = catchAsync(async (req, res) => {
  const extraFilters: { [key: string]: any } = {
    status: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: [
        { key: 'successful', value: 'successful' },
        { key: 'failed', value: 'failed' },
        { key: 'unknown', value: 'unknown' },
        { key: 'queued', value: 'queued' },
        { key: 'running', value: 'running' },
      ],
    },
    stderr: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    stdout: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    use_queue: {
      type: FilterType.BOOLEAN,
      relatives: Object.values(BooleanRelative),
    },
    finished_at: {
      type: FilterType.DATE,
      relatives: Object.values(NumberRelative),
    },
  };

  const skippedFilter = [
    'deleted',
    'enabled',
    'protected',
    'address',
    'os',
    'osKernel',
    'osVersion',
    'osFamily',
    'uninstalling',
    'uninstallingStartingTime',
    'clientEmail',
    'rebootPending',
    'updatesRefreshedAt',
    'scoring',
    'createdAt',
    'updatedAt',
  ];

  const [filter, fields] = await RportClient.createFilter(
    extraFilters,
    skippedFilter,
    Object.keys(extraFilters)
  );

  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getOneJobReponse: RequestHandler = catchAsync(async (req, res) => {
  const jobIdParam = (req as Request).params?.id;
  const job = await RportJob.findById(jobIdParam).populate('platform', 'name');
  if (!job) {
    throw errors.not_found('Job');
  }

  const jobDetails: RportMultipleJob[] = await getJobDetails(job.jobId, job.jobIds);
  if (!jobDetails || jobDetails.length === 0) throw errors.not_found('Job details');

  const jobDetail = jobDetails[0];
  if (!jobDetail.jobs || jobDetail.jobs.length === 0) throw errors.not_found('Job details');

  const jobResponse = jobDetail.jobs[0];

  return ServiceResponse.get(jobResponse).send(res);
});
