import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { startSession } from 'mongoose';

import { RportScriptTag, RportScript } from '../schemas';

import { GetAllQuery } from '@shared/types';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';

export const scriptTagFilter: RequestHandler = catchAsync(async (_, res) => {
  const [filter, fields] = await RportScriptTag.createFilter(undefined, [
    'deleted',
    'protected',
    'enabled',
  ]);

  res.status(200).json({ filter, fields });
});

export const getAllScriptTag: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery] = RportScriptTag.parseFilter(filter, sort);

  const [filteredResults, filteredResultsCount] = await Promise.all([
    await RportScriptTag.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0),
    await RportScriptTag.countDocuments(query),
  ]);

  return res.status(200).json({
    meta: {
      count: filteredResultsCount,
      resources: filteredResults.length,
      offset: +(offset ?? 0),
    },
    data: filteredResults,
  });
});

export const getOneScriptTag: RequestHandler = catchAsync(async (req, res) => {
  const tag = await RportScriptTag.findById(req.params.id, {});
  if (!tag) throw errors.not_found('Script Tag');

  res.status(200).json(tag);
});

export const createScriptTag: RequestHandler = catchAsync(async (req, res) => {
  const { name } = req.body as { name: string };

  // Validating if the tag already exists
  const registeredTag = await RportScriptTag.findOne({ name });
  if (registeredTag) throw errors.already_exists('Script Tag');

  const tag = await RportScriptTag.create({ name });

  res.status(201).json(tag);
});

export const updateScriptTag: RequestHandler = catchAsync(async (req, res) => {
  const { name } = req.body as { name: string };

  const tag = await RportScriptTag.findByIdAndUpdate(
    req.params.id,
    { name },
    { runValidators: true, new: true }
  );

  if (!tag) throw errors.not_found('Script Tag');

  res.status(200).json(tag);
});

export const deleteScriptTag: RequestHandler = catchAsync(async (req, res) => {
  const tag = await RportScriptTag.findById(req.params.id);
  if (!tag) throw errors.not_found('Script Tag');

  // Start mongo db transaction
  const session = await startSession();
  session.startTransaction();
  // Pull removing tag from all hosts
  await RportScript.updateMany({}, { $pull: { tags: tag._id } });
  // Delete tag
  await RportScriptTag.deleteOne({ _id: tag._id });

  // Commit mongo db transaction
  await session.commitTransaction();

  res.status(204).json({});
});
