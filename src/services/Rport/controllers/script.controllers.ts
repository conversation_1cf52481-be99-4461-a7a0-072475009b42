import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';

import { RportScript } from '../schemas';

import { GetAllQuery } from '@shared/types';

import { RportScriptRequest } from '../helpers/models';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';

import {
  updateScripTags,
  validateShellType,
  getValidatedScriptTags,
  getScriptFilters,
  parseScriptFilters,
} from '../helpers/functions';

export const scriptFilter: RequestHandler = catchAsync(async (_, res) => {
  const extraFilters = await getScriptFilters();
  const [filter, fields] = await RportScript.createFilter(extraFilters, [
    'deleted',
    'protected',
    'enabled',
  ]);

  res.status(200).json({ filter, fields });
});

export const getAllScripts: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = RportScript.parseFilter(filter, sort, ['tags']);

  const extra = parseScriptFilters(extraFilter);

  const [filteredResults, filteredResultsCount] = await Promise.all([
    await RportScript.find({ $and: [query, extra] })
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0)
      .populate('tags')
      .select('-__v -deleted'),
    await RportScript.countDocuments({ $and: [query, extra] }),
  ]);

  return res.status(200).json({
    meta: {
      count: filteredResultsCount,
      resources: filteredResults.length,
      offset: +(offset ?? 0),
    },
    data: filteredResults,
  });
});

export const getOneScript: RequestHandler = catchAsync(async (req, res) => {
  const script = await RportScript.findById(req.params.id, {}).populate('tags');
  if (!script) throw errors.not_found('Script');

  res.status(200).json(script);
});

export const createScript: RequestHandler = catchAsync(async (req, res) => {
  const { name, description, script, shellType, tags } = req.body as RportScriptRequest;

  // Validating if the tag already exists
  const registeredScript = await RportScript.findOne({ name });
  if (registeredScript) throw errors.already_exists('Script');

  // Validating the shell type
  if (!validateShellType(shellType)) throw errors.not_valid('Shell Type');

  // Getting validated Script Tags
  const validatedTags = await getValidatedScriptTags(tags);

  const newScript = await RportScript.create({
    name,
    description,
    script,
    shellType,
    tags: validatedTags,
    updatedBy: req.user?.email,
    createdBy: req.user?.email,
  });

  res.status(201).json(newScript);
});

export const updateScript: RequestHandler = catchAsync(async (req, res) => {
  const { tags, ...updatedBody } = req.body as RportScriptRequest;

  const { id } = req.params as { id: string };

  // Validating the shell type
  if (updatedBody.shellType && !validateShellType(updatedBody.shellType))
    throw errors.not_valid('Shell Type');

  if (tags && tags.length !== 0) {
    await updateScripTags(id, tags);
  }

  const updatedScript = await RportScript.findByIdAndUpdate(
    id,
    {
      ...updatedBody,
      updatedBy: req.user?.email,
    },
    { runValidators: true, new: true }
  ).populate('tags');

  if (!updatedScript) throw errors.not_found('Script');

  res.status(200).json(updatedScript);
});

export const setTagsToScript: RequestHandler = catchAsync(async (req, res) => {
  const { tags } = req.body as { tags: string[] };
  const { id } = req.params as { id: string };

  await updateScripTags(id, tags);

  const updatedScript = await RportScript.findById(id);
  if (!updatedScript) throw errors.not_found('Script');

  // Update the updatedBy field
  await RportScript.findByIdAndUpdate(
    req.params.id,
    { updatedBy: req.user?.email },
    { new: true }
  ).populate('tags');

  // Returning the updated script
  res.status(200).json(updatedScript);
});

export const deleteScript: RequestHandler = catchAsync(async (req, res) => {
  const script = await RportScript.findById(req.params.id);
  if (!script) throw errors.not_found('Script');

  // Delete Script
  await RportScript.deleteOne({ _id: script._id });

  res.status(204).json({});
});

export const deleteManyScripts: RequestHandler = catchAsync(async (req, res) => {
  const { script } = req.query as { script: string | string[] };

  if (!script) throw errors.not_valid('Script');

  await RportScript.deleteMany({ _id: Array.isArray(script) ? { $in: script } : script });

  res.status(204).json({});
});
