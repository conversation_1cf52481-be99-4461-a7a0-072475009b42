import _ from 'lodash';
import { RequestHandler } from 'express';
import { Types } from 'mongoose';

import { QueuesTask } from '@root/services/Queues/schemas';
import {
  RportClient,
  RportJob,
  RportIP,
  RportPlatform,
  ClientGroup,
  BatutaCommand,
  ProcessDesiredState,
  ProcessDesiredStateDocument,
} from '../schemas';
import {
  RportClient as RportClientApi,
  RportClientAuth,
  RportClientUpdateStatus,
  RportUpdateStatusModel,
} from '../helpers/types/';

import { GetAllQuery } from '@shared/types';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';

import { randomPasswordRport } from '@shared/utils/strings';
import { Logger } from '@shared/helpers/classes/logger.class';
import { Gateway } from '@shared/helpers/classes/gateway.class';
import { delay } from '@shared/utils/delay';
import { Time } from '@shared/helpers/classes/times.class';

import { exportFileOf } from '@services/Export/controllers/';
import { getAndValidateClientsConnected } from '../helpers/utils/rport-platform.utils';
import TasksFunctions from '@services/Queues/helpers/functions/task.function';
import { removeHostInventory } from '@services/Inventory/helpers/functions';

import { updateTags } from '../helpers/utils/rport-clients-tag.utils';
import {
  getClient,
  getClientJobs,
  getClientJobDetail,
  getMultiClientJobs,
  getMultiClientJobDetail,
  getClientAuth,
  createClientAuth,
  getInstaller,
  getServerStatus,
  executeBatutaCommandOnClientsOrGroups,
  deleteDisconnectedClient,
  executeCommandOnClientsOrGroups,
  getClientCPUUsage,
  getClientMemoryUsage,
  getClientLanUsage,
  getClientBpsLanUsage,
} from '../helpers/connections/rport';

import {
  getDeviceFromService,
  getAgentIdInPlatform,
  removeDeviceFromPlatform,
  updateClients,
  removeOldClients,
  getClientFilters,
  getClientsFromRport,
  getRebootCommand,
  getHostProcessesById,
  stopProcessesById,
  getCountClientsAddedSince,
  getOsUpdateSummary,
  updateSingleClientOsUpdates,
  updateMacAddresses,
} from '../helpers/functions';

import {
  postUpdateOrCreateHost,
  validateHost,
  validateHostConnection,
} from '../helpers/utils/rport-clients.utils';

import { PAIRING_BASE_URL, RPORT_AGENT_VERSION } from '@shared/constants/env';

import { getClientsCountByOS } from '../helpers/queries/client.queries';

import { PENDING } from '@services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@services/Queues/helpers/constants/tasks';
import { getHostById, updateClientsOsUpdates } from '../helpers/functions/client.functions';
import { ServiceResponse } from '@root/shared/models/service-response';
import { ProcessModel } from '../helpers/models';
import { TaskParams } from '@root/services/Queues/helpers/types/task.types';
import { KnownQueuesTaskDocument } from '@root/services/Queues/schemas/task.schema';
import { userHasPermissionToFunctionality } from '@root/shared/helpers/functions/validate-permission.function';
import {
  deleteHostFromVulnService,
  getTotalVulnsByHostId,
} from '@root/services/Vulnerabilities/helpers/vulnerabilities.functions';
import { updateProactivityScores } from '@root/services/LevelOfProactivity/helpers/utils';

const deleteClient = async (
  clientId: string,
  validateUninstall = true,
  deleteCredentials = false
) => {
  // Getting client
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });
  if (!client) throw errors.rport_host_not_found();

  if (validateUninstall) {
    // Validating if the client should be removed
    if (!client.uninstalling) throw errors.invalid_uninstalling_host();
  }

  // Removing host from groups
  await ClientGroup.deleteMany({ clientId: client.rportId });

  // Setting client as deleted in the DB
  await RportClient.findOneAndUpdate(
    { rportId: clientId },
    { $set: { deleted: true, uninstalling: false } }
  );

  // Removing client from RPort
  try {
    await deleteDisconnectedClient(clientId, deleteCredentials);
  } catch (error: any) {
    Logger.warning(`Unable to remove client ${clientId} from RPort: ${error}`);
  }
};

export const clientFilter: RequestHandler = catchAsync(async (_, res) => {
  const [filter, fields] = await getClientFilters();
  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getAllClients: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const responseData = await getClientsFromRport(filter, limit, offset, sort);

  const { data, meta } = responseData;

  if (req.user) {
    const userHasPermission = await userHasPermissionToFunctionality(
      req.user,
      'read:vulnerabilities.vulnerabilities'
    );
    if (userHasPermission) {
      for (const host of data) {
        host.vulnsCount = await getTotalVulnsByHostId(host.id);
      }
    }
  }

  return ServiceResponse.get({ data, meta }).send(res);
});

export const exportClients: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, getClientsFromRport, ({ data }) => data);
});

export const getOneClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;

  const rportClient = await getHostById(clientId);

  return ServiceResponse.get({ client: rportClient }).send(res);
});

export const getOneClientFromBatuta: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;

  let storedClient = await RportClient.findOne({ _id: clientId, deleted: false }, 'rportId');
  if (!storedClient) throw errors.not_found('Rport client');

  const rportClient = await getClient(storedClient.rportId);
  if (!rportClient) throw errors.not_found('Rport client');

  // Parse Updates Status
  const newUpdateStatus: RportClientUpdateStatus[] =
    rportClient.updates_status?.update_summaries?.map((update: RportUpdateStatusModel) => ({
      title: update.title,
      description: update.description ?? '',
      isSecurityUpdate: update.is_security_update,
      rebootRequired: update.reboot_required,
    })) ?? [];

  await RportClient.findOneAndUpdate(
    { _id: storedClient.id },
    {
      $set: {
        name: rportClient.name,
        address: rportClient.address,
        os: rportClient.os_full_name,
        osKernel: rportClient.os_kernel,
        osVersion: rportClient.os_version,
        osFamily: rportClient.os_family,
        ipv4: rportClient.ipv4,
        updatesStatus: newUpdateStatus,
        rebootPending: rportClient.updates_status?.reboot_pending ?? false,
        updatesRefreshedAt: rportClient.updates_status?.refreshed,
        monitoringEnabled: rportClient.client_configuration?.monitoring?.enabled || false,
      },
    }
  );

  // async process to avoid blocking the request
  process.nextTick(async () => {
    if (!storedClient) return;

    try {
      // Update Proactivity Scores
      await updateProactivityScores([storedClient.rportId]);
      Logger.info('Proactivity scores updated successfully for host:', storedClient.rportId);
    } catch (error) {
      Logger.error('Error updating proactivity scores:', error);
    }
  });

  storedClient = await RportClient.findById(clientId)
    .populate({
      path: 'platforms.id',
      select: 'name template commands',
      populate: {
        path: 'template',
        select: 'service',
        populate: { path: 'service', select: 'name internalName' },
      },
    })
    .populate('tags', 'name')
    .populate('scoring');

  // Update MAC Addresses collection
  if (rportClient.addrs) {
    try {
      await updateMacAddresses(rportClient.id, rportClient.addrs);
    } catch (error) {
      Logger.error(`Error when attempting to update MAC Addresses: ${error}`);
    }
  }

  rportClient.platforms = storedClient?.platforms ?? [];
  rportClient.batutaId = storedClient?._id;
  rportClient.createdAt = storedClient?.createdAt;
  rportClient.criticality = storedClient?.criticality;
  rportClient.tags = storedClient?.tags;
  rportClient.enabled = storedClient?.enabled;
  rportClient.scoring = storedClient?.scoring;
  rportClient.uninstalling = storedClient?.uninstalling;
  rportClient.uninstallingStartingTime = storedClient?.uninstallingStartingTime;
  rportClient.uninstallingTaskId = null;
  if (rportClient.uninstalling) {
    const query = {
      name: TASK_NAMES.UNINSTALL_BATUTA,
      params: {
        selectedHosts: rportClient.id,
        affectedHosts: { $ne: rportClient.id },
      } as Partial<Record<keyof TaskParams<TASK_NAMES.UNINSTALL_BATUTA>, any>>,
      status: PENDING,
    };

    const task = await QueuesTask.findOne<KnownQueuesTaskDocument<TASK_NAMES.UNINSTALL_BATUTA>>(
      query
    )
      .select('_id')
      .lean();
    rportClient.uninstallingTaskId = task?._id;
  }

  return ServiceResponse.get({ client: rportClient }).send(res);
});

export const getOneClientJobList: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  const { filter, limit, offset, sort } = req.query as {
    filter?: { [key: string]: string };
    limit?: string;
    offset?: string;
    sort?: string;
  };

  const result = await getClientJobs(clientId, filter || {}, limit, offset, sort);

  return ServiceResponse.get({
    data: result.data,
    count: result.meta.count,
  }).send(res);
});

export const getOneClientJob: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  const jobId = req.params.jobId;

  const jobResult = await getClientJobDetail(clientId, jobId);

  await RportJob.findOneAndUpdate(
    {
      $or: [{ jobId: jobId }, { jobIds: { $in: [jobId] } }],
    },
    { clients: [clientId] }
  );

  return ServiceResponse.get(jobResult).send(res);
});

export const getMultiClientJobList: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as {
    filter?: { [key: string]: string };
    limit?: string;
    offset?: string;
    sort?: string;
  };

  const result = await getMultiClientJobs(filter || {}, limit, offset, sort);

  return ServiceResponse.get({ count: result.meta.count, data: result.data }).send(res);
});

export const getMultiClientJob: RequestHandler = catchAsync(async (req, res) => {
  const jobId = req.params.jobId;

  const jobResult = await getMultiClientJobDetail(jobId);

  await RportJob.findOneAndUpdate(
    {
      $or: [{ jobId: jobId }, { jobIds: { $in: [jobId] } }],
    },
    { clients: jobResult.jobs.map((job) => job.client_id) }
  );

  return ServiceResponse.get(jobResult).send(res);
});

export const getClientPlatformInfo: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  const platformId = new Types.ObjectId(req.params.platformId);

  // Getting client
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });
  if (!client) throw errors.rport_host_not_found();
  if (!client.enabled) throw errors.host_not_enabled();

  const platformInClient = client.platforms.find((pl) => pl.id.equals(platformId));

  if (!platformInClient) throw errors.platform_not_in_client();

  const platform = await RportPlatform.findById(platformId);
  if (!platform || !platform.enabled || platform.deleted) throw errors.not_found('Platform');

  let device;

  try {
    device = await getDeviceFromService(platformId, platformInClient.agentId);
  } catch (error) {
    Logger.error(`Error getting device info from platform "${platform.name}": ${error}`);
  }

  return ServiceResponse.get(device).send(res);
});

export const addOrUpdateClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  const { token, tags } = req.query as { token?: string; tags?: string };

  let rportClient: RportClientApi | undefined;
  let count = 0;

  while (!rportClient && count < 3) {
    try {
      rportClient = await getClient(clientId);
    } catch (error: any) {
      Logger.info(`RPort client with ID ${clientId} not found, trying again. Error: ${error}`);
    }
    if (!rportClient) {
      await delay(2000);
      count++;
    }
  }

  if (!rportClient) {
    Logger.warning(`RPort client with ID ${clientId} NOT FOUND.`);
    throw errors.not_found('RPort Client');
  }

  let clientEmail: string | undefined;

  if (token) {
    clientEmail = await Gateway.getClientEmailFromToken(token);
  }

  // Parse Updates Status
  const newUpdateStatus: RportClientUpdateStatus[] =
    rportClient.updates_status?.update_summaries?.map((update) => ({
      title: update.title,
      description: update.description ?? '',
      isSecurityUpdate: update.is_security_update,
      rebootRequired: update.reboot_required,
    })) ?? [];

  // Create or update the client
  const client = await RportClient.findOneAndUpdate(
    { rportId: clientId },
    {
      $setOnInsert: {
        clientEmail,
        tags: [],
      },
      $set: {
        rportId: clientId,
        name: rportClient.name,
        address: rportClient.address,
        os: rportClient.os_full_name,
        osKernel: rportClient.os_kernel,
        osVersion: rportClient.os_version,
        osFamily: rportClient.os_family,
        ipv4: rportClient.ipv4,
        deleted: false,
        updatesStatus: newUpdateStatus,
        rebootPending: rportClient.updates_status?.reboot_pending ?? false,
        updatesRefreshedAt: rportClient.updates_status?.refreshed,
        monitoringEnabled: rportClient.client_configuration?.monitoring?.enabled || false,
      },
    },
    { upsert: true, new: true }
  );

  // Add tags, if requested
  if (tags && !_.isEmpty(tags?.trim()) && tags?.trim() !== 'undefined') {
    const tagsList = tags.split(',');

    if (tagsList.length > 0) {
      await updateTags(clientId, tagsList);
    }
  }

  postUpdateOrCreateHost(client, clientEmail ? clientEmail : 'AUTO_INSTALL');

  return ServiceResponse.delete().send(res);
});

export const removePlatformFromClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  const platformId = new Types.ObjectId(req.params.platformId);

  // Remove platform from the client
  const client = await RportClient.findOneAndUpdate(
    { rportId: clientId, enabled: true, deleted: false },
    { $pull: { platforms: { id: platformId } } }
  );

  // async process to avoid blocking the request
  process.nextTick(async () => {
    if (!client) return;

    try {
      // Update Proactivity Scores
      await updateProactivityScores([clientId]);
      Logger.info('Proactivity scores updated successfully for host:', clientId);
    } catch (error) {
      Logger.error('Error updating proactivity scores:', error);
    }
  });

  return ServiceResponse.delete().send(res);
});

export const addOrUpdatePlatformToClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  let platformId: Types.ObjectId = new Types.ObjectId(req.params.platformId);

  // agentId is how the device is identified in the platform
  // so its value will be different for each platform
  const { status, agentId } = req.query as {
    status: 'RUNNING' | 'NOT_RUNNING' | 'NOT_AVAILABLE';
    agentId: string;
  };

  // These parameters can be used to identify the correct platform ID,
  // in cases when there is more than one platform of the same tool.
  // For example, a CS Pro and a CS Premium.
  const { cid, template } = req.body as { cid?: string; template?: string };

  // Getting client
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });
  if (!client) throw errors.rport_host_not_found();

  // async process to avoid blocking the request
  process.nextTick(async () => {
    if (!client) return;

    try {
      // Update Proactivity Scores
      await updateProactivityScores([clientId]);
      Logger.info('Proactivity scores updated successfully for host:', clientId);
    } catch (error) {
      Logger.error('Error updating proactivity scores:', error);
    }
  });

  // Getting platform
  let platform = await RportPlatform.findOne({ _id: platformId, deleted: false });

  if (cid && template) {
    if (!platform) {
      // Attempt to find platform by tool and cid
      platform = await RportPlatform.findOne({
        template,
        'parameters.clientId.value': { $regex: cid, $options: 'i' },
        deleted: false,
      });
    } else {
      // If platform exists, check that it has the same cid
      if (
        !platform.parameters.clientId ||
        platform.parameters.clientId.value.toUpperCase() !== cid.toUpperCase()
      ) {
        // If not, find the correct one
        platform = await RportPlatform.findOne({
          template,
          'parameters.clientId.value': { $regex: cid, $options: 'i' },
          deleted: false,
        });
      }
    }
  }

  // If not platform is found, remove it from the host
  if (!platform) {
    Logger.error('Platform not found! Removing it from the host...', { platformId, cid, template });

    // Remove platform from the client
    await RportClient.findOneAndUpdate(
      { rportId: clientId },
      { $pull: { platforms: { id: platformId } } }
    );

    return ServiceResponse.delete().send(res);
  }

  platformId = platform._id;

  const currentPlatformIndex = client.platforms.findIndex(
    (plat) => String(plat.id) === String(platformId)
  );

  if (currentPlatformIndex !== -1) {
    // Saving platform in a variable
    const currentPlatform = client.platforms[currentPlatformIndex];

    // Updating platform object
    if (!currentPlatform.status || currentPlatform.status !== status) {
      // If the previous status was NOT_AVAILABLE, attempt to renew the agentId
      if (currentPlatform.status === 'NOT_AVAILABLE') {
        try {
          currentPlatform.agentId = await getAgentIdInPlatform(platformId, agentId);
        } catch (error) {
          Logger.error(`Error trying to get the agent ID for the platform: ${error}`);
        }
      }

      currentPlatform.status = status;
      currentPlatform.lastChangeAt = new Date();

      // If the new status is NOT_AVAILABLE, attempt to remove the device from the platform
      if (status === 'NOT_AVAILABLE') {
        try {
          await removeDeviceFromPlatform(platformId, currentPlatform.agentId);
        } catch (error) {
          Logger.error(`Error attempting to remove device from platform: ${error}`);
        }
      }
    }

    // Make sure the agentId is up to date
    if (['RUNNING', 'NOT_RUNNING'].includes(status)) {
      try {
        let platformAgentId = '';
        platformAgentId = await getAgentIdInPlatform(platformId, agentId);
        currentPlatform.agentId = platformAgentId;
      } catch (error) {
        Logger.error(`Error trying to get the agent ID for the platform: ${error}`);
      }
    }

    // In any case we want to turn off the running process
    currentPlatform.running = false;
    currentPlatform.checkedAt = new Date();

    await RportClient.findOneAndUpdate(
      { rportId: clientId, 'platforms.id': platformId, deleted: false },
      {
        $set: { 'platforms.$': currentPlatform },
      }
    );
  } else {
    // If the platform is not present, and the service is present, add it
    if (['RUNNING', 'NOT_RUNNING'].includes(status)) {
      let platformAgentId = '';
      const _date = new Date();

      try {
        platformAgentId = await getAgentIdInPlatform(platformId, agentId);
      } catch (error) {
        Logger.error(`Error trying to get the agent ID for the platform: ${error}`);
      }

      await RportClient.findOneAndUpdate(
        { rportId: clientId, deleted: false },
        {
          $push: {
            platforms: {
              id: platformId,
              status: status, // Default status
              checkedAt: _date, // Default value for last check (The moment it register the platform for the first time)
              lastChangeAt: _date, // default value its the first status (The moment it register the platform for the first time)
              agentId: platformAgentId,
              running: false,
              startedAt: _date,
            },
          },
        }
      );
    }
  }

  return ServiceResponse.delete().send(res);
});

export const getInstallCommand: RequestHandler = catchAsync(async (req, res) => {
  const { id } = req.query as { id: string };
  if (!id || id.trim().length === 0) {
    throw errors.params(['id']);
  }

  let clientAuth: RportClientAuth | undefined;
  let password: string | undefined;
  let count = 0;

  while (!clientAuth && count < 5) {
    try {
      clientAuth = await getClientAuth(id);
      if (!clientAuth) {
        Logger.info(`RPort client auth with ID ${id} not found, trying to create it...`);
        try {
          password = randomPasswordRport();
          await createClientAuth(id, password);
          clientAuth = { id, password };
        } catch (error: any) {
          Logger.info(`RPort client auth with ID ${id} already exists. Error: ${error}`);
        }
      }
    } catch (error: any) {
      Logger.info(
        `RPort client auth with ID ${id} not found, trying to create it. Error: ${error}`
      );
      try {
        password = randomPasswordRport();
        await createClientAuth(id, password);
        clientAuth = { id, password };
      } catch (error: any) {
        Logger.info(`RPort client auth with ID ${id} already exists. Error: ${error}`);
      }
    }
    if (!clientAuth) {
      await delay(1000);
      count++;
    }
  }

  password = clientAuth?.password;

  if (!password) {
    throw errors.not_found(`RPort client auth for ID ${id}`);
  }

  // obtener el installer
  const installer = await getInstaller(id, password);

  return ServiceResponse.get(installer).send(res);
});

export const updateClientsFromRport: RequestHandler = catchAsync(async (_, res) => {
  const updateCount = await updateClients();
  return ServiceResponse.get({ updateCount }).send(res);
});
export const updateClientUpdatesFromRport: RequestHandler = catchAsync(async (_, res) => {
  const updateCount = await updateClientsOsUpdates();
  return ServiceResponse.get({ updateCount }).send(res);
});

export const removeOldClientsFromRport: RequestHandler = catchAsync(async (_, res) => {
  const removeCount = await removeOldClients();
  return ServiceResponse.get({ removeCount }).send(res);
});

export const updateHostStatus: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;

  // Fetch the current status of the host
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });
  if (!client) throw errors.not_found('Host');

  // Switch the enabled status
  const newStatus = !client.enabled;

  // Update the host with the new status
  const updatedClient = await RportClient.findOneAndUpdate(
    { rportId: clientId },
    { $set: { enabled: newStatus } },
    { new: true }
  );

  // async process to avoid blocking the request
  process.nextTick(async () => {
    if (!client) return;

    try {
      // Update Proactivity Scores
      await updateProactivityScores([clientId]);
      Logger.info('Proactivity scores updated successfully for host:', clientId);
    } catch (error) {
      Logger.error('Error updating proactivity scores:', error);
    }
  });

  return ServiceResponse.get({ client: updatedClient }).send(res);
});

export const updateHostUpdates: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;

  // Fetch the current status of the host
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });
  if (!client) throw errors.not_found('Host');

  const updatedClient = await updateSingleClientOsUpdates(clientId);

  // async process to avoid blocking the request
  process.nextTick(async () => {
    if (!client) return;

    try {
      // Update Proactivity Scores
      await updateProactivityScores([clientId]);
      Logger.info('Proactivity scores updated successfully for host:', clientId);
    } catch (error) {
      Logger.error('Error updating proactivity scores:', error);
    }
  });

  return ServiceResponse.get({ client: updatedClient }).send(res);
});

export const bulkUpdateHostStatus: RequestHandler = catchAsync(async (req, res) => {
  const { hostIds, status } = req.body as { hostIds: string[]; status: boolean };

  if (hostIds.length === 0) throw errors.empty_list('Hosts');
  if (hostIds.length > 500) throw errors.max_elements_exceeded('Hosts', 500);

  // Update the hosts with the new status
  await RportClient.updateMany({ rportId: { $in: hostIds, deleted: false } }, { enabled: status });

  // Fetch the updated hosts
  const updatedClients = await RportClient.find({ rportId: { $in: hostIds }, deleted: false });

  return ServiceResponse.get({ clients: updatedClients }).send(res);
});

export const removeBatutaFromClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;

  // Removing from Rport
  await deleteClient(clientId, true, true);

  // Removing from elastic
  await removeHostInventory(clientId);

  // sending response back
  return ServiceResponse.delete().send(res);
});

export const manuallyRemoveBatutaFromClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;

  // Validate if the client exists
  const client = await RportClient.findOne({ rportId: clientId, deleted: false });
  if (!client) throw errors.not_found('Host');
  if (!client.enabled) throw errors.host_not_enabled();

  // Throw an error if the client is uninstalling
  if (client.uninstalling) throw errors.host_already_uninstalling();

  // Getting rport client
  const rportClient = await getClient(clientId);
  if (!rportClient) throw errors.not_found('Rport client');

  // If the client is disconnected, throw an error
  if (rportClient.connection_state === 'connected') throw errors.host_is_connected();

  await deleteClient(clientId, false, false);

  // Removing from elastic
  await removeHostInventory(clientId);

  // Removing Vulnerability Service
  await deleteHostFromVulnService(clientId);

  // sending response back
  return ServiceResponse.delete().send(res);
});

export const getStatus: RequestHandler = catchAsync(async (_, res) => {
  const status = await getServerStatus();
  // sending response back
  return ServiceResponse.get({ ok: !!status }).send(res);
});

export const getClientsCountPerLocation: RequestHandler = catchAsync(async (_, res) => {
  const stats = await RportIP.aggregate([
    {
      $group: {
        _id: '$location',
        city: {
          $first: '$city',
        },
        region: {
          $first: '$region',
        },
        country: {
          $first: '$country',
        },
        location: {
          $first: '$location',
        },
        ips: {
          $push: '$ip',
        },
      },
    },
    {
      $lookup: {
        from: 'rportclients',
        localField: 'ips',
        foreignField: 'address',
        as: 'clients',
        pipeline: [{ $match: { deleted: false } }, { $project: { _id: 1 } }],
      },
    },
    {
      $addFields: {
        quantity: {
          $size: '$clients',
        },
        coordinates: '$location.coordinates',
      },
    },
    {
      $match: {
        quantity: { $gt: 0 },
      },
    },
    {
      $project: {
        _id: 0,
        coordinates: 1,
        quantity: 1,
        city: 1,
        region: 1,
        country: 1,
      },
    },
  ]);

  // sending response back
  return ServiceResponse.get({ locations: stats }).send(res);
});

export const getClientsMetrics: RequestHandler = catchAsync(async (_, res) => {
  const serverStatus = await getServerStatus();
  if (!serverStatus || !serverStatus.data.data) {
    throw Error('Could not get server status');
  }

  const { clients_connected: connected, clients_disconnected: disconnected } =
    serverStatus.data.data;

  // counting added host in last 30 days
  const latest = await getCountClientsAddedSince(Time.daysAgo(30));

  return ServiceResponse.get({
    total: connected + disconnected,
    connected,
    disconnected,
    latest,
  }).send(res);
});

export const getTotalClients: RequestHandler = catchAsync(async (_, res) => {
  // count all not deleted hosts
  const total = await RportClient.countDocuments({ deleted: false });

  return ServiceResponse.get({ totalHosts: total }).send(res);
});

export const getOsClientCount: RequestHandler = catchAsync(async (_, res) => {
  const osArray = await getClientsCountByOS();

  const osObject: { [key: string]: number } = {};

  // Transforming array to object
  osArray.forEach((os) => {
    osObject[os.os] = os.count;
  });

  return ServiceResponse.get(osObject).send(res);
});

export const getUpdatesStatus: RequestHandler = catchAsync(async (_, res) => {
  const { hostSummary, vulnerabilitiesSummary } = await getOsUpdateSummary();

  return res.status(200).send({ host: hostSummary, vulnerabilities: vulnerabilitiesSummary });
});

export const getTopUpdatesStatus: RequestHandler = catchAsync(async (_, res) => {
  const storedHosts = await RportClient.find({ deleted: false }).select('_id name updatesStatus');

  // Sort the hosts by securityUpdatesAvailable, then by updatesAvailable
  const sortedHosts = storedHosts.sort((a, b) => {
    const aUpdates = a.updatesStatus ?? [];
    const bUpdates = b.updatesStatus ?? [];

    // Get the number of security updates
    const aSecurityUpdates = aUpdates.filter((update) => update.isSecurityUpdate).length;
    const bSecurityUpdates = bUpdates.filter((update) => update.isSecurityUpdate).length;

    // Sort by security updates first
    if (aSecurityUpdates !== bSecurityUpdates) {
      return bSecurityUpdates - aSecurityUpdates;
    }

    // Get the number of updates
    const aUpdatesAvailable = aUpdates.length - aSecurityUpdates;
    const bUpdatesAvailable = bUpdates.length - bSecurityUpdates;

    // Sort by updates available if security updates are the same
    return bUpdatesAvailable - aUpdatesAvailable;
  });

  // Get the top 10 hosts
  const topVulnerableHosts = sortedHosts.slice(0, 10);

  return res.status(200).send({ hosts: topVulnerableHosts });
});

export const getAgentVersion: RequestHandler = catchAsync(async (_, res) => {
  // Send env var value
  res.status(200).send(RPORT_AGENT_VERSION);
});

export const getPairingBaseURL: RequestHandler = catchAsync(async (_, res) => {
  // Send env var value
  res.status(200).send(PAIRING_BASE_URL);
});

export const batutaUninstallOnMultipleClients: RequestHandler = catchAsync(async (req, res) => {
  const batutaPlatform = await BatutaCommand.findOne({ name: 'batuta-uninstall' });
  const { useQueue = false, clientIds = [] } = req.body as {
    useQueue?: boolean;
    clientIds: string[];
  };
  const email = req.user?.email;

  if (!batutaPlatform) throw errors.command_not_supported();

  const { clientIdsConnected, clientIdsDisconnected } =
    await getAndValidateClientsConnected(clientIds);

  let results: { jid: string | null }[] = [{ jid: null }];

  if (!useQueue && clientIdsConnected.length === 0) {
    return ServiceResponse.get({
      message:
        "You don't use queues, but all hosts are disconnected, if you want to uninstall Batuta from those hosts, you will need to use queues",
    }).send(res);
  }

  if (!useQueue && clientIdsDisconnected.length > 0) {
    throw errors.batuta_uninstall_clients_disconnected_and_not_use_queue();
  }

  if (clientIdsConnected.length > 0) {
    results = await executeBatutaCommandOnClientsOrGroups(batutaPlatform, clientIdsConnected);

    await RportClient.updateMany(
      { rportId: { $in: clientIdsConnected }, deleted: false },
      { uninstalling: true, uninstallingStartingTime: new Date() }
    );
  }

  const rportJob = await RportJob.create({
    jobId: results[0].jid,
    jobIds: results[0].jid === null ? [] : results.map(({ jid }) => jid),
    author: email,
    command: 'batuta-uninstall',
    timeout: 60,
    os: 'multiple',
    platform: null,
    clients: clientIdsConnected,
    clientsInQueue: useQueue ? clientIdsDisconnected : [],
    useQueue,
    is_batuta_command: true,
  });

  if (useQueue && clientIdsDisconnected.length > 0) {
    await TasksFunctions.createTask(
      TASK_NAMES.UNINSTALL_BATUTA,
      1,
      {
        selectedHosts: clientIds,
        affectedHosts: clientIdsConnected,
        rportJobId: rportJob.id,
      },
      { author: email }
    );

    await RportClient.updateMany(
      { rportId: { $in: clientIdsDisconnected }, deleted: false },
      { uninstalling: true }
    );
  }

  return ServiceResponse.get({ id: rportJob.id }).send(res);
});

export const rebootClient: RequestHandler = catchAsync(async (req, res) => {
  const clientId = req.params.id;
  const email = req.user?.email;

  const storedClient = await RportClient.findOne({
    rportId: clientId,
    deleted: false,
    enabled: true,
  });
  if (!storedClient) throw errors.not_found('Rport client');

  const rportClient = await getClient(storedClient.rportId);
  if (!rportClient) throw errors.not_found('Rport client');
  if (rportClient.connection_state !== 'connected') throw errors.rport_host_disconnected();

  // Get the reboot command
  const rebootCommand = getRebootCommand(storedClient);

  // Get the interpreter
  const interpreter = storedClient.osKernel === 'windows' ? 'powershell' : 'bash';
  const cwd = storedClient.osKernel === 'windows' ? 'C:\\Windows\\Temp' : '/tmp';

  // Run the reboot command
  const result = await executeCommandOnClientsOrGroups(
    [storedClient.rportId],
    [],
    rebootCommand,
    500,
    interpreter,
    cwd
  );

  const rportJob = await RportJob.create({
    jobId: result.jid,
    jobIds: [],
    author: email,
    command: rebootCommand,
    timeout: 60,
    os: storedClient.osKernel,
    platform: null,
    clients: [storedClient.rportId],
    clientsInQueue: [],
    useQueue: false,
    is_batuta_command: true,
  });

  return ServiceResponse.get(rportJob).send(res);
});

export const getHostProcesses: RequestHandler = catchAsync(async (req, res) => {
  // Get the Host ID
  const { id } = req.params as { id: string };
  let response: { processes: ProcessModel[]; timestamp: string } = {
    processes: [],
    timestamp: new Date().toISOString(),
  };

  try {
    // Validate the Host ID (non deleted, enabled)
    await validateHost(id);

    // Get if the host is online
    await validateHostConnection(id);

    // Get the processes from rport
    response = await getHostProcessesById(id);
  } catch (error) {
    Logger.error('Error getting host processes', error);
  }

  // Return the processes
  return ServiceResponse.get(response).send(res);
});

export const getProcessesMetrics: RequestHandler = catchAsync(async (req, res) => {
  // Get the Host ID
  const { id } = req.params as { id: string };

  try {
    // Validate the Host By ID (non deleted, enabled)
    await validateHost(id);

    // Get if the host is online
    await validateHostConnection(id);

    // Get process metrics
    const [cpuUsage, memoryUsage, lanUsage, bpsLanUsage] = await Promise.all([
      getClientCPUUsage(id),
      getClientMemoryUsage(id),
      getClientLanUsage(id),
      getClientBpsLanUsage(id),
    ]);

    // Format the metrics in a single object
    const metrics = {
      cpuUsage: cpuUsage.data[0]?.cpu_usage_percent,
      memoryUsage: memoryUsage.data[0]?.memory_usage_percent,
      lanUsage: lanUsage.data[0]?.net_usage_percent_lan,
      bpsLanUsage: bpsLanUsage.data[0]?.net_usage_bps_lan,
    };

    // Return the metrics
    return ServiceResponse.get({ metrics }).send(res);
  } catch (error) {
    Logger.error('Error getting host metrics', error);
    return ServiceResponse.get({ metrics: {} }).send(res);
  }
});

export const setProcessDesiredState: RequestHandler = catchAsync(async (req, res) => {
  // Get the Host ID
  const { id } = req.params as { id: string };

  // Validate the Host ID (non deleted, enabled)
  const host = await validateHost(id);

  // Get the request body
  const { process_name: pName, desired_state: desiredState } = req.body as {
    process_name: string;
    desired_state?: 'running' | 'stopped';
  };

  // Validate the desired state
  if (desiredState && !['running', 'stopped'].includes(desiredState)) {
    throw errors.invalid_field_value('desired_state', desiredState, ['running', 'stopped']);
  }

  let newState: ProcessDesiredStateDocument | undefined;
  if (!desiredState) {
    // Remove the desired state by the process name for the host
    await ProcessDesiredState.findByIdAndDelete({ hostId: host.rportId, processName: pName });
  } else {
    // Save or update the desired state by the process name for the host
    newState = await ProcessDesiredState.findOneAndUpdate(
      { hostId: host.rportId, processName: pName },
      { desiredState, hostId: host.rportId, processName: pName },
      { upsert: true, new: true }
    );
  }

  // Return the desired state
  return ServiceResponse.get({ desired_state: newState }).send(res);
});

export const stopProcesses: RequestHandler = catchAsync(async (req, res) => {
  // Get the Host ID
  const { id } = req.params as { id: string };

  const { processId } = req.body as { processId: number };

  // Validate the Host ID (non deleted, enabled)
  const host = await validateHost(id);

  // Get if the host is online
  await validateHostConnection(id);

  // Define Author
  const author = req.user?.email;
  if (!author) throw errors.not_allowed();

  // Stop the process
  await stopProcessesById(host, [processId], author);

  // Return the response
  return ServiceResponse.delete().send(res);
});

export const bulkCriticalityUpdate: RequestHandler = catchAsync(async (req, res) => {
  const { clientIds, criticality } = req.body as {
    clientIds: string[];
    criticality: string;
  };

  await RportClient.updateMany({ rportId: { $in: clientIds } }, { criticality });

  const modifiedClients = await RportClient.find({ rportId: { $in: clientIds } }).select('rportId');
  const clientUpdated = modifiedClients.map((client) => client.rportId);

  return ServiceResponse.patch({ clientIds: clientUpdated }).send(res);
});
