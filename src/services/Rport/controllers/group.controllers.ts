import _ from 'lodash';

import { RequestHand<PERSON> } from 'express';
import { Types } from 'mongoose';

import { RportGroup, ClientGroup, RportClient } from '../schemas';

import { GetAllQuery } from '@shared/types';

import { QueryModel } from '@shared/models';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';
import {
  getSuggestedValuesFromField,
  parseSchemaToFieldFormat,
} from '@shared/helpers/classes/schema-utils.class';

import { getClients } from '../helpers/connections/rport';

import {
  getHostCount,
  getHostPreview,
  parseClientFilters,
  getHostIdsFromGroup,
  validateGroupRules,
  getClientFilters,
} from '../helpers/functions';

import { LIST_CLIENT_FIELDS } from '../helpers/constants';
import { HOST_SKIPPED_FIELDS } from '../helpers/constants/groups';
import { ServiceResponse } from '@root/shared/models/service-response';

export const groupFilter: RequestHandler = catchAsync(async (_, res) => {
  const skippedFilter = ['description', 'rules', 'deleted', 'protected'];
  const [filter, fields] = await RportGroup.createFilter(undefined, skippedFilter);
  return ServiceResponse.get({ filter, fields }).send(res);
});

export const groupMemberFilters: RequestHandler = catchAsync(async (_, res) => {
  const [filter, fields] = await getClientFilters();
  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getAllGroups: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery] = RportGroup.parseFilter(filter, sort);

  const [groups, filteredResultsCount] = await Promise.all([
    await RportGroup.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0),
    await RportGroup.countDocuments(query),
  ]);

  const finalGroups = await Promise.all(
    groups.map(async (group) => {
      const count = await getHostCount(group._id);
      return {
        ...group.toObject(),
        count: count || 0,
      };
    })
  );

  return ServiceResponse.get({
    meta: { count: filteredResultsCount, resources: groups.length, offset: +(offset ?? 0) },
    data: finalGroups,
  }).send(res);
});

export const getOneGroup: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;

  const group = await RportGroup.findById(groupId);
  if (!group) throw errors.not_found('Group');

  const count = await getHostCount(group._id);

  return ServiceResponse.get({
    ...group.toObject(),
    count: count || 0,
  }).send(res);
});

export const createGroup: RequestHandler = catchAsync(async (req, res) => {
  const { name, description, type } = req.body as {
    name: string;
    description?: string;
    type: 'STATIC' | 'DYNAMIC';
  };

  const existingGroup = await RportGroup.findOne({ name: name });
  if (existingGroup) throw errors.already_exists('Group');

  const newGroup = await RportGroup.create({
    name,
    description,
    type,
    updatedBy: req.user?.email,
    createdBy: req.user?.email,
  });

  return ServiceResponse.post({ group: newGroup }).send(res);
});

export const updateGroup: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;

  const { name, description } = req.body as { name: string; description: string };

  await RportGroup.findByIdAndUpdate(
    groupId,
    { ...{ name, description }, updatedBy: req.user?.email },
    { runValidators: true, new: true }
  );

  const group = await RportGroup.findById(groupId);
  if (!group) throw errors.not_found('Group');

  return ServiceResponse.get({ group }).send(res);
});

export const updateHostInGroup: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;
  const { clientIds } = req.body as { clientIds: Types.ObjectId[] };
  const { action } = req.query as { action: 'PUSH' | 'PULL' };

  // replace rules with new rules
  const group = await RportGroup.findById(groupId);
  if (!group) throw errors.not_found('Group');

  if (group.type === 'DYNAMIC') throw errors.invalid_assignation();

  // Get valid clients
  const validClientIds = await RportClient.find({
    rportId: { $in: clientIds },
    deleted: false,
  }).distinct('rportId');

  // get list of host inside the group
  const hostInGroup = await ClientGroup.find({ groupId: group._id }).distinct('clientId');

  // Validate by action
  if (action === 'PUSH') {
    // Get the difference between the valid clients from the host inside the group
    const hostDifference = _.differenceWith(validClientIds, hostInGroup, _.isEqual);
    // pushing the diferente to the group
    await ClientGroup.create(hostDifference.map((id) => ({ clientId: id, groupId: group._id })));
  } else if (action === 'PULL') {
    // Get the intersection between valid clients inside the group
    const hostIntersection = _.intersectionWith(hostInGroup, validClientIds, _.isEqual);
    // removing the intersection from the group
    await ClientGroup.deleteMany({ clientId: { $in: hostIntersection } });
  } else {
    throw errors.invalid_static_group_action();
  }

  // get list of host inside the group
  const count = await ClientGroup.countDocuments({ groupId: group._id });

  // Updating the group
  const updatedGroup = await RportGroup.findOneAndUpdate(
    { _id: group._id },
    { updatedBy: req.user?.email || group.updatedBy },
    { new: true }
  );
  if (!updatedGroup) throw errors.not_found('Group');

  return ServiceResponse.get({
    ...group.toObject(),
    count: count,
  }).send(res);
});

export const updateAssignmentRules: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;
  const { rules } = req.body as {
    rules: QueryModel;
  };

  // validating rules
  if (Object.keys(rules).length === 0) throw errors.not_valid('Rules');
  if (!validateGroupRules(rules)) throw errors.not_valid('Rules');

  // Replace rules with new rules
  const group = await RportGroup.findOneAndUpdate(
    { _id: groupId, type: 'DYNAMIC' },
    { rules: rules, updatedBy: req.user?.email },
    { runValidators: true, new: true }
  );

  if (!group) throw errors.not_found('Group');

  return ServiceResponse.get({ group }).send(res);
});

export const removeGroup: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;

  const group = await RportGroup.findById(groupId);
  if (!group) throw errors.not_found('Group');

  // if group is static must remove from client-groups
  if (group.type === 'STATIC') {
    await ClientGroup.deleteMany({ groupId: group.id });
  }

  await group.deleteOne();

  return ServiceResponse.delete().send(res);
});

export const getMembers: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;

  const group = await RportGroup.findById(groupId);
  if (!group) throw errors.not_found('Group');

  let { limit, offset } = req.query as GetAllQuery;
  const { filter, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = RportClient.parseFilter(filter, sort, [
    'connection_state',
    'platforms',
    'tags',
  ]);

  // getting filters (Platforms, Tags & rport filters)
  const [extraQuery, rportQuery] = await parseClientFilters(extraFilter);

  const hostIds = await getHostIdsFromGroup(group._id);
  // getting clients with the filters applied
  const storedClients = await RportClient.find(
    { $and: [query, extraQuery, { rportId: { $in: hostIds } }] },
    '_id rportId platforms tags createdAt'
  )
    .populate('platforms.id', 'name')
    .populate('tags', 'name');
  const storedClientIds = storedClients.map((client) => client.rportId);

  const total = storedClientIds.length;
  if (!!!offset) offset = 0;
  if (!!!limit) limit = 100;

  // If no stored clients are found, avoid sending the request to rport
  if (total === 0) {
    return ServiceResponse.get([], 0, +offset).send(res);
  }

  let rportFilter = {};
  if (rportQuery['connection_state']) {
    rportFilter = { connection_state: rportQuery['connection_state'] };
  }

  const rportClients = await getClients(
    LIST_CLIENT_FIELDS,
    { id: storedClientIds, ...rportFilter },
    limit.toString(),
    offset.toString(),
    sort ? sort : 'name'
  );

  const clients = rportClients.data;

  //  putting the data inside the client payload
  const joinedClients = clients.map((client) => {
    // search the client on stored clients
    const storedClient = storedClients.find((storedClient) => storedClient.rportId === client.id);

    if (!storedClient) return client;
    return {
      ...client,
      tags: storedClient.tags ?? [],
      platforms: storedClient.platforms ?? [],
      batutaId: storedClient._id,
      createdAt: storedClient.createdAt,
    };
  });

  return ServiceResponse.get(joinedClients, rportClients.meta.count, +offset).send(res);
});

export const getNewMembers: RequestHandler = catchAsync(async (req, res) => {
  const groupId = req.params.id;

  const group = await RportGroup.findById(groupId);
  if (!group) throw errors.not_found('Group');

  let { limit, offset } = req.query as GetAllQuery;
  const { filter, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = RportClient.parseFilter(filter, sort, [
    'connection_state',
    'platforms',
    'tags',
  ]);

  // getting filters (Platforms, Tags & rport filters)
  const [extraQuery, rportQuery] = await parseClientFilters(extraFilter);

  const hostIds = await getHostIdsFromGroup(group._id);
  // getting clients with the filters applied
  const storedClients = await RportClient.find(
    { $and: [query, extraQuery, { rportId: { $nin: hostIds } }] },
    '_id rportId platforms tags createdAt'
  )
    .populate('platforms.id', 'name')
    .populate('tags', 'name');
  const storedClientIds = storedClients.map((client) => client.rportId);

  const total = storedClientIds.length;
  if (!!!offset) offset = 0;
  if (!!!limit) limit = 100;

  // If no stored clients are found, avoid sending the request to rport
  if (total === 0) {
    return res.status(200).json({
      meta: { total, resources: 0, offset: +offset },
      data: [],
    });
  }

  let rportFilter = {};
  if (rportQuery['connection_state']) {
    rportFilter = { connection_state: rportQuery['connection_state'] };
  }

  const rportClients = await getClients(
    LIST_CLIENT_FIELDS,
    { id: storedClientIds, ...rportFilter },
    limit.toString(),
    offset.toString(),
    sort ? sort : 'name'
  );

  const clients = rportClients.data;

  //  putting the data inside the client payload
  const joinedClients = clients.map((client) => {
    const storedClient = storedClients.find((storedClient) => storedClient.rportId === client.id);

    if (!storedClient) return client;

    return {
      ...client,
      tags: storedClient.tags ?? [],
      platforms: storedClient.platforms ?? [],
      batutaId: storedClient._id,
      createdAt: storedClient.createdAt,
    };
  });

  return ServiceResponse.get(joinedClients, rportClients.meta.count, +offset).send(res);
});

export const previewMembers: RequestHandler = catchAsync(async (req, res) => {
  const { rules } = req.body as { rules: QueryModel };

  const clientCounter = await getHostPreview(rules);

  res.status(200).json({ count: clientCounter });
});

export const removeManyGroups: RequestHandler = catchAsync(async (req, res) => {
  const { group } = req.query as { group: string | string[] };

  if (!group) throw errors.not_valid('Group');

  await RportGroup.deleteMany({ _id: Array.isArray(group) ? { $in: group } : group });

  // removing from client-group
  await ClientGroup.deleteMany({ groupId: Array.isArray(group) ? { $in: group } : group });

  res.status(204).json({});
});

export const getAvailableAssignmentRules: RequestHandler = catchAsync(async (req, res) => {
  const availableRules = parseSchemaToFieldFormat(RportClient.schema, HOST_SKIPPED_FIELDS);
  res.status(200).json({ rules: availableRules });
});

export const getSuggestValues: RequestHandler = catchAsync(async (req, res) => {
  const { field, query } = req.query as { field: string; query: string };

  const suggestedValues = await getSuggestedValuesFromField(field, query, RportClient);

  res.status(200).json({ values: suggestedValues });
});
