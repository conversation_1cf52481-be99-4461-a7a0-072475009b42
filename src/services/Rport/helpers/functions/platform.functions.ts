import { capitalize } from 'lodash';

import { ServiceDocument } from '@shared/schemas';
import {
  RportClient,
  RportClientDocument,
  RportPlatform,
  RportPlatformDocument,
  RportJob,
  RportPlatformTemplate,
} from '@services/Rport/schemas';

import { BooleanRelative, FilterType, SelectRelative } from '@shared/models';

import { Logger } from '@shared/helpers/classes/logger.class';
import { delay } from '@shared/utils/delay';

import { executeCommandOnClient } from '../connections/rport';
import TasksFunctions from '@services/Queues/helpers/functions/task.function';

// import { RportConfigStore } from '../classes/';
import { getOsVersion } from './client.functions';
import { CommandSet } from '../models';

import { RPORT_AUTOINSTALL_PLATFORMS } from '@shared/constants/env';
import { TASK_NAMES } from '@services/Queues/helpers/constants/tasks';
import { getSummarizedPlatforms } from '../queries/rport-platform.queries';
import { buildExtraFilters } from '@root/shared/helpers/classes/schema-utils.class';

// This function send the check command to a client and gets the status on that device
export const checkPlatformOnClient = async (
  client: RportClientDocument,
  platform: RportPlatformDocument
) => {
  if (!client || !client.enabled || client.deleted) return false;
  if (!platform || !platform.enabled || platform.deleted) return false;

  const osVersion = getOsVersion(client);

  if (!osVersion) {
    Logger.warning(`OS version of client ${client.rportId} not supported. OS: ${client.os}`);
    return false;
  }

  if (!platform.commands['checkStatus']?.[client.osKernel]?.[osVersion]) {
    Logger.warning(
      `Platform Status Check: OS version ${osVersion} of client ${client.rportId} not supported or command not found in platform ${platform.name}`
    );
    return false;
  }

  // ejecutar el comando en el cliente
  try {
    await executeCommandOnClient(
      client.rportId,
      platform.getParsedCommand('checkStatus', client.osKernel, osVersion),
      90,
      client.osKernel === 'windows'
        ? 'powershell'
        : client.osKernel === 'linux'
          ? 'bash'
          : undefined,
      client.osKernel === 'windows' ? 'C:\\Windows\\Temp' : '/tmp'
    );
  } catch (err: any) {
    if (err.status >= 500) {
      // Stop the process and throw err for email
      // const config = await RportConfigStore.getInstance();
      // Removing abort process
      // await config.abortProcess();
      // Log if error is >=500
      Logger.error(
        `Platform Status Check Error for client ${client.rportId} and platform ${platform.name}: ${err}`
      );
    }
    return;
  }
};

// This function installs the default platforms when a new client registers
export const addDefaultPlatformsToClient = async (client: RportClientDocument) => {
  if (!client) return;

  if (RPORT_AUTOINSTALL_PLATFORMS) {
    const osVersion = getOsVersion(client);

    if (!osVersion) {
      Logger.error(
        `OS version of client ${client.rportId} not supported for platform auto installation. OS: ${client.os}`
      );
      return;
    }

    const platformIds = RPORT_AUTOINSTALL_PLATFORMS.split(',');

    for (let i = 0; i < platformIds.length; i++) {
      const platformId = platformIds[i].trim();

      try {
        const platform = await RportPlatform.findOne({
          _id: platformId,
          enabled: true,
          deleted: false,
        });

        if (!platform) {
          Logger.error(
            `Platform with id ${platformId} not found. Please make sure it's not disabled or deleted.`
          );
          continue;
        }

        const platformInClient = client.platforms.find((pl) => pl.id.equals(platform._id));

        if (platformInClient) {
          Logger.info(`Client ${client.rportId} already has or had platform ${platform.name}`);
          continue;
        }

        if (!platform.commands['install']) {
          Logger.info(`Platform ${platform.name} doesn't have the "install" command`);
          continue;
        }

        if (
          !platform.commands['install'] ||
          !platform.commands['install'][client.osKernel] ||
          !platform.commands['install'][client.osKernel][osVersion]
        ) {
          Logger.info(
            `OS version ${osVersion} of client ${client.rportId} not supported by platform ${platform.name}`
          );
          continue;
        }

        Logger.info(
          `Attempting to create installation task of platform ${platform.name} for client ${client.rportId}`
        );

        const timeout =
          platform.commands['install']?.[client.osKernel]?.[osVersion]?.timeout || 600;

        const rportJob = await RportJob.create({
          jobId: null,
          jobIds: [],
          author: 'AUTO_INSTALL',
          command: 'install',
          timeout,
          os: `${capitalize(client.osKernel)} (${capitalize(osVersion)})`,
          platform: platform.id,
          clients: [],
          clientsInQueue: [client.rportId],
          useQueue: true,
        });

        await TasksFunctions.createTask(
          TASK_NAMES.RUN_COMMAND,
          1,
          {
            selectedHosts: [client.rportId],
            affectedHosts: [],
            commandName: 'install',
            os_version: osVersion,
            os: client.osKernel,
            platformId: platform.id,
            rportJobId: rportJob.id,
          },
          { author: 'AUTO_INSTALL' }
        );

        await RportClient.findOneAndUpdate(
          {
            rportId: client.rportId,
            platforms: { $not: { $elemMatch: { id: platform.id } } }, // Just in case, checking that it doesn't have the platform already
            deleted: false,
          },
          {
            $push: {
              platforms: {
                id: platform.id,
                running: true,
                startedAt: new Date(),
              },
            },
          }
        );

        Logger.info(
          `Created installation task of platform ${platform.name} for client ${client.rportId}. Job ID: ${rportJob.id}`
        );
      } catch (error) {
        Logger.error(
          `Couldn't create install task of platform with ID ${platformId} for client ${client.rportId}: ${error}`
        );
      }
    }
  } else {
    Logger.info('RPORT_AUTOINSTALL_PLATFORMS is not set.');
  }
};

// This function checks for enabled platforms when a new client registers
export const checkEnabledPlatformsInClient = async (
  client: RportClientDocument,
  author: string = 'AUTO_INSTALL'
) => {
  if (!client) return;

  const osVersion = getOsVersion(client);

  if (!osVersion) {
    Logger.error(
      `OS version of client ${client.rportId} not supported for platform auto installation. OS: ${client.os}`
    );
    return;
  }

  // Only enabled platforms
  const platforms = await RportPlatform.find({
    enabled: true,
    deleted: false,
    isPlatform: true,
  });

  await Promise.all(
    platforms.map(async (platform) => {
      try {
        if (!platform.commands['checkStatus']) {
          Logger.info(`Platform ${platform.name} doesn't have the "checkStatus" command`);
        } else if (
          !platform.commands['checkStatus'][client.osKernel] ||
          !platform.commands['checkStatus'][client.osKernel][osVersion]
        ) {
          Logger.info(
            `OS version ${osVersion} of client ${client.rportId} not supported by "checkStatus" command of platform ${platform.name}`
          );
        } else {
          Logger.info(
            `Attempting to create status check task of platform ${platform.name} for client ${client.rportId}`
          );

          await TasksFunctions.createTask(
            TASK_NAMES.RUN_COMMAND,
            1,
            {
              selectedHosts: [client.rportId],
              affectedHosts: [],
              commandName: 'checkStatus',
              os_version: osVersion,
              os: client.osKernel,
              platformId: platform.id,
            },
            { author }
          );

          Logger.info(
            `Created status check task of platform ${platform.name} for client ${client.rportId}.`
          );
        }
      } catch (error) {
        Logger.error(
          `Couldn't create status check task of platform ${platform.name} for client ${client.rportId}: ${error}`
        );
      }
    })
  );
};

export const checkAndAddDefaultPlatformsInNewClient = async (
  client: RportClientDocument,
  author: string = 'AUTO_INSTALL'
) => {
  if (!client) return;

  await checkEnabledPlatformsInClient(client, author);

  // Wait a few seconds so the client can report what platforms it already has
  await delay(20000);

  await addDefaultPlatformsToClient(client);
};

export const statusValidator = (stdout: string) => {
  if (stdout.includes('RUNNING')) {
    return 'RUNNING';
  }
  if (stdout.includes('NOT_RUNNING')) {
    return 'STOPPED';
  }
  if (stdout.includes('NOT_AVAILABLE')) {
    return 'NOT_AVAILABLE';
  }
  return 'UNREACHABLE';
};

export const getSupportedOs = (commands: CommandSet, commandName: string) => {
  const commandObj = commands[commandName];
  return commandObj ? Object.keys(commandObj) : [];
};

export const getSupportedOsVersions = <T extends CommandSet>(commands: T, commandName: keyof T) => {
  const commandObj = commands[commandName];
  if (!commandObj) return [];

  return Object.keys(commandObj).flatMap((osType) => Object.keys(commandObj[osType]));
};

export const countClientsByStatus = async (
  platform: RportPlatformDocument,
  status: string
): Promise<number> => {
  return RportClient.countDocuments({
    deleted: false,
    ...(status === 'NOT_AVAILABLE'
      ? {
          $or: [
            { platforms: { $not: { $elemMatch: { id: platform.id } } } },
            { platforms: { $elemMatch: { id: platform.id, status: { $eq: status } } } },
          ],
        }
      : { platforms: { $elemMatch: { id: platform.id, status: { $eq: status } } } }),
  });
};

export const getTechnologyCoverageMetrics = async () => {
  const platforms = await RportPlatform.find(
    { deleted: false, enabled: true, isPlatform: true },
    {
      parameters: 0,
      apiConfig: 0,
    }
  ).sort('name');

  const total = await RportClient.countDocuments({ deleted: false });

  const counts: {
    [key: string]: {
      running: number;
      notRunning: number;
      notAvailable: number;
      id: string;
      licenses?: number;
    };
  } = {};

  for (let i = 0; i < platforms.length; i++) {
    const platform = platforms[i];
    // Counting clients from the collection using platform id
    const [running, notRunning, notAvailable] = await Promise.all([
      countClientsByStatus(platform, 'RUNNING'),
      countClientsByStatus(platform, 'NOT_RUNNING'),
      countClientsByStatus(platform, 'NOT_AVAILABLE'),
    ]);

    counts[platform.name] = {
      running,
      notRunning: notRunning,
      notAvailable: notAvailable,
      id: platform.id,
      licenses: platform?.licenses,
    };
  }

  const hostsWithNoPlatforms = await RportClient.countDocuments({
    deleted: false,
    platforms: { $size: 0 },
  });

  return { total, counts, hostsWithNoPlatforms };
};

export const getPlatformFilters = async () => {
  const skippedFilter = [
    'protected',
    'deleted',
    'commands',
    'parameters',
    'apiConfig',
    'isPlatform',
    'template',
  ];

  const serviceTemplates = await RportPlatformTemplate.find({
    deleted: false,
    enabled: true,
  }).populate<{ service: ServiceDocument }>('service');

  // Get Unique Service names
  const servicesMap = new Map();
  for (let i = 0; i < serviceTemplates.length; i++) {
    const t = serviceTemplates[i];
    if (!t.service?.name) continue;
    servicesMap.set(t.service.name, { key: t.service.name, value: t._id });
  }
  const services = Array.from(servicesMap.values());

  const extraFilters: { [key: string]: any } = {
    service: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: services,
    },
    requiresAttention: {
      type: FilterType.BOOLEAN,
      relatives: Object.values(BooleanRelative),
    },
  };

  return await RportPlatform.createFilter(extraFilters, skippedFilter);
};

export const _getPlatforms = async (
  filter: any,
  limit?: number,
  offset?: number,
  sort?: string
) => {
  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = RportPlatform.parseFilter(filter, sort, [
    'service',
    'requiresAttention',
  ]);
  const extra = buildExtraFilters(extraFilter);

  // Not returning parameters, apiConfig and commands for security and request size
  const [filteredResults, filteredResultsCount] = await getSummarizedPlatforms(
    limit !== undefined ? +limit : 100,
    offset ? +offset : 0,
    { $and: [query, extra] },
    sortQuery
  );

  return [filteredResults, filteredResultsCount] as const;
};
