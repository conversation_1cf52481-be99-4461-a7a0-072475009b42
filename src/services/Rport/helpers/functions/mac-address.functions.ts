import { RportMacAddress } from '../../schemas/rport-mac-address.schema';

export const updateMacAddresses = async (
  hostId: string,
  macAddresses: { name: string; mac: string; ipv4: string[]; ipv6: string[] }[]
) => {
  try {
    // Get all the MAC Addresses for the host
    const hostMacAddresses = await RportMacAddress.find({ rportId: hostId });

    // Determine the MAC Addresses to delete (present in DB but not in the new list)
    const toDelete = hostMacAddresses.filter(
      (dbRecord: any) => !macAddresses.some((item) => item.mac === dbRecord.mac)
    );

    // Delete MAC Addresses that are no longer in the received list
    if (toDelete.length > 0) {
      await RportMacAddress.deleteMany({
        _id: { $in: toDelete.map((record: any) => record._id) },
      });
    }

    // Determine the MAC Addresses to add (present in the new list but not in the DB)
    const toAdd = macAddresses
      .filter((item) => !hostMacAddresses.some((dbRecord: any) => dbRecord.mac === item.mac))
      // Include the host id to properly associate the record in the DB
      .map((item) => ({ ...item, rportId: hostId }));

    // Insert new MAC Addresses if any
    if (toAdd.length > 0) {
      await RportMacAddress.insertMany(toAdd, { ordered: false });
    }
  } catch (err) {
    throw err;
  }
};
