import _ from 'lodash';
import { Types } from 'mongoose';
import semverLt from 'semver/functions/lt';
import semverCoerce from 'semver/functions/coerce';

import {
  ClientGroup,
  RportClient,
  RportClientDocument,
  RportGroup,
  RportPlatform,
  RportTag,
} from '@services/Rport/schemas';

import { RportConfigStore } from '../classes/';

import { RportClientResponse, RportClientUpdateStatus, RportUpdateStatusModel } from '../types/';
import {
  ArrayRelative,
  FilterType,
  QueryFilterModel,
  RelativeType,
  SelectRelative,
  ShellType,
  StringRelative,
} from '@shared/models';

import { errors } from '@shared/utils/app-errors';
import { delay } from '@shared/utils/delay';
import { Time } from '@shared/helpers/classes/times.class';
import { Logger } from '@shared/helpers/classes/logger.class';
import { Gateway } from '@shared/helpers/classes/gateway.class';
import { getOSFromShellType } from '@shared/utils/shellType';

import { removeHostInventory } from '@services/Inventory/helpers/functions';

import {
  getClients,
  getClient,
  executeCommandOnClientsOrGroups,
  deleteDisconnectedClient,
  getOnlineClientIds,
  triggerOSUpdatesRefresh,
} from '../connections/rport';

import { checkPlatformOnClient } from './platform.functions';
import { countryConditionMaker, getCountryOptions } from './ip.functions';
import { getHostIdsFromGroup } from './group.functions';

import prescripts from '../prescripts';

import { getAndValidateClientsConnected } from '../utils/rport-platform.utils';

import { API_BASE_URL, CDN_BASE_URL, RPORT_AGENT_VERSION } from '@shared/constants/env';

import { ProactivityHostScore } from '@root/services/LevelOfProactivity/schemas';
import agentVersions from '../constants/agentVersions';
import {
  HOST_UPDATE_STATUS,
  UPDATED,
  UPDATES_AVAILABLE,
  SECURITY_UPDATES_AVAILABLE,
} from '../constants/os-update';
import rebootCommands from '../constants/reboot-commands';
import osPlatforms, { OS_LIST, SupportedVersionsByOS, TSupportedOS } from '../constants/platforms';
import { ALL_CLIENT_FIELDS, ACTIVE_CLIENT_FIELDS, MIN_CLIENT_FIELDS } from '../constants';
import { QUALIFICATIONS } from '@root/services/LevelOfProactivity/helpers/constants';
import { OS_UPDATES_UPDATE_FIELDS, RportClientField } from '../constants/clientFields';
import { updateMacAddresses } from './mac-address.functions';
import { QueuesTask } from '@root/services/Queues/schemas';
import { KnownQueuesTaskDocument } from '@root/services/Queues/schemas/task.schema';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
import { TaskParams } from '@root/services/Queues/helpers/types/task.types';
import { PENDING } from '@root/services/Queues/helpers/constants/status';
import { updateProactivityScores } from '@root/services/LevelOfProactivity/helpers/utils';

const RPORT_CLIENT_MAX_LIMIT = 500;

export const getRportHostWithBatches = async <T extends RportClientField[]>(
  filter: { [key: string]: string | string[] } = {},
  ids: string[] = [],
  offset: number | undefined = undefined,
  fields: T = MIN_CLIENT_FIELDS as T
): Promise<RportClientResponse<T>> => {
  // Getting Batches
  const idBatches = _.chunk(ids, RPORT_CLIENT_MAX_LIMIT);

  // Sending request to rport
  const promises = idBatches.map(async (idBatch, index) => {
    // Incremental Delay
    await delay(10 * index);

    // Merging rport filter with id batch
    const batchFilter = { ...filter, id: idBatch };

    // Sending request to rport
    return await getClients(fields, batchFilter, RPORT_CLIENT_MAX_LIMIT.toString(), '0', 'name');
  });

  // Waiting for all request to be done
  const results = await Promise.all(promises);

  // Merging results into one object
  const unifiedResult = results.reduce(
    (acc, rportClient) => {
      return {
        data: [...acc.data, ...rportClient.data],
        meta: { count: acc.meta.count + rportClient.meta.count },
      };
    },
    { data: [], meta: { count: 0 } }
  );

  // Applying offset to the unified result if offset exists
  let dataWithOffset = unifiedResult.data;
  if (offset && offset > 0) {
    dataWithOffset = [...unifiedResult.data.slice(offset)];
  }

  return {
    data: dataWithOffset,
    meta: { count: unifiedResult.meta.count },
  };
};

export const getClientsFromRport = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = ''
) => {
  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = RportClient.parseFilter(filter, sort, [
    'connection_state',
    'platforms',
    'scoring',
    'tags',
    'groups',
    'country',
    'updatesStatus',
  ]);

  // getting filters (Platforms, Tags, groups & rport filters)
  const [extraQuery, rportQuery] = await parseClientFilters(extraFilter);

  let total: number;
  let storedHosts: RportClientDocument[] = [];
  let storedHostsMap: Map<string, RportClientDocument>;
  let rportData: RportClientResponse<typeof MIN_CLIENT_FIELDS>;

  let rportFilter = {};
  if (rportQuery['connection_state']) {
    // Getting all host available for rport
    storedHosts = await RportClient.find({ $and: [query, extraQuery] })
      .sort(sortQuery ?? { name: 1 })
      .populate({
        path: 'platforms.id',
        select: 'name template',
        populate: {
          path: 'template',
          select: 'service',
          populate: { path: 'service', select: 'name internalName' },
        },
      })
      .populate('tags', 'name')
      .populate('scoring');

    // Setting rport filter
    rportFilter = { connection_state: rportQuery['connection_state'] };

    // Getting ids from map object
    storedHostsMap = new Map(storedHosts.map((host) => [host.rportId, host]));

    // Getting rport host data
    rportData = await getRportHostWithBatches(rportFilter, [...storedHostsMap.keys()], offset);

    // Setting total value
    total = rportData.meta.count;

    if (total === 0) {
      return {
        meta: { total, resources: 0, offset: +offset },
        data: [],
      };
    }
  } else {
    let queriedResults = RportClient.find({ $and: [query, extraQuery] })
      .sort(sortQuery ?? { name: 1 })
      .skip(offset)
      .populate('platforms.id', 'name')
      .populate('tags', 'name')
      .populate('scoring');
    if (!limit || limit > 0) {
      queriedResults = queriedResults.limit(limit ?? 100);
    }

    [storedHosts, total] = await Promise.all([
      queriedResults,
      RportClient.countDocuments({ $and: [query, extraQuery] }),
    ]);

    // If no stored hosts are found, avoid sending the request to rport
    if (total === 0) {
      return {
        meta: { total, resources: 0, offset: +offset },
        data: [],
      };
    }

    // Getting ids from map object
    storedHostsMap = new Map(storedHosts.map((host) => [host.rportId, host]));

    // Getting all selected host from rport
    rportData = await getRportHostWithBatches(rportFilter, [...storedHostsMap.keys()]);
  }

  let hosts = rportData.data;
  // Enforcing limit if it's not -1
  if (limit !== -1) {
    hosts = hosts.slice(0, limit <= RPORT_CLIENT_MAX_LIMIT ? limit : RPORT_CLIENT_MAX_LIMIT);
  }

  // Merging rport stored hosts with rport data hosts
  const joinedHosts = hosts.map((host) => {
    const storedHost = storedHostsMap.get(host.id);

    if (storedHost === undefined) return host;

    return {
      ...host,
      batutaId: storedHost._id,
      createdAt: storedHost.createdAt,
      tags: storedHost.tags ?? [],
      platforms: storedHost.platforms ?? [],
      name: storedHost.name,
      address: storedHost.address,
      os: storedHost.os,
      os_kernel: storedHost.osKernel,
      os_family: storedHost.osFamily,
      os_version: storedHost.osVersion,
      ipv4: storedHost.ipv4,
      scoring: storedHost.scoring,
      uninstalling: storedHost.uninstalling,
      uninstallingStartingTime: storedHost.uninstallingStartingTime,
      enabled: storedHost.enabled,
      criticality: storedHost.criticality,
    };
  });

  return {
    meta: { total, resources: joinedHosts.length, offset: +offset },
    data: joinedHosts,
  };
};

export const updateClients = async () => {
  // We need to iterate by 500 because it's RPort's max limit
  const limit = RPORT_CLIENT_MAX_LIMIT;
  const hostsToUpdateLoP: string[] = [];
  let page = 0;
  let updateCount = 0;

  let result = await getClients(ALL_CLIENT_FIELDS, {}, limit.toString(), `${page * limit}`);

  const fortyFiveDaysAgo = Time.daysAgo(45);

  while (result.data.length > 0) {
    // Updating saved clients with their information
    await Promise.all(
      result.data.map(async (client) => {
        // Getting the activity status for the current host
        const clientIsInactive = isClientInactive(client.disconnected_at, fortyFiveDaysAgo);

        // If the client is inactive, we should remove the host from rport
        if (clientIsInactive) {
          await deleteDisconnectedClient(client.id);
          // Remove the host from elastic
          await removeHostInventory(client.id);
        }

        // Parse Updates Status
        const newUpdateStatus: RportClientUpdateStatus[] =
          client.updates_status?.update_summaries?.map((update: RportUpdateStatusModel) => ({
            title: update.title,
            description: update.description ?? '',
            isSecurityUpdate: update.is_security_update,
            rebootRequired: update.reboot_required,
          })) ?? [];

        await RportClient.findOneAndUpdate(
          { rportId: client.id },
          {
            $set: {
              rportId: client.id,
              name: client.name,
              address: client.address,
              os: client.os_full_name,
              osKernel: client.os_kernel,
              osVersion: client.os_version,
              osFamily: client.os_family,
              ipv4: client.ipv4,
              deleted: clientIsInactive,
              updatesStatus: newUpdateStatus,
              rebootPending: client.updates_status?.reboot_pending ?? false,
              updatesRefreshedAt: client.updates_status?.refreshed,
              monitoringEnabled: client.client_configuration?.monitoring?.enabled || false,
            },
          },
          { upsert: true }
        );

        // Update MAC Addresses collection
        if (client.addrs) {
          try {
            await updateMacAddresses(client.id, client.addrs);
          } catch (error) {
            Logger.error(`Error when attempting to update MAC Addresses: ${error}`);
          }
        }

        updateCount++;
      })
    );

    page++;

    result = await getClients(ALL_CLIENT_FIELDS, {}, limit.toString(), `${page * limit}`);
  }

  if (hostsToUpdateLoP.length) {
    await updateProactivityScores(hostsToUpdateLoP);
  }

  return `RPort clients updated: ${updateCount}`;
};

export const updateSingleClientOsUpdates = async (hostId: string) => {
  // First, trigger os updates refresh
  await triggerOSUpdatesRefresh(hostId);

  // Then, obtain the refreshed os updates from the client
  let result;
  try {
    result = await getClient(hostId, OS_UPDATES_UPDATE_FIELDS);
  } catch (err) {
    Logger.error(`Error trying to get client: ${err}`);
  }

  if (!result) return;

  const { id, updates_status } = result;
  const newUpdateStatus =
    updates_status?.update_summaries?.map((update: RportUpdateStatusModel) => ({
      title: update.title,
      description: update.description ?? '',
      isSecurityUpdate: update.is_security_update,
      rebootRequired: update.reboot_required,
    })) ?? [];

  return await RportClient.findOneAndUpdate(
    { rportId: id },
    {
      $set: {
        updatesStatus: newUpdateStatus,
        rebootPending: updates_status?.reboot_pending ?? false,
        updatesRefreshedAt: updates_status?.refreshed,
      },
    },
    { new: true }
  );
};

export const updateClientsOsUpdates = async () => {
  // We need to iterate by 500 because it's RPort's max limit
  const limit = RPORT_CLIENT_MAX_LIMIT;
  let page = 0;
  let updateCount = 0;

  let result = await getClients(OS_UPDATES_UPDATE_FIELDS, {}, limit.toString(), `${page * limit}`);

  while (result.data.length > 0) {
    // Updating saved clients with their information
    await Promise.all(
      result.data.map(async ({ id, updates_status }) => {
        // Parse update status
        const newUpdateStatus =
          updates_status?.update_summaries?.map((update: RportUpdateStatusModel) => ({
            title: update.title,
            description: update.description ?? '',
            isSecurityUpdate: update.is_security_update,
            rebootRequired: update.reboot_required,
          })) ?? [];

        await RportClient.findOneAndUpdate(
          { rportId: id },
          {
            $set: {
              updatesStatus: newUpdateStatus,
              rebootPending: updates_status?.reboot_pending ?? false,
              updatesRefreshedAt: updates_status?.refreshed,
            },
          }
        );

        updateCount++;
      })
    );

    page++;

    result = await getClients(OS_UPDATES_UPDATE_FIELDS, {}, limit.toString(), `${page * limit}`);
  }

  return `RPort clients updated: ${updateCount}`;
};

export const isClientInactive = (
  disconnectDate: string | undefined,
  thresholdDate: Date
): boolean => {
  if (disconnectDate) {
    const disconnectedAt = new Date(disconnectDate);

    return disconnectedAt < thresholdDate;
  } else {
    return false;
  }
};

export const removeOldClients = async () => {
  const limit = 500;
  let page = 0;
  let removeCount = 0;

  const deletedClients = await RportClient.find({ deleted: true }).select('_id rportId');

  if (deletedClients.length) {
    await Promise.all(
      deletedClients.map(async (client) => {
        try {
          // Removing from rport
          await deleteDisconnectedClient(client.rportId, false);
          // Removing from elastic
          await removeHostInventory(client.rportId);

          removeCount++;
          return true;
        } catch (error: any) {
          Logger.warning(`Error removing client from RPort: ${error}`);
          return false;
        }
      })
    );
  }

  let clients = await RportClient.find({ deleted: false })
    .select('_id rportId')
    .limit(limit)
    .skip(page * limit);

  while (clients.length > 0) {
    await Promise.all(
      clients.map(async (client) => {
        try {
          const rportClient = await getClient(client.rportId);

          if (!rportClient) {
            await RportClient.findOneAndUpdate(
              { rportId: client.rportId },
              { $set: { deleted: true } }
            );
            await ClientGroup.deleteMany({ clientId: client.rportId });

            removeCount++;
          }
        } catch (error: any) {
          if (error.status === 404) {
            await RportClient.findOneAndUpdate(
              { rportId: client.rportId },
              { $set: { deleted: true } }
            );
            await ClientGroup.deleteMany({ clientId: client.rportId });

            removeCount++;
          }
        }
      })
    );

    page++;

    clients = await RportClient.find({ deleted: false })
      .select('_id rportId')
      .limit(limit)
      .skip(page * limit);
  }

  return `RPort clients removed: ${removeCount}`;
};

export const updateAgentInClients = async () => {
  // Output
  let output = 'No old agent versions found.';

  // Filter only agent versions previous to the version for this tenant
  const olderVersions = agentVersions.filter((v) =>
    semverLt(semverCoerce(v) || '0', semverCoerce(RPORT_AGENT_VERSION) || '0')
  );

  // Make sure there is at least one older version
  if (olderVersions.length > 0) {
    // Get Windows active hosts with older agent versions
    const activeOldWindowsClients = await getClients(
      ACTIVE_CLIENT_FIELDS,
      { version: olderVersions.join(','), connection_state: 'connected', os_kernel: 'windows' },
      '500'
    );

    if (activeOldWindowsClients.data.length > 0) {
      const updateTokenWin = await Gateway.getUpdateToken(activeOldWindowsClients.data.length);

      if (updateTokenWin) {
        let upgradeCommandWin = `Invoke-WebRequest -Uri "https://api.batuta.io/get-agent" -OutFile "BatutaAGInstallerCL.exe";Start-Process BatutaAGInstallerCL.exe '-a -t ${updateTokenWin} -u ${API_BASE_URL}' -Wait`;

        if (prescripts.windows) {
          upgradeCommandWin = prescripts.windows.addPrescripts(upgradeCommandWin);
        }

        try {
          await executeCommandOnClientsOrGroups(
            activeOldWindowsClients.data.map((c) => c.id),
            [],
            upgradeCommandWin,
            300,
            'powershell'
          );
          output = `Sent upgrade command to ${activeOldWindowsClients.data.length} Windows hosts.`;
        } catch (error: any) {
          Logger.warning(`Error sending upgrade command to Windows hosts: ${error}`);
          output = `Unable to send upgrade command to ${activeOldWindowsClients.data.length} Windows hosts.`;
        }
      }
    } else {
      output = 'No active Windows hosts found to upgrade.';
    }

    // Get active Linux hosts with older agent versions
    const activeOldLinuxClients = await getClients(
      ACTIVE_CLIENT_FIELDS,
      { version: olderVersions.join(','), connection_state: 'connected', os_kernel: 'linux' },
      '500'
    );

    if (activeOldLinuxClients.data.length > 0) {
      const updateTokenLin = await Gateway.getUpdateToken(activeOldLinuxClients.data.length);

      if (updateTokenLin) {
        let upgradeCommandLin = `curl -Ls https://api.batuta.io/get-agent?p=l -o batuta-installer.sh && sudo bash batuta-installer.sh -a -t ${updateTokenLin} -u ${API_BASE_URL}`;

        if (prescripts.linux) {
          upgradeCommandLin = prescripts.linux.addPrescripts(upgradeCommandLin);
        }

        try {
          await executeCommandOnClientsOrGroups(
            activeOldLinuxClients.data.map((c) => c.id),
            [],
            upgradeCommandLin,
            300,
            'bash'
          );
          output += `\nSent upgrade command to ${activeOldLinuxClients.data.length} Linux hosts.`;
        } catch (error: any) {
          Logger.warning(`Error sending upgrade command to Linux hosts: ${error}`);
          output += `\nUnable to send upgrade command to ${activeOldLinuxClients.data.length} Linux hosts.`;
        }
      }
    } else {
      output += '\nNo active Linux hosts found to upgrade.';
    }
  }

  return output;
};

export const enableMonitoringInClients = async () => {
  // Output
  let output = 'No online hosts found.';
  // Online hosts ids
  let onlineIds: string[] = [];

  // Get active Windows hosts with monitoring disabled
  const windowsHostIds = (
    await RportClient.find(
      {
        osKernel: 'windows',
        deleted: false,
        $or: [{ monitoringEnabled: false }, { monitoringEnabled: { $exists: false } }],
      },
      '_id rportId'
    )
      .limit(500)
      .lean()
  ).map((c) => c.rportId);

  try {
    onlineIds = await getOnlineClientIds();
  } catch (error) {
    Logger.error(`Unable to obtain online group from rport: ${error}`);
  }

  const onlineWindowsHostIds = windowsHostIds.filter((id) => onlineIds.includes(id));

  if (onlineWindowsHostIds.length) {
    let commandWin = `Invoke-WebRequest -Uri "${CDN_BASE_URL}/monitoring/windows/Enable-Monitoring.ps1" -OutFile "Enable-Monitoring.ps1"; .\\Enable-Monitoring.ps1`;

    if (prescripts.windows) {
      commandWin = prescripts.windows.addPrescripts(commandWin);
    }

    try {
      await executeCommandOnClientsOrGroups(
        onlineWindowsHostIds,
        [],
        commandWin,
        300,
        'powershell'
      );
      output = `Sent enable monitoring command to ${onlineWindowsHostIds.length} Windows hosts.`;

      // Preemptively update the monitoring status in the database
      await RportClient.updateMany(
        { rportId: { $in: onlineWindowsHostIds } },
        { monitoringEnabled: true }
      );
    } catch (error: any) {
      Logger.warning(`Error sending enable monitoring command to Windows hosts: ${error}`);
      output = `Unable to send enable monitoring command to ${onlineWindowsHostIds.length} Windows hosts.`;
    }
  } else {
    output = 'No active Windows hosts found.';
  }

  // Get active Linux hosts with monitoring disabled
  const linuxHostIds = (
    await RportClient.find(
      {
        osKernel: 'linux',
        deleted: false,
        $or: [{ monitoringEnabled: false }, { monitoringEnabled: { $exists: false } }],
      },
      '_id rportId'
    )
      .limit(500)
      .lean()
  ).map((c) => c.rportId);

  try {
    onlineIds = await getOnlineClientIds();
  } catch (error) {
    onlineIds = [];
    Logger.error(`Unable to obtain online group from rport: ${error}`);
  }

  const onlineLinuxHostIds = linuxHostIds.filter((id) => onlineIds.includes(id));

  if (onlineLinuxHostIds.length) {
    let commandLin = `_mo_script_path=$(mktemp -q /tmp/enable-mon-XXXXX.sh) && curl -Ls "${CDN_BASE_URL}/monitoring/linux/enable-monitoring.sh" -o $_mo_script_path && sudo bash $_mo_script_path; rm -rf $_mo_script_path`;

    if (prescripts.linux) {
      commandLin = prescripts.linux.addPrescripts(commandLin);
    }

    try {
      await executeCommandOnClientsOrGroups(onlineLinuxHostIds, [], commandLin, 300, 'bash');
      output += `\nSent enable monitoring command to ${onlineLinuxHostIds.length} Linux hosts.`;

      // Preemptively update the monitoring status in the database
      await RportClient.updateMany(
        { rportId: { $in: onlineLinuxHostIds } },
        { monitoringEnabled: true }
      );
    } catch (error: any) {
      Logger.warning(`Error sending enable monitoring command to Linux hosts: ${error}`);
      output += `\nUnable to send enable monitoring command to ${onlineLinuxHostIds.length} Linux hosts.`;
    }
  } else {
    output += '\nNo active Linux hosts found.';
  }

  return output;
};

export const composeQuery = (
  filters: { [key: string]: string | string[] | boolean } = {},
  include?: (Types.ObjectId | string)[],
  exclude?: (Types.ObjectId | string)[]
) => {
  // building query for platform filtering
  const query: { [key: string]: any } = {
    $and: [
      include?.length
        ? { platforms: { $elemMatch: { id: { $in: include }, status: { $ne: 'NOT_AVAILABLE' } } } }
        : {},
      exclude?.length
        ? {
            $or: [
              { 'platforms.id': { $nin: exclude } },
              { platforms: { $elemMatch: { id: { $in: exclude }, status: 'NOT_AVAILABLE' } } },
            ],
          }
        : {},
    ],
  };

  // Filtering name
  if ('name' in filters && filters.name) {
    query['name'] = { $regex: filters.name, $options: 'i' };
  }
  // Filtering by os kernel
  if ('os_kernel' in filters && filters.os_kernel) {
    query['osKernel'] = filters.os_kernel;
  }
  // Filtering by os version
  if ('os_version' in filters && filters.os_version) {
    if (query['osKernel'] && query['osKernel'] === 'linux') {
      query['osFamily'] = { $regex: filters.os_version, $options: 'i' };
    } else {
      query['os'] = { $regex: filters.os_version, $options: 'i' };
    }
  }

  if ('enabled' in filters && filters.enabled) {
    query['enabled'] = filters.enabled;
  } else {
    query['enabled'] = true; // default filter, searching only enabled host
  }

  if ('uninstalling' in filters) {
    query['uninstalling'] = filters.uninstalling;
  }

  if ('deleted' in filters && filters.deleted) {
    query['deleted'] = filters.deleted;
  } else {
    query['deleted'] = false; // default filter, searching only not deleted host
  }

  if ('tags' in filters && filters.tags) {
    if (Array.isArray(filters.tags)) {
      query['tags'] = { $in: filters.tags };
    } else {
      query['tags'] = filters.tags;
    }
  }

  return query;
};

export const getOsVersion = (client: RportClientDocument) => {
  if (!client) return;

  const kernel = client.osKernel.toLocaleLowerCase() as 'windows' | 'linux';
  const fullOsName = kernel === 'linux' ? client.osFamily : client.os;

  const osVersions = osPlatforms[kernel];

  if (!osVersions) return;

  return osVersions.find((version) =>
    fullOsName.toLocaleLowerCase().includes(version.toLocaleLowerCase())
  );
};

// This function get triggered by cron, indicates that its running, validate the service and validate each client on the infrastructure.
export const infrastructureScanner = async () => {
  // Get config
  let RportConfig = await RportConfigStore.getInstance();
  // Validating if the process can be ran
  if (!RportConfig.enabledStatus || RportConfig.runningStatus) {
    return `Host platforms status update process is disabled or already running`;
  }
  // Starting process
  await RportConfig.startProcess();

  // Calculating expiration date from current date and the checkingTime
  let expiringTime = new Date(+new Date() - RportConfig.time * 1000);
  // Get all clients if have platforms and by expiring date
  const clients = await RportClient.find({
    platforms: {
      $elemMatch: {
        checkedAt: { $lt: expiringTime },
        status: { $exists: true, $ne: 'NOT_AVAILABLE' },
      },
    },
    deleted: false,
  })
    .select('_id')
    .limit(50);

  // Looping through clients checking in every loop that the process can be ran
  for (let i = 0; i < clients.length; i++) {
    const client = clients[i];

    try {
      // Updating config
      RportConfig = await RportConfigStore.getInstance();
      expiringTime = new Date(+new Date() - RportConfig.time * 1000);
      if (!RportConfig.enabledStatus) {
        break;
      }
      // Here will the checking process
      await clientScanner(client._id, expiringTime, RportConfig.delay);
    } catch (error) {
      Logger.error(`Platform Status Check - Error looping through clients: ${error}`);
    }
  }

  // Closing process
  await RportConfig.closeProcess();

  return 'Host platforms status updated';
};

// This function receives the client and loops through its platforms validating them
const clientScanner = async (id: Types.ObjectId, expiringTime: Date, delayTime: number) => {
  // Getting the client
  const client = await RportClient.findById(id);
  if (!client) return;

  // Check if client is connected
  try {
    const rportClient = await getClient(client.rportId);
    if (!rportClient || rportClient.connection_state === 'disconnected') {
      return;
    }
  } catch (error: any) {
    if (error.status >= 500) {
      Logger.warning(`Platform Status Check - Error getting rport client: ${error}`);
    }
    return;
  }

  // Filtering the platforms that doesn't needs to be checked
  const platforms = client.platforms.filter(
    (plat) =>
      plat.checkedAt &&
      plat.checkedAt < expiringTime &&
      plat.status &&
      plat.status !== 'NOT_AVAILABLE'
  );

  for (let i = 0; i < platforms.length; i++) {
    const plat = platforms[i];

    try {
      const RportConfig = await RportConfigStore.getInstance();
      if (!RportConfig.enabledStatus) {
        break;
      }
      // Check one by one
      const platform = await RportPlatform.findOne({ _id: plat.id, enabled: true, deleted: false });
      if (platform) {
        await checkPlatformOnClient(client, platform);
        await delay(delayTime);
      }
    } catch (error) {
      Logger.error(`Platform Status Check - Error looping through platforms of client: ${error}`);
    }
  }

  return;
};

export const getCountClientsAddedSince = async (since: Date = Time.daysAgo(30)) => {
  const date = since.toISOString();

  return await RportClient.countDocuments({ deleted: false, createdAt: { $gt: date } });
};

export const platformFlagValidator = async () => {
  const expiringTime = new Date(+new Date() - 60 * 60 * 1000);

  // Remove the platforms that don't have a status and have a running process that expired
  await RportClient.updateMany(
    { 'platforms.0': { $exists: true }, deleted: false },
    {
      $pull: {
        platforms: { startedAt: { $lt: expiringTime }, running: true, status: { $exists: false } },
      },
    }
  );

  // Turn off the running property of the platforms that have a status and have a running process that expired
  await RportClient.updateMany(
    { 'platforms.0': { $exists: true }, deleted: false },
    { $set: { 'platforms.$[plat].running': false } },
    {
      arrayFilters: [
        {
          'plat.startedAt': { $lt: expiringTime },
          'plat.running': true,
          'plat.status': { $exists: true },
        },
      ],
    }
  );

  return `Host flags for running process removed`;
};

export const hostUninstallFlagValidator = async () => {
  const expiringTime = new Date(+new Date() - 15 * 60 * 1000);

  // Update all clients that have a running process that expired
  await RportClient.updateMany(
    { uninstalling: true, uninstallingStartingTime: { $lt: expiringTime }, deleted: false },
    { uninstalling: false }
  );

  return `Host flags for uninstalling process removed`;
};

export const getClientFilters = async (
  skip?: string[],
  extra?: {
    [key: string]: {
      type: string;
      relatives: string[];
      options?: { key: any; value: any }[];
    };
  }
) => {
  // Filters that are not needed to be shown
  const skippedFilter = [
    'deleted',
    'clientEmail',
    'addressHistory',
    'rportId',
    'updatedAt',
    'protected',
    'uninstallingStartingTime',
    'updatesRefreshedAt',
    'monitoringEnabled',
  ];
  if (skip) skippedFilter.push(...skip);

  // Get the options for platforms, tags and groups
  const [rportPlatforms, rportTags, rportGroups] = await Promise.all([
    RportPlatform.find(
      { deleted: false, enabled: true, isPlatform: true },
      {
        parameters: 0,
        apiConfig: 0,
      }
    )
      .sort('name')
      .select('_id name')
      .lean(),
    RportTag.find().select('_id name').lean(),
    RportGroup.find().select('_id name').lean(),
  ]);

  const platforms = rportPlatforms.map((option: any) => ({ key: option.name, value: option._id }));
  const tags = rportTags.map((option: any) => ({ key: option.name, value: option._id }));
  const groups = rportGroups.map((option: any) => ({ key: option.name, value: option._id }));
  const qualifications = QUALIFICATIONS.map((qualification: any) => ({
    key: qualification,
    value: qualification,
  }));
  const countries = await getCountryOptions();

  let extraFilters: { [key: string]: any } = {
    groups: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: groups,
    },
    tags: {
      type: FilterType.SELECT,
      relatives: Object.values(ArrayRelative),
      options: tags,
    },
    platforms: {
      type: FilterType.SELECT,
      relatives: Object.values(ArrayRelative),
      options: platforms,
    },
    scoring: {
      type: FilterType.SELECT,
      relatives: Object.values(ArrayRelative),
      options: qualifications,
    },
    connection_state: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: [
        { key: 'connected', value: 'connected' },
        { key: 'disconnected', value: 'disconnected' },
      ],
    },
    ipv4: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    country: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: countries,
    },
    updatesStatus: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: HOST_UPDATE_STATUS.map((status) => ({ key: status, value: status })),
    },
    osKernel: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: OS_LIST.map((os) => ({ key: os, value: os })),
    },
  };

  if (extra) {
    extraFilters = { ...extraFilters, ...extra };
  }

  // Filter Skip from extra filters
  for (const filter of skippedFilter) {
    delete extraFilters[filter];
  }

  return await RportClient.createFilter(extraFilters, skippedFilter);
};

export const parseClientFilters = async (filterObject: QueryFilterModel) => {
  // Mongo Filter
  const hostFilter: { [key: string]: any } = {};
  const rportFilter: { [key: string]: any } = {};

  // Create an object to store the possible conditions for each field
  const possibleConditions: { [key: string]: any[] } = {};

  // Parsing filters
  for (const [key, filterArray] of Object.entries(filterObject)) {
    for (const filter of filterArray) {
      // getting filter values
      const { relative, value } = filter;
      let query;
      // Making conditions for groups
      if (key === 'groups') {
        let hosts: string[];
        try {
          // getting ids from group
          hosts = await getHostIdsFromGroup(value.toString());
          // setting or condition for the relative (is_not $nin | is $in)
          query = groupConditionMaker(relative, hosts);
        } catch (err: any) {
          Logger.warning(`Error getting hosts from group: ${err}`);
          continue;
        }
      } else if (key === 'platforms') {
        query = platformConditionMaker(relative, value);
      } else if (key === 'tags') {
        query = tagConditionMaker(relative, value.toString());
      } else if (key === 'connection_state') {
        rportFilter['connection_state'] = rportConditionMaker(relative, value.toString());
        continue;
      } else if (key === 'ipv4') {
        query = ipv4ConditionMaker(relative, value.toString());
      } else if (key === 'country') {
        query = await countryConditionMaker(relative, value.toString());
      } else if (key === 'updatesStatus') {
        query = updateFilterParser(relative, value.toString());
      } else if (key === 'scoring') {
        query = await scoringConditionMaker(relative, value.toString());
      }
      if (!possibleConditions[key]) {
        possibleConditions[key] = [];
      }
      possibleConditions[key].push(query);
    }
  }

  // Create an array of $and conditions, where each $and condition includes all the possible conditions for a field
  const andConditions: { [key: string]: any }[] = [];
  for (const field in possibleConditions) {
    andConditions.push({ $or: possibleConditions[field] });
  }

  // Add the array of $and conditions to the mongo filter
  if (andConditions.length > 0) {
    hostFilter.$and = andConditions;
  }

  return [hostFilter, rportFilter];
};

const tagConditionMaker = (relative: RelativeType, value: string) => {
  return relative === 'is' ? { tags: value } : { tags: { $ne: value } };
};

const platformConditionMaker = (relative: RelativeType, platform: any) => {
  return relative === 'is'
    ? {
        platforms: {
          $elemMatch: { id: platform, status: { $ne: 'NOT_AVAILABLE' } },
        },
      }
    : {
        $or: [
          { 'platforms.id': { $ne: platform } },
          {
            platforms: {
              $elemMatch: { id: platform, status: 'NOT_AVAILABLE' },
            },
          },
        ],
      };
};

const scoringConditionMaker = async (relative: RelativeType, value: string) => {
  const hostScoreIds = await ProactivityHostScore.find({
    'globalScore.qualification': value,
  }).select('_id');

  return relative === 'is'
    ? { scoring: { $in: hostScoreIds } }
    : { scoring: { $nin: hostScoreIds } };
};

const groupConditionMaker = (relative: RelativeType, ids: Types.ObjectId[] | string[]) => {
  return relative === 'is' ? { rportId: { $in: ids } } : { rportId: { $nin: ids } };
};

const ipv4ConditionMaker = (relative: RelativeType, value: string) => {
  const queries = {
    is: { $elemMatch: value },
    is_not: { $not: { $elemMatch: value } },
    contains: { $elemMatch: { $regex: value } },
    not_contains: { $not: { $elemMatch: { $regex: value } } },
  };

  return queries[relative as StringRelative];
};

const rportConditionMaker = (relative: RelativeType, value: string) => {
  if (relative === 'is') {
    return value;
  } else {
    return value === 'connected' ? 'disconnected' : 'connected';
  }
};

export const updateFilterParser = (relative: RelativeType, value: string) => {
  if (value === UPDATED) {
    return relative === 'is' ? { updatesStatus: { $eq: [] } } : { updatesStatus: { $ne: [] } };
  }
  if (value === UPDATES_AVAILABLE) {
    return relative === 'is' ? { updatesStatus: { $ne: [] } } : { updatesStatus: { $eq: [] } };
  }
  if (value === SECURITY_UPDATES_AVAILABLE) {
    return relative === 'is'
      ? { 'updatesStatus.isSecurityUpdate': true }
      : { 'updatesStatus.isSecurityUpdate': false };
  }
};

export const getRebootCommand = (host: RportClientDocument) => {
  const os = host.osKernel as TSupportedOS;
  const fullOsName = host.os;

  // Calculate osVersion based on fullOsName
  const osVersions = osPlatforms[os];
  const version = osVersions?.find((version) => fullOsName.includes(version)) as
    | SupportedVersionsByOS<typeof os>[number]
    | undefined;

  if (!version) {
    throw errors.not_supported();
  }

  // Getting reboot command
  // NO estoy orgulloso de este tipado
  const rebootCommand = (<T extends TSupportedOS, K extends SupportedVersionsByOS<T>[number]>(
    os: T,
    version: K
  ): string => rebootCommands[os][version])(os, version);

  // If no reboot command is found, return null
  if (!rebootCommand) {
    throw errors.not_supported();
  }

  return rebootCommand;
};

// Function to retrieve host names and map them
export const getHostNamesMap = async (hosts: any[]) => {
  const hostIds = hosts.map((host) => host._id);
  const hostNames = await RportClient.find({ rportId: { $in: hostIds }, deleted: false }).select(
    'name rportId enabled'
  );

  const hostNamesMap = new Map<string, { name: string; enabled: boolean }>();
  hostNames.forEach((host) => {
    hostNamesMap.set(host.rportId, { name: host.name, enabled: host.enabled });
  });

  return hostNamesMap;
};

export const getHostByShellType = async (shellType: ShellType, hostIds?: string[]) => {
  try {
    // Get OS from shell type
    const os = getOSFromShellType(shellType);
    if (!os) throw errors.not_valid('Shell Type');

    // Get Host candidates based on the fix shell type
    const hosts = await RportClient.find({
      osKernel: os,
      deleted: false,
      enabled: true,
      ...(hostIds && hostIds.length > 0 ? { rportId: { $in: hostIds } } : {}),
    });

    // Check if host is connected
    const { clientIdsConnected, clientIdsDisconnected } = await getAndValidateClientsConnected(
      hosts.map((host) => host.rportId)
    );

    return { connectedHosts: clientIdsConnected, disconnectedHosts: clientIdsDisconnected };
  } catch (err) {
    throw err;
  }
};

export const getHostById = async (hostId: string) => {
  try {
    const rportClient = await getClient(hostId);
    if (!rportClient) throw errors.not_found('Rport client');

    const storedClient = await RportClient.findOne({
      rportId: hostId,
      deleted: false,
    })
      .populate({
        path: 'platforms.id',
        select: 'name template commands',
        populate: {
          path: 'template',
          select: 'service',
          populate: { path: 'service', select: 'name internalName' },
        },
      })
      .populate('tags', 'name')
      .populate('scoring');

    rportClient.platforms = storedClient?.platforms ?? [];
    rportClient.createdAt = storedClient?.createdAt;
    rportClient.criticality = storedClient?.criticality;
    rportClient.tags = storedClient?.tags;
    rportClient.enabled = storedClient?.enabled;
    rportClient.scoring = storedClient?.scoring;
    rportClient.uninstalling = storedClient?.uninstalling;
    rportClient.uninstallingStartingTime = storedClient?.uninstallingStartingTime;
    rportClient.uninstallingTaskId = null;
    if (rportClient.uninstalling) {
      const query = {
        name: TASK_NAMES.UNINSTALL_BATUTA,
        params: {
          selectedHosts: rportClient.id,
          affectedHosts: { $ne: rportClient.id },
        } as Partial<Record<keyof TaskParams<TASK_NAMES.UNINSTALL_BATUTA>, any>>,
        status: PENDING,
      };

      const task = await QueuesTask.findOne<KnownQueuesTaskDocument<TASK_NAMES.UNINSTALL_BATUTA>>(
        query
      )
        .select('_id')
        .lean();
      rportClient.uninstallingTaskId = task?._id;
    }

    return rportClient;
  } catch (err) {
    throw err;
  }
};
