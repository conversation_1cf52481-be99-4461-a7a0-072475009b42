import { Types } from 'mongoose';

import { RportScript, RportScriptTag } from '@services/Rport/schemas';

import { ShellType, ShellTypes } from '@shared/models';

import { errors } from '@shared/utils/app-errors';

import { MAX_TAGS_PER_SCRIPT } from '../constants';
import { ArrayRelative, FilterType, QueryFilterModel } from '@root/shared/models';
import { buildExtraFilters } from '@root/shared/helpers/classes/schema-utils.class';

// Validate ShellType value
export const validateShellType = (value: string): boolean =>
  ShellTypes.includes(value as ShellType);

// Tags validation
export const updateScripTags = async (scriptId: Types.ObjectId | string, scriptTags: string[]) => {
  // Getting the script
  const script = await RportScript.findById(scriptId);
  if (!script) throw errors.not_found('Script');

  // Getting validated Script Tags
  let validatedTags: Types.ObjectId[] = [];
  try {
    validatedTags = await getValidatedScriptTags(scriptTags);
  } catch (error) {
    throw error;
  }

  // Updating the script tags
  await RportScript.updateOne({ _id: scriptId }, { $set: { tags: validatedTags } });
};

export const getValidatedScriptTags = async (scriptTags: string[] = []) => {
  // Checking if the tags are greated than the maximum allowed
  if (scriptTags.length > MAX_TAGS_PER_SCRIPT) throw errors.maximum_tags_host();
  if (scriptTags.length === 0) return [];

  // Getting the tags that are registered
  const validatedTags = await RportScriptTag.find({ name: { $in: scriptTags } });

  // Validating which given tags are not registered
  const notRegisteredTags = scriptTags.filter(
    (tag) => !validatedTags.map((tag) => tag.name).includes(tag)
  );

  // Creating the not registered tags
  try {
    await RportScriptTag.insertMany(notRegisteredTags.map((tag) => ({ name: tag })));
  } catch (error) {
    throw error;
  }

  // Getting the tags that are registered
  const registeredTags = await RportScriptTag.find({ name: { $in: scriptTags } }).distinct('_id');

  return registeredTags;
};

// Script Extra Filters
export const getScriptFilters = async () => {
  const [rportScriptTags] = await Promise.all([RportScriptTag.find().select('_id name').lean()]);
  const tags =
    rportScriptTags.map((option: any) => ({ key: option.name, value: option._id })) || [];
  return {
    tags: {
      type: FilterType.SELECT,
      relatives: Object.values(ArrayRelative),
      options: tags,
    },
  };
};

export const parseScriptFilters = (filter: QueryFilterModel) => {
  return buildExtraFilters(filter);
};
