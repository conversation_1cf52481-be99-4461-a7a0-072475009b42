import { PipelineStage } from 'mongoose';

import { RportClient } from '../../schemas';

import {
  BooleanRelative,
  FilterType,
  NumberRelative,
  SelectRelative,
  StringRelative,
} from '@shared/models';
import { RportOsUpdateWithCount, RportOsUpdateWithHosts } from '../models';

import { errors } from '@shared/utils/app-errors';

import { OS_UPDATE_TYPES } from '../constants/os-update';
import {
  buildExtraFilters,
  parseFilterString,
  parseSortString,
} from '@shared/helpers/classes/schema-utils.class';

export const groupOsUpdates = async (
  offset: number = 0,
  limit?: number,
  filter?: string,
  sort?: string
) => {
  // Pipeline for grouping osUpdates
  const pipeline = [
    // Remove deleted hosts first to reduce number of operations
    { $match: { deleted: false, updatesStatus: { $exists: true } } },
    // unwind since we're de-duping documents
    { $unwind: { path: '$updatesStatus' } },
    // Keep only important variables and parse type
    {
      $project: {
        title: '$updatesStatus.title',
        description: '$updatesStatus.description',
        type: {
          $cond: {
            if: '$updatesStatus.isSecurityUpdate',
            then: 'security_update',
            else: 'update',
          },
        },
        rebootRequired: '$updatesStatus.rebootRequired',
        os: '$os',
        osFamily: '$osFamily',
        updatesRefreshedAt: '$updatesRefreshedAt',
      },
    },
    // De-dupe updates
    {
      $group: {
        _id: '$title',
        description: { $first: '$description' },
        type: { $first: '$type' },
        rebootRequired: { $first: '$rebootRequired' },
        os: { $addToSet: '$os' },
        osFamily: { $addToSet: '$osFamily' },
        maxExposureTime: { $min: '$updatesRefreshedAt' },
        count: { $sum: 1 },
      },
    },
    // Remove id
    {
      $project: {
        _id: 0,
        name: '$_id',
        description: '$description',
        type: '$type',
        rebootRequired: '$rebootRequired',
        os: '$os',
        osFamily: '$osFamily',
        maxExposureTime: '$maxExposureTime',
        count: '$count',
      },
    },
  ] as PipelineStage[];

  // Filter osUpdates
  if (filter) {
    const query = parseOsUpdateFilter(filter);
    pipeline.push({ $match: query });
  }
  // Sort osUpdates
  if (sort) {
    const sortQuery = parseOsUpdateSort(sort);
    pipeline.push({ $sort: sortQuery });
  } else {
    // Setting field for case insensitive sort
    pipeline.push({ $addFields: { lowerName: { $toLower: '$name' } } });

    // Default Sort by type and case insensitive name
    pipeline.push({ $sort: { type: 1, lowerName: 1 } });

    // Remove the field after sorting
    pipeline.push({ $project: { lowerName: 0 } });
  }

  // Pipeline for counting osUpdates
  const countPipeline = [...pipeline, { $count: 'total' }] as PipelineStage[];

  // Skip osUpdates
  pipeline.push({ $skip: +offset });

  // Limit osUpdates
  if (limit) {
    pipeline.push({ $limit: +limit });
  }

  const [filteredResults, filteredResultsCount] = await Promise.all([
    RportClient.aggregate<RportOsUpdateWithCount>(pipeline),
    RportClient.aggregate<{ total: number }>(countPipeline),
  ]);

  return [filteredResults, filteredResultsCount[0]?.total ?? 0] as const;
};

export const getOsUpdate = async (name: string) => {
  const pipeline: PipelineStage[] = [
    // Match first to reduce number of documents to unwind and process
    {
      $match: {
        deleted: false,
        'updatesStatus.title': name,
      },
    },
    // Unwind to create a copy of each host with all its' missing updates
    { $unwind: { path: '$updatesStatus' } },
    // Filter to only leave the copies with the update were looking for
    { $match: { 'updatesStatus.title': name } },
    // Grouping by title leaves a single group since they all share it
    {
      $group: {
        _id: '$updatesStatus.title',
        description: {
          $first: '$updatesStatus.description',
        },
        type: {
          $first: '$updatesStatus.isSecurityUpdate',
        },
        rebootRequired: {
          $first: '$updatesStatus.rebootRequired',
        },
        os: { $addToSet: '$os' },
        osFamily: { $addToSet: '$osFamily' },
        updatesRefreshedAt: { $min: '$updatesRefreshedAt' },
        // Create hosts set with necessary properties
        hosts: {
          $addToSet: {
            _id: '$_id',
            rportId: '$rportId',
            name: '$name',
            refreshed: '$updatesRefreshedAt',
            rebootPending: '$rebootPending',
          },
        },
      },
    },
    // Give fields their final names and format
    {
      $project: {
        _id: 0,
        name: '$_id',
        description: '$description',
        type: {
          $cond: {
            if: '$type',
            then: 'security_update',
            else: 'update',
          },
        },
        rebootRequired: '$rebootRequired',
        os: '$os',
        osFamily: '$osFamily',
        maxExposureTime: '$maxExposureTime',
        hosts: '$hosts',
      },
    },
  ];
  const osUpdates = await RportClient.aggregate<RportOsUpdateWithHosts>(pipeline);
  const osUpdateWithHosts = osUpdates.find((v) => v.name.toString() === name);

  if (!osUpdateWithHosts) {
    throw errors.not_found('OsUpdate');
  }

  return osUpdateWithHosts;
};

export const getOsUpdateFilters = () => {
  const filter = {
    name: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    type: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: OS_UPDATE_TYPES.map((type) => ({ value: type, key: type })),
    },
    count: {
      type: FilterType.NUMBER,
      relatives: Object.values(NumberRelative),
    },
    rebootRequired: {
      type: FilterType.BOOLEAN,
      relatives: Object.values(BooleanRelative),
    },
    os: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    osFamily: {
      type: FilterType.STRING,
      relatives: Object.values(StringRelative),
    },
    maxExposureTime: {
      type: FilterType.DATE,
      relatives: Object.values(NumberRelative),
    },
  };

  const fields = [
    'name',
    'type',
    'count',
    'rebootRequired',
    'maxExposureTime',
    'os',
    'osFamily',
  ] as const as (keyof RportOsUpdateWithCount)[];
  return [filter, fields];
};

export const parseOsUpdateFilter = (filterString: string) => {
  const filterObject = parseFilterString(filterString);
  return buildExtraFilters(filterObject);
};

const parseOsUpdateSort = (sortString: string) => {
  return parseSortString(sortString);
};

export const getOsUpdateSummary = async () => {
  // Initialize the hostSummary object
  const hostSummary = (
    await RportClient.aggregate<{
      host_up_to_date: number;
      host_with_updates: number;
      host_with_security_updates: number;
    }>([
      // Skip deleted hosts
      { $match: { deleted: false, updatesStatus: { $exists: true } } },
      {
        $group: {
          _id: null,
          // Count hosts with no updates
          host_up_to_date: {
            $sum: {
              $cond: [{ $eq: [{ $size: '$updatesStatus' }, 0] }, 1, 0],
            },
          },
          // Count hosts where no update is a security update
          // but they do have updates pending
          host_with_updates: {
            $sum: {
              $cond: [
                {
                  $or: [
                    { $eq: [{ $size: '$updatesStatus' }, 0] },
                    { $anyElementTrue: '$updatesStatus.isSecurityUpdate' },
                  ],
                },
                0,
                1,
              ],
            },
          },
          // Count hosts where any update is a security update
          host_with_security_updates: {
            $sum: {
              $cond: [{ $anyElementTrue: '$updatesStatus.isSecurityUpdate' }, 1, 0],
            },
          },
        },
      },
      {
        // Remove _id
        $project: {
          _id: 0,
          host_up_to_date: '$host_up_to_date',
          host_with_updates: '$host_with_updates',
          host_with_security_updates: '$host_with_security_updates',
        },
      },
    ])
  )[0];

  const vulnerabilitiesSummary: {
    updates_available: number;
    security_updates_available: number;
  } = (
    await RportClient.aggregate<{
      updates_available: number;
      security_updates_available: number;
    }>([
      // Remove deleted documents first
      { $match: { deleted: false, updatesStatus: { $exists: true } } },
      // Unwind the updates on all hosts
      { $unwind: { path: '$updatesStatus' } },
      {
        // Count security and regular updates
        $group: {
          _id: null,
          updates_available: {
            $sum: { $cond: ['$updatesStatus.isSecurityUpdate', 0, 1] },
          },
          security_updates_available: {
            $sum: { $cond: ['$updatesStatus.isSecurityUpdate', 1, 0] },
          },
        },
      },
      // Remove _id field
      {
        $project: {
          _id: 0,
          updates_available: 1,
          security_updates_available: 1,
        },
      },
    ])
  )[0];

  const updatesOSDistribution = await RportClient.aggregate([
    // Filter out deleted documents and ensure updatesStatus exists
    {
      $match: {
        deleted: false,
        updatesStatus: { $exists: true },
      },
    },
    // Deconstruct the updatesStatus array into separate documents
    { $unwind: { path: '$updatesStatus' } },
    // Create simplified OS field by removing version number
    {
      $addFields: {
        osSimplified: {
          $cond: {
            if: {
              $regexMatch: {
                input: '$os',
                regex: 'Windows .*?\\d+',
              },
            },
            then: {
              $regexFind: {
                input: '$os',
                regex: /Windows .*?\d+/,
              },
            },
            else: {
              $regexFind: {
                input: '$os',
                regex: /.*/,
              },
            },
          },
        },
      },
    },
    {
      $addFields: {
        osSimplified: {
          $trim: {
            input: {
              $replaceAll: {
                input: {
                  $cond: {
                    if: {
                      $lt: [{ $strLenCP: '$osSimplified.match' }, 30],
                    },
                    then: '$osSimplified.match',
                    else: {
                      $concat: [
                        {
                          $toUpper: {
                            $substrCP: ['$osFamily', 0, 1],
                          },
                        },
                        {
                          $substrCP: [
                            '$osFamily',
                            1,
                            {
                              $strLenCP: '$osFamily',
                            },
                          ],
                        },
                      ],
                    },
                  },
                },
                find: '$osVersion',
                replacement: '',
              },
            },
            chars: ' ',
          },
        },
      },
    },
    // Group by OS name and count occurrences
    {
      $group: {
        _id: '$osSimplified',
        count: {
          $sum: 1,
        },
      },
    },
    // Format final output structure
    {
      $project: {
        _id: 0,
        os: '$_id',
        count: 1,
      },
    },
  ]);

  // Aggregate to get updates grouped by age ranges
  const updatesByAgeRanges = await RportClient.aggregate([
    // Match non-deleted documents that have updates status
    {
      $match: {
        deleted: false,
        updatesStatus: { $exists: true },
      },
    },
    // Unwind updates array to process each update individually
    { $unwind: '$updatesStatus' },
    // Project only needed fields and calculate days since update
    {
      $project: {
        isSecurityUpdate: '$updatesStatus.isSecurityUpdate',
        daysSinceUpdate: {
          $divide: [
            { $subtract: [new Date(), '$updatesRefreshedAt'] }, // Calculate the difference in milliseconds
            1000 * 60 * 60 * 24, // Convert milliseconds to days (1 day = 24 hours * 60 minutes * 60 seconds * 1000 milliseconds)
          ],
        },
      },
    },
    // Add age range field based on days since update
    {
      $addFields: {
        ageRange: {
          $switch: {
            branches: [
              { case: { $lte: ['$daysSinceUpdate', 14] }, then: '0-14' },
              {
                case: {
                  $and: [{ $gt: ['$daysSinceUpdate', 14] }, { $lte: ['$daysSinceUpdate', 30] }],
                },
                then: '15-30',
              },
              {
                case: {
                  $and: [{ $gt: ['$daysSinceUpdate', 30] }, { $lte: ['$daysSinceUpdate', 60] }],
                },
                then: '31-60',
              },
              { case: { $gt: ['$daysSinceUpdate', 60] }, then: '61+' },
            ],
            default: 'Unknown',
          },
        },
      },
    },
    // Group by age range and security update type to get counts
    {
      $group: {
        _id: {
          ageRange: '$ageRange',
          isSecurityUpdate: '$isSecurityUpdate',
        },
        count: { $sum: 1 },
      },
    },
    // Sort by age range
    {
      $sort: {
        '_id.ageRange': 1,
      },
    },
    // Project to flatten the _id structure and remove _id field
    {
      $project: {
        _id: 0,
        ageRange: '$_id.ageRange',
        isSecurityUpdate: '$_id.isSecurityUpdate',
        count: 1,
      },
    },
  ]);

  // Define age range labels in order
  const ageRanges = ['0-14', '15-30', '31-60', '61+'];

  // Map aggregation results to separate arrays for regular and security updates
  const updates = {
    updates: ageRanges.map(
      (range) =>
        updatesByAgeRanges.find((item) => item.ageRange === range && !item.isSecurityUpdate)
          ?.count || 0 // If no security update, count as regular update
    ),
    securityUpdates: ageRanges.map(
      (range) =>
        updatesByAgeRanges.find((item) => item.ageRange === range && item.isSecurityUpdate)
          ?.count || 0 // If security update, count as security update
    ),
  };

  return {
    hostSummary,
    vulnerabilitiesSummary,
    updatesOSDistribution: updatesOSDistribution,
    updatesByAgeRanges: {
      labels: ageRanges,
      updates: updates.updates,
      securityUpdates: updates.securityUpdates,
    },
  };
};
