import { ProcessDesiredState, RportClientDocument, RportJob } from '../../schemas/';

import { executeCommandOnClientsOrGroups, getProcessesList } from '../connections/rport';

import prescripts from '../prescripts';

import { ShellTypesByKernel } from '@shared/models';
import { ProcessModel } from '../models';
import { CDN_BASE_URL } from '@root/shared/constants/env';
import { errors } from '@root/shared/utils/app-errors';

export const getHostProcessesById = async (
  hostId: string
): Promise<{ processes: ProcessModel[]; timestamp: string }> => {
  // Get all processes from rport
  const { data } = await getProcessesList(hostId);
  // Get all Desired States from DB for this host
  const desiredStates = await ProcessDesiredState.find({ hostId: hostId });

  // Create a hashmap of desired states for faster lookup
  const desiredStatesMap = new Map<string, 'running' | 'stopped'>();

  desiredStates.forEach((state) => {
    desiredStatesMap.set(state.processName, state.desiredState);
  });

  // Set desired state for each process if the name matches
  const parsedProcesses = data.processes.map((process) => {
    return {
      ...process,
      desiredState: desiredStatesMap.get(process.name) || undefined,
    };
  });

  // Return all processes
  return { processes: parsedProcesses, timestamp: data.timestamp };
};

export const stopProcessesById = async (
  host: RportClientDocument,
  processIds: number[],
  author: string
) => {
  let stopProcessCommand = getStopCommand(processIds, host.osKernel);

  if (prescripts[host.osKernel]) {
    stopProcessCommand = prescripts[host.osKernel].addPrescripts(stopProcessCommand);
  }

  if (!stopProcessCommand) {
    throw errors.not_supported('OS Kernel');
  }

  // Getting default shell type
  const shellType = ShellTypesByKernel[host.osKernel][0];

  let result;

  try {
    result = await executeCommandOnClientsOrGroups(
      [host.rportId],
      [],
      stopProcessCommand,
      300,
      shellType
    );
  } catch (error) {
    throw error;
  }

  // Create a new job for the stop process
  await RportJob.create({
    jobId: null,
    jobIds: result?.jid || [],
    author: author,
    command: 'BATUTA_STOP_PROCESS',
    timeout: 300,
    os: 'windows',
    clients: [host.rportId],
    clientsInQueue: [],
    useQueue: false,
  });
};

const getStopCommand = (processIds: number[], osKernel: string) => {
  if (osKernel === 'windows') {
    return `Invoke-WebRequest -Uri "${CDN_BASE_URL}/process/windows/Stop-Process.ps1" -OutFile "Stop-Process.ps1"; .\\Stop-Process.ps1 -ProcessIds ${processIds.join(',')}; Remove-Item -Path "Stop-Process.ps1" 2>&1 > $null`;
  }
  if (osKernel === 'linux') {
    return `curl -Ls "${CDN_BASE_URL}/process/linux/stop-process.sh" -o stop-process.sh; sudo bash stop-process.sh -p ${processIds.join(',')}`;
  }

  return undefined;
};
