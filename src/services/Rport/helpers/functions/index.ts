export {
  updateClients,
  infrastructureScanner,
  getOsVersion,
  composeQuery,
  platformFlagValidator,
  hostUninstallFlagValidator,
  removeOldClients,
  updateAgentInClients,
  getClientFilters,
  parseClientFilters,
  getClientsFromRport,
  getCountClientsAddedSince,
  getRportHostWithBatches,
  getRebootCommand,
  getHostNamesMap,
  updateFilterParser,
  getHostByShellType,
  enableMonitoringInClients,
  updateSingleClientOsUpdates,
} from './client.functions';
export { needsApiConfig } from './api-config.functions';
export {
  getDeviceFromService,
  updateServiceTokens,
  getAgentIdInPlatform,
  removeDeviceFromPlatform,
  getServiceStatus,
} from './services.functions';
export {
  _getPlatforms,
  checkPlatformOnClient,
  statusValidator,
  addDefaultPlatformsToClient,
  checkEnabledPlatformsInClient,
  checkAndAddDefaultPlatformsInNewClient,
  getSupportedOs,
  getSupportedOsVersions,
  countClientsByStatus,
  getTechnologyCoverageMetrics,
  getPlatformFilters,
} from './platform.functions';
export { abortScanner, getConfigInstance, updateConfig } from './config.functions';
export { getJobFilters, getMostAffectedHosts, getHostsAffectedByDeployment } from './job.functions';
export {
  getHostFromGroup,
  getHostCount,
  getHostPreview,
  getHostIdsFromGroup,
  validateGroupRules,
  getValidHostFields,
  getOnlineHostsFromGroup,
} from './group.functions';
export {
  updateRportPlatforms,
  convertToPlatformConfig,
  validateCommandSet,
  validateParameters,
  validateConfig,
  mergeWithTemplate,
  updateRportPlatform,
} from './platform-template.functions';
export { countryConditionMaker, getCountryOptions } from './ip.functions';
export {
  getScriptFilters,
  updateScripTags,
  validateShellType,
  getValidatedScriptTags,
  parseScriptFilters,
} from './script.functions';
export {
  getOsUpdate,
  getOsUpdateFilters,
  groupOsUpdates,
  parseOsUpdateFilter,
  getOsUpdateSummary,
} from './os-update.functions';
export { getHostProcessesById, stopProcessesById } from './client-process.functions';
export { updateMacAddresses } from './mac-address.functions';
