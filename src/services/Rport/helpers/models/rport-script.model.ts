import { Types } from 'mongoose';

import { SchemaBasicModel, ShellType } from '@shared/models';

// Rport Script Model Type
export interface RportScriptBase {
  name: string;
  description: string;
  script: string;
}

export interface RportScriptRequest extends RportScriptBase {
  shellType: string;
  tags: string[];
}

export interface RportScriptModel extends SchemaBasicModel, RportScriptBase {
  shellType: ShellType;
  tags: Types.ObjectId[];
  updatedBy: string;
  createdBy: string;
}
