import { CustomValidator } from 'express-validator';

export const interpreterValidator: CustomValidator = (interpreter: string, { req }) => {
  if (!interpreter) return true;

  const platform = (req.body.platform || '').toLowerCase();

  // Valid interpreter names referenced in the rport documentation for each supported platform
  // https://oss.rport.io/get-started/scripts/#customizing-an-interpreter
  // Since we validate platform before interpreter, we can assume it's either 'windows', 'macos' or 'linux'
  // platform property defaults to windows
  if (!platform || platform == 'windows') {
    if (interpreter != 'cmd' && interpreter != 'powershell') {
      throw new Error(
        `${interpreter} is not a valid interpreter for Windows. Select either 'powershell' (default) or 'cmd'.`
      );
    }
  } else if (platform == 'linux') {
    if (interpreter != 'bash') {
      throw new Error(
        `${interpreter} is not a valid interpreter for Linux. Currently, only 'bash' is supported.`
      );
    }
  } else if (interpreter != 'bash') {
    throw new Error(
      `${interpreter} is not a valid interpreter for Mac OS. Currently, only 'bash' is supported.`
    );
  }
  return true;
};
