import { RportClient, RportClientDocument } from '../../schemas';

import { errors } from '@shared/utils/app-errors';
import { delay } from '@shared/utils/delay';

import { getClient } from '../connections/rport';
import { checkAndAddDefaultPlatformsInNewClient } from '../functions';
import { updateProactivityScores } from '@root/services/LevelOfProactivity/helpers/utils';

export const getClientChunks = (ids: string[], size: number = 100) => {
  const res = [];
  for (let i = 0; i < ids.length; i += size) {
    const chunk = ids.slice(i, i + size);
    res.push(chunk);
  }
  return res;
};

export const validateHost = async (hostId: string) => {
  const host = await RportClient.findOne({ rportId: hostId, deleted: false });
  if (!host) throw errors.not_found('Host');
  if (!host.enabled) throw errors.host_not_enabled();

  return host;
};

export const postUpdateOrCreateHost = async (client: RportClientDocument, author: string) => {
  // Check and add default platforms in the new client
  await checkAndAddDefaultPlatformsInNewClient(client, author);

  // Update Proactivity Scores
  await updateProactivityScores([client.rportId]);
};

export const validateHostConnection = async (hostId: string) => {
  // Get if the host is online
  const rportHost = await getClient(hostId);
  if (!rportHost) throw errors.not_found('Host');
  if (rportHost.connection_state !== 'connected') throw errors.rport_host_disconnected();

  return rportHost;
};

export const runFunctionInBatchWithDelay = async <T>(
  ids: string[],
  fn: (ids: string[]) => Promise<T>,
  size: number = 100,
  delayInMs = 100
) => {
  let results: T[] = [];

  const chunks = getClientChunks(ids, size);
  for (const chunk of chunks) {
    const result = await fn(chunk);
    results = results.concat(result);

    if (delayInMs) {
      await delay(delayInMs);
    }
  }

  return results;
};
