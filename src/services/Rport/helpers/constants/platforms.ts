// Platforms available
const WINDOWS = 'windows';
const LINUX = 'linux';

export const OS_LIST = [WINDOWS, LINUX] as const;

export type TSupportedOS = (typeof OS_LIST)[number];

const WINDOWS_OS_VERSIONS = [
  'Windows XP',
  'Windows Vista',
  'Windows 7',
  'Windows 8',
  'Windows 8.1',
  'Windows 10',
  'Windows 11',
  'Windows Server',
] as const;

const LINUX_OS_VERSIONS = [
  'ubuntu',
  'debian',
  'fedora',
  'centos',
  'rhel',
  'Arch Linux',
  'suse',
  'Gentoo',
  'Kali Linux',
  'Alpine Linux',
  'Linux Mint',
  'Manjaro',
  'Elementary OS',
  'Zorin OS',
  'Slackware',
] as const;

const OS_VERSIONS_BY_OS = {
  [WINDOWS]: WINDOWS_OS_VERSIONS,
  [LINUX]: LINUX_OS_VERSIONS,
};
export default OS_VERSIONS_BY_OS;

// OS versions of any given supported os
export type SupportedVersionsByOS<T extends TSupportedOS> = T extends 'windows'
  ? (typeof OS_VERSIONS_BY_OS)['windows']
  : (typeof OS_VERSIONS_BY_OS)['linux'];

// Flatten os versions by os object to only leave versions
export const OS_VERSION_LIST = OS_LIST.flatMap((os) => OS_VERSIONS_BY_OS[os]);

// All os versions regardless of os
export type SupportedOSVersion = (typeof OS_VERSION_LIST)[number];

export const INTERPRETERS_BY_OS = {
  [WINDOWS]: ['powershell', 'cmd'],
  [LINUX]: ['tacoscript', 'bash'],
} as const;

export const SUPPORTED_INTERPRETERS = [
  ...INTERPRETERS_BY_OS[WINDOWS],
  ...INTERPRETERS_BY_OS[LINUX],
];

export type SupportedInterpreter = (typeof SUPPORTED_INTERPRETERS)[number];

// Utility function to identify os versions as supported (also checks for it being a substring of a full os version name)
export function isSupportedOsVersion(
  osVersion: string | SupportedOSVersion
): osVersion is SupportedOSVersion {
  return OS_VERSION_LIST.some((simplifiedOSName) =>
    (osVersion as SupportedOSVersion).includes(simplifiedOSName)
  );
}
