import { SupportedVersionsByOS, TSupportedOS } from './platforms';

const WINDOWS_COMMAND =
  'shutdown /r /t 60 /c "A restart has been scheduled by <PERSON><PERSON>, in 60 seconds your equipment will restart for security reasons"';

const LINUX_COMMAND =
  'sudo shutdown -r +1 "A restart has been scheduled by <PERSON><PERSON>, in 60 seconds your equipment will restart for security reasons"';

const REBOOT_COMMANDS = {
  windows: {
    'Windows XP': WINDOWS_COMMAND,
    'Windows Vista': WINDOWS_COMMAND,
    'Windows 7': WINDOWS_COMMAND,
    'Windows 8': WINDOWS_COMMAND,
    'Windows 8.1': WINDOWS_COMMAND,
    'Windows 10': WINDOWS_COMMAND,
    'Windows 11': WINDOWS_COMMAND,
    'Windows Server': WINDOWS_COMMAND,
  },
  linux: {
    ubuntu: LINUX_COMMAND,
    debian: LINUX_COMMAND,
    fedora: LINUX_COMMAND,
    centos: LINUX_COMMAND,
    rhel: LINUX_COMMAND,
    'Arch Linux': LINUX_COMMAND,
    suse: LINUX_COMMAND,
    Gentoo: LINUX_COMMAND,
    'Kali Linux': LINUX_COMMAND,
    'Alpine Linux': LINUX_COMMAND,
    'Linux Mint': LINUX_COMMAND,
    Manjaro: LINUX_COMMAND,
    'Elementary OS': LINUX_COMMAND,
    'Zorin OS': LINUX_COMMAND,
    Slackware: LINUX_COMMAND,
  },
} as { [os in TSupportedOS]: { [version in SupportedVersionsByOS<os>[number]]: string } };

export default REBOOT_COMMANDS;
