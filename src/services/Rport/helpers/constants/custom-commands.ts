export default {
  install: {
    windows: {
      'Windows 11':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Install-Custom.ps1" -OutFile "Install-Custom.ps1"; .\\Install-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{INSTALL_PARAMETERS}}',
      'Windows 10':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Install-Custom.ps1" -OutFile "Install-Custom.ps1"; .\\Install-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{INSTALL_PARAMETERS}}',
      'Windows Server':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Install-Custom.ps1" -OutFile "Install-Custom.ps1"; .\\Install-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{INSTALL_PARAMETERS}}',
    },
    linux: {
      debian: '',
    },
  },
  checkStatus: {
    windows: {
      'Windows 11':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Status-Custom.ps1" -OutFile "Status-Custom.ps1"; .\\Status-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{STATUS_PARAMETERS}}',
      'Windows 10':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Status-Custom.ps1" -OutFile "Status-Custom.ps1"; .\\Status-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{STATUS_PARAMETERS}}',
      'Windows Server':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Status-Custom.ps1" -OutFile "Status-Custom.ps1"; .\\Status-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{STATUS_PARAMETERS}}',
    },
    linux: {
      debian: '',
    },
  },
  uninstall: {
    windows: {
      'Windows 11':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Uninstall-Custom.ps1" -OutFile "Uninstall-Custom.ps1"; .\\Uninstall-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{UNINSTALL_PARAMETERS}}',
      'Windows 10':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Uninstall-Custom.ps1" -OutFile "Uninstall-Custom.ps1"; .\\Uninstall-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{UNINSTALL_PARAMETERS}}',
      'Windows Server':
        'Invoke-WebRequest -Uri "{{cdnBaseUrl}}/custom/windows/Uninstall-Custom.ps1" -OutFile "Uninstall-Custom.ps1"; .\\Uninstall-Custom.ps1 -gw "{{baseURL}}" -ct "{{clientToken}}" -sid "{{soarId}}" -plid "{{platformId}}" {{UNINSTALL_PARAMETERS}}',
    },
    linux: {
      debian: '',
    },
  },
} as {
  [commandType: string]: {
    [os: string]: {
      [version: string]: string;
    };
  };
};
