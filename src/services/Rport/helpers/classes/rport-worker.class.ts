import { RportJob, RportPlatformDocument } from '../../schemas';

import { RportClient } from '@services/Rport/schemas';
import { Task } from '@services/Queues/helpers/types';
import {
  getAndValidateClients,
  getAndValidatePlatform,
  updateClientByCommand,
} from '../utils/rport-platform.utils';
import AbstractWorker from '@services/Queues/helpers/worker/abstractWorker';
import { executeCommandOnClientsOrGroups } from '../connections/rport';
import TasksFunctions from '@root/services/Queues/helpers/functions/task.function';

import {
  CANCELLED,
  FINISHED,
  PENDING,
  TaskStatus,
} from '@root/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
import { CommandSet } from '../models';
import { Types } from 'mongoose';
import { INTERPRETERS_BY_OS, SupportedInterpreter } from '../constants/platforms';

export class RportWorker extends AbstractWorker<Task<TASK_NAMES.RUN_COMMAND>> {
  commandName: keyof CommandSet;
  groupIds?: string[];
  interpreter: SupportedInterpreter;
  os_version: string;
  os: string;
  platformId: string | Types.ObjectId;
  rportJobId?: string;

  affectedHosts: string[];
  selectedHosts: string[];

  hostsToAffect: string[] = [];
  connectedHosts: string[] = [];
  disconnectedHosts: string[] = [];
  platform: RportPlatformDocument | null = null;

  nextTaskStatus: TaskStatus = PENDING;
  nextTaskStatusDetail: string = '';

  constructor(task: Task<TASK_NAMES.RUN_COMMAND>) {
    super(task);
    this.commandName = task.params.commandName;
    this.groupIds = task.params.groupIds;
    this.interpreter = task.params.interpreter ?? INTERPRETERS_BY_OS[task.params.os][0];
    this.os = task.params.os;
    this.os_version = task.params.os_version;
    this.platformId = task.params.platformId;
    this.rportJobId = task.params.rportJobId;

    this.affectedHosts = task.params.affectedHosts;
    this.selectedHosts = task.params.selectedHosts;
  }

  async validate(): Promise<boolean> {
    try {
      const { commandName, os_version, os, platformId } = this.task.params;

      const platform = await getAndValidatePlatform(platformId, commandName, os, os_version);
      if (!platform) {
        // Cancel the current task
        this.nextTaskStatus = CANCELLED;
        this.nextTaskStatusDetail = 'Could not find platform to execute command';
        return false;
      }
      this.platform = platform;

      // Validate Hosts
      const hosts = await RportClient.find({
        rportId: { $in: this.selectedHosts },
        deleted: false,
      });

      if (hosts.length === 0) {
        // Cancel the current task
        this.nextTaskStatus = CANCELLED;
        this.nextTaskStatusDetail = 'The selected hosts are not available';
        return false;
      }

      // Get the hosts to affect in this run
      this.hostsToAffect = this.selectedHosts.filter((host) => !this.affectedHosts.includes(host));

      // Get connected hosts
      const { clientIdsConnected, clientIdsDisconnected } = await getAndValidateClients(
        this.hostsToAffect,
        os,
        os_version,
        commandName,
        platformId,
        false
      );

      if (clientIdsConnected.length === 0) {
        this.nextTaskStatus = PENDING;
        this.nextTaskStatusDetail = 'No connected hosts';
        return false;
      }

      // Set connected hosts
      this.connectedHosts = clientIdsConnected;
      this.disconnectedHosts = clientIdsDisconnected;

      return true;
    } catch {}

    this.nextTaskStatus = PENDING;
    this.nextTaskStatusDetail = 'Error validating the task, please wait for the next retry';

    return false;
  }

  async validationUpdates(isValid: boolean) {
    // If is valid returns nothing
    if (isValid) return { statusDetail: '' };

    // If is not valid, return the task with the new status
    return {
      status: this.nextTaskStatus,
      statusDetail: this.nextTaskStatusDetail,
    };
  }

  async run() {
    try {
      const { commandName, groupIds, interpreter, rportJobId, os_version, os } = this.task.params;
      let result: { jid: string | null } = { jid: null };

      // If there are no hosts to affect, finish the task
      if (this.hostsToAffect.length === 0) {
        return {
          _id: this.task._id,
          finished: new Date(),
          status: FINISHED,
          statusDetail: 'The command have been applied to all the hosts',
        };
      }

      result = await executeCommandOnClientsOrGroups(
        this.connectedHosts,
        groupIds || [],
        this.platform?.getParsedCommand(commandName, os, os_version) || '',
        this.platform?.commands[commandName]?.[os]?.[os_version].timeout || 60,
        interpreter
          ? interpreter
          : os === 'windows'
            ? 'powershell'
            : os === 'linux'
              ? 'bash'
              : undefined,
        os === 'windows' ? 'C:\\Windows\\Temp' : '/tmp'
      );

      if (rportJobId) {
        await RportJob.findOneAndUpdate(
          { _id: rportJobId },
          {
            $push: {
              jobIds: result.jid,
              clients: { $each: this.connectedHosts },
            },
            $pullAll: {
              clientsInQueue: this.connectedHosts,
            },
          }
        );
      }

      if (this.platform) {
        await updateClientByCommand(this.platform, commandName, this.connectedHosts);
      }

      // Get new affected hosts
      const newAffectedHosts: string[] = [...this.affectedHosts, ...this.connectedHosts];

      // Remove the connected hosts from the task params
      await TasksFunctions.updateTask(this.task._id, {
        params: { ...this.task.params, affectedHosts: newAffectedHosts },
      });

      // Check if there are still hosts to affect
      const hostLeft = this.selectedHosts.filter((host) => !newAffectedHosts.includes(host));

      if (hostLeft.length === 0) {
        return {
          _id: this.task._id,
          finished: new Date(),
          status: FINISHED,
          statusDetail: 'The command has been sent to all the hosts',
        };
      }

      return {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: 'Some hosts are not connected',
      };
    } catch (error) {
      throw {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: error?.toString(),
      };
    }
  }
}
