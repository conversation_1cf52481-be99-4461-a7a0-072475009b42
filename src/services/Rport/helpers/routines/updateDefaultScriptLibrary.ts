import { downloadFileFromS3, getFileHeadFromS3 } from '@root/soar/helpers/services/cdn.services';
import { RportScript, RportScriptTag } from '../../schemas';
import { isFunctionalityEnabled } from '@root/shared/helpers/functions/functionality.function';
import { Logger } from '@root/shared/helpers/classes/logger.class';
import { ShellType, ShellTypes } from '@root/shared/models';
import { Notifier } from '@root/shared/helpers/classes/notifier.class';
import { CDN_S3_BASE_URL, CLIENT_NAME } from '@root/shared/constants/env';
import { dateAndTime } from '@root/shared/utils/log-date';

// Routine configuration variables
const DEFAULT_LIBRARY_FILE_KEY = 'shared/rtr_default_library.json';
const BUILTIN_TAG = 'builtin_ra';
const MAINTAINER = '<EMAIL>';

function handleRoutineError(error: Error) {
  Logger.error('Error in RTR Routine:', error.message);
  Notifier.notifyDevTeam({
    subject: `${CLIENT_NAME} - Error while running RTR Default Update`,
    templateName: 'requestError',
    templateValues: {
      data: {
        Date: dateAndTime(),
        ErrorName: error.name,
        ErrorMessage: error.message,
        Contact: `Please forward to ${MAINTAINER}`,
      },
    },
  });
}

type DefaultLibraryEntry = {
  name: string;
  description: string;
  script: string;
  shelltype: ShellType;
  tags: string[];
};

function isValidScript(script: DefaultLibraryEntry): boolean {
  // Validate simple string properties
  return (
    (
      [
        ['name', 256],
        ['description', 1000],
        ['script', undefined],
      ] as [keyof DefaultLibraryEntry, number | undefined][]
    ).every(
      ([property, maxLength]) =>
        script[property] !== undefined &&
        typeof script[property] == typeof '' &&
        (maxLength === undefined || script[property].length < maxLength)
    ) &&
    // Script shell type validation
    script.shelltype !== undefined &&
    ShellTypes.includes(script.shelltype) &&
    // Script tags validation
    script.tags !== undefined &&
    script.tags.length !== undefined &&
    script.tags.every((tag) => tag !== undefined && typeof tag == typeof '')
  );
}

async function updateDefaultScriptLibrary() {
  Logger.info('Running RTR Default Library Update');

  // Convert tags to name-id hashmap to speed-up lookups in creation
  const tagsNameToId = new Map(
    (await RportScriptTag.find().select('name id')).map((tag) => [tag.name, tag.id])
  );

  let defaultLibrary = [] as DefaultLibraryEntry[];
  // Try catch since we don't know if the file is valid json
  try {
    const scriptFile = await downloadFileFromS3(DEFAULT_LIBRARY_FILE_KEY);
    const libraryText = await scriptFile.Body.transformToString();

    const parsedLibrary = JSON.parse(libraryText);
    if (!Array.isArray(parsedLibrary)) throw Error('Invalid library file. Not a json array');
    // Leave validation till iteration so we can have better error messages
    defaultLibrary = parsedLibrary as DefaultLibraryEntry[];
  } catch (e) {
    handleRoutineError(e as Error);
    return;
  }

  // Delete all builtin_ra scripts no longer in the library
  // If builtin tag does not exist, no scripts do
  if (tagsNameToId.has(BUILTIN_TAG)) {
    RportScript.deleteMany(
      {
        name: { $notIn: defaultLibrary.map((script) => script.name) },
        tags: { $includes: tagsNameToId.get(BUILTIN_TAG) },
      },
      {
        bypassProtection: true,
      }
    );
  } else {
    const newTag = await RportScriptTag.create({
      name: BUILTIN_TAG,
    });
    tagsNameToId.set(BUILTIN_TAG, newTag.id);
  }

  const BUILTIN_TAG_ID = tagsNameToId.get(BUILTIN_TAG);

  for (const script of defaultLibrary) {
    // Validate script before doing anything else
    if (!isValidScript(script)) {
      const invalidScriptString = `
{
  name: "${script.name}",
  description: "${script.description}",
  tags: [${script.tags}],
  shellType: "${script.shelltype}",
  script: [ommitted, check file if needed]
}
Halting default library update`;

      handleRoutineError({
        name: 'Found invalid script in default library file',
        message: invalidScriptString,
      });
      return;
    }

    // Add missing tags
    for (const tag of script.tags) {
      const safeTag = tag.replace(/\s/g, '_').slice(0, 30);
      if (!tagsNameToId.has(safeTag)) {
        const newTag = await RportScriptTag.create({
          name: safeTag,
        });
        tagsNameToId.set(newTag.name, newTag.id);
      }
    }

    const existingScript = await RportScript.findOne({ name: script.name }).select('id');
    // Add builtin tag to script tags
    const tags = new Set([
      ...script.tags
        ?.map((tag) => tagsNameToId.get(tag.replace(/\s/g, '_').slice(0, 30)))
        .filter((v) => !!v),
      BUILTIN_TAG_ID,
    ]);

    try {
      if (existingScript === null) {
        await RportScript.create({
          name: script.name,
          description: script.description,
          script: script.script,
          tags: [...tags.values()],
          shellType: script.shelltype,
          protected: true,
          createdBy: 'SYSTEM',
          updatedBy: 'SYSTEM',
        });
      } else {
        await existingScript.updateOne(
          {
            description: script.description,
            script: script.script,
            shellType: script.shelltype,
            tags: [...tags.values()],
            updatedBy: 'SYSTEM',
            createdBy: 'SYSTEM',
          },
          {
            bypassProtection: true,
          }
        );
      }
    } catch (e) {
      handleRoutineError(e as Error);
      return;
    }
  }
  Logger.info('Finished RTR Default Library Update');
  return 'Finished RTR Default Library Update';
}

export default async function checkIfDefaultScriptLibraryNeedsUpdate() {
  // If the functionality is not enabled, don't run the routine
  if (!isFunctionalityEnabled('rport.scripts', false)) return;
  // This routine requires the CDN to be configured and on some dev envs it is not.
  // The calls are inside a try catch, but we can avoid the throw if we check here.
  if (!CDN_S3_BASE_URL) return;

  let scriptFileMetadata;
  try {
    scriptFileMetadata = await getFileHeadFromS3(DEFAULT_LIBRARY_FILE_KEY);
  } catch (e) {
    handleRoutineError({
      name: 'Could not get file head',
      message: `RTR Update Routine errored while getting file head. Check file key: ${DEFAULT_LIBRARY_FILE_KEY}`,
    });
    return 'RTR Update Routine errored while getting file head';
  }
  if (scriptFileMetadata.LastModified === undefined) {
    handleRoutineError({
      name: 'Invalid library file',
      message: `RTR Update Routine errored. File had no metadata`,
    });
    return 'RTR Update Routine errored. File head had no metadata';
  }

  // Get the builtin tag, if it isn't registered
  const builtinTag = await RportScriptTag.findOne({ name: BUILTIN_TAG }).select('id');
  if (builtinTag === null) {
    return await updateDefaultScriptLibrary();
  }

  // If there is a script with the builtin tag and it was updated after the last time the library was modified, do nothing
  const latestUpdatedDefaultLibraryScript = await RportScript.findOne({
    tags: { $in: builtinTag.id },
  })
    .sort({ updatedAt: 1 })
    .select('updatedAt');

  if (
    latestUpdatedDefaultLibraryScript !== null &&
    scriptFileMetadata.LastModified <= latestUpdatedDefaultLibraryScript.updatedAt
  )
    return 'RTR Default Library Update did not need to execute';

  // If we're here it means we need to update the library
  return await updateDefaultScriptLibrary();
}
