import { RportClient, RportIP } from '../../schemas';

import { RportClientsPerCountry, RportClientsByOSAggregation } from '../types';

export const getClientsCountPerCountry = async (): Promise<RportClientsPerCountry> => {
  return (await RportIP.aggregate([
    {
      $group: {
        _id: '$location',
        city: {
          $first: '$city',
        },
        region: {
          $first: '$region',
        },
        country: {
          $first: '$country',
        },
        location: {
          $first: '$location',
        },
        ips: {
          $push: '$ip',
        },
      },
    },
    {
      $lookup: {
        from: 'rportclients',
        localField: 'ips',
        foreignField: 'address',
        pipeline: [{ $match: { deleted: false, enabled: true } }],
        as: 'clients',
      },
    },
    {
      $addFields: {
        quantity: {
          $size: '$clients',
        },
        coordinates: '$location.coordinates',
      },
    },
    {
      $match: {
        quantity: { $gt: 0 },
      },
    },
    {
      $group: {
        _id: '$country',
        country: {
          $first: '$country',
        },
        locations: {
          $addToSet: '$coordinates',
        },
        count: {
          $sum: '$quantity',
        },
      },
    },
    {
      $project: {
        _id: 0,
        locations: 1,
        count: 1,
        country: 1,
      },
    },
    {
      $sort: {
        count: -1,
      },
    },
  ])) as RportClientsPerCountry;
};

export const getClientsCountByOS = async () => {
  return (await RportClient.aggregate([
    {
      $match: {
        deleted: false,
        enabled: true,
      },
    },
    {
      $addFields: {
        osSimplified: {
          $cond: {
            if: {
              $regexMatch: {
                input: '$os',
                regex: 'Windows .*?\\d+',
              },
            },
            then: {
              $regexFind: {
                input: '$os',
                regex: /Windows .*?\d+/,
              },
            },
            else: {
              $regexFind: {
                input: '$os',
                regex: /.*/,
              },
            },
          },
        },
      },
    },
    {
      $addFields: {
        osSimplified: {
          $trim: {
            input: {
              $replaceAll: {
                input: {
                  $cond: {
                    if: {
                      $lt: [{ $strLenCP: '$osSimplified.match' }, 30],
                    },
                    then: '$osSimplified.match',
                    else: {
                      $concat: [
                        {
                          $toUpper: {
                            $substrCP: ['$osFamily', 0, 1],
                          },
                        },
                        {
                          $substrCP: [
                            '$osFamily',
                            1,
                            {
                              $strLenCP: '$osFamily',
                            },
                          ],
                        },
                      ],
                    },
                  },
                },
                find: '$osVersion',
                replacement: '',
              },
            },
            chars: ' ',
          },
        },
      },
    },
    {
      $group: {
        _id: '$osSimplified',
        count: {
          $sum: 1,
        },
      },
    },
    {
      $sort: {
        count: -1,
      },
    },
    {
      $project: {
        _id: 0,
        os: '$_id',
        count: '$count',
      },
    },
  ])) as RportClientsByOSAggregation;
};
