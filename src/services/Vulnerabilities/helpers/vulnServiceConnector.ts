import { Logger } from '@root/shared/helpers/classes/logger.class';
import axios, { AxiosError, AxiosResponse } from 'axios';
import mongoose from 'mongoose';
import { SOAR_ID } from '@shared/constants/env';

const vulnerabilityServiceApiKeySchema = new mongoose.Schema({
  clientId: { type: String, required: true, unique: true },
  apiKey: { type: String, required: true },
  updatedAt: { type: Date, default: Date.now },
});

const VulnerabilityServiceApiKey = mongoose.model(
  'VulnerabilityServiceApiKey',
  vulnerabilityServiceApiKeySchema
);

class VulnerabilityServiceConnector {
  private serviceUrl: string;
  private clientId: string;
  private apiKey: string = '';
  private token: string | null = null;
  private tokenExpiry = 0;
  private initialized: boolean = false;

  constructor(config: { serviceUrl: string; clientId: string; apiKey?: string }) {
    this.serviceUrl = config.serviceUrl;
    this.clientId = config.clientId;

    if (config.apiKey) {
      this.apiKey = config.apiKey;
    }

    this.initializeApiKey();
  }

  private async initializeApiKey(): Promise<void> {
    try {
      const storedKey = await this.loadApiKeyFromDb();

      if (storedKey) {
        this.apiKey = storedKey;
      } else if (this.apiKey) {
        await this.saveApiKeyToDb(this.apiKey);
      } else {
        Logger.warning(`No Vulnerability Service API Key available for client: ${this.clientId}`);
      }

      this.initialized = true;
    } catch (error) {
      Logger.error('Failed to initialize Vulnerability Service API Key:', error);
    }
  }

  public async updateApiKey(newApiKey: string): Promise<void> {
    this.apiKey = newApiKey;
    await this.saveApiKeyToDb(newApiKey);
  }

  private async loadApiKeyFromDb(): Promise<string | null> {
    try {
      const keyData = await VulnerabilityServiceApiKey.findOne({
        clientId: this.clientId,
      }).lean();

      return keyData?.apiKey || null;
    } catch (error) {
      Logger.error('Failed to load Vulnerability Service API Key from database:', error);
      return null;
    }
  }

  private async saveApiKeyToDb(apiKey: string): Promise<void> {
    try {
      await VulnerabilityServiceApiKey.findOneAndUpdate(
        {
          clientId: this.clientId,
        },
        {
          apiKey,
          updatedAt: new Date(),
        },
        { upsert: true }
      );

      Logger.info(`Vulnerability Service API Key updated in database for client: ${this.clientId}`);
    } catch (error) {
      Logger.error('Failed to save Vulnerability Service API Key to database:', error);
      throw new Error('Failed to save Vulnerability Service API Key to database');
    }
  }

  private async ensureValidToken(): Promise<string> {
    if (!this.initialized) {
      const maxWaitTime = 5000;
      const startTime = Date.now();

      while (!this.initialized && Date.now() - startTime < maxWaitTime) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      if (!this.initialized) {
        Logger.warning('Proceeding without completed initialization');
      }
    }

    const now = Math.floor(Date.now() / 1000);

    if (!this.token || this.tokenExpiry - now < 30) {
      await this.authenticate();
    }

    return this.token!;
  }

  private async authenticate(): Promise<void> {
    try {
      if (!this.apiKey) {
        const storedKey = await this.loadApiKeyFromDb();
        if (!storedKey) {
          throw new Error('No API Key available for authentication');
        }
        this.apiKey = storedKey;
      }

      const response = await axios.post(
        `${this.serviceUrl}/clients/auth`,
        {
          clientId: this.clientId,
          apiKey: this.apiKey,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      this.token = response.data.token;

      const expiresIn = Number(response.data.expiresIn);
      if (isNaN(expiresIn)) {
        this.tokenExpiry = Math.floor(Date.now() / 1000) + 3600;
      } else {
        const now = Math.floor(Date.now() / 1000);
        this.tokenExpiry = now + expiresIn;
      }
    } catch (error) {
      Logger.error('Authentication failed:', error);
      throw new Error('Failed to authenticate with the vulnerability service');
    }
  }

  private async getHeaders(): Promise<Record<string, string>> {
    const token = await this.ensureValidToken();
    return {
      Authorization: `Bearer ${token}`,
      'X-API-Key': this.apiKey,
      'Soar-Id': SOAR_ID,
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(
    requestPartial: (headers: Record<string, string>) => Promise<AxiosResponse<T, AxiosError>>
  ): Promise<T> {
    try {
      const headers = await this.getHeaders();
      const response = await requestPartial(headers);

      // await this.checkForKeyRotation(response.data);

      return response.data;
    } catch (error: any) {
      const isUnauthorized = error?.response?.status === 401;

      if (isUnauthorized) {
        try {
          Logger.warning('Token expired or unauthorized');

          await this.authenticate();

          const headers = await this.getHeaders();

          const retryResponse = await requestPartial(headers);

          // await this.checkForKeyRotation(retryResponse.data);

          return retryResponse.data;
        } catch (retryError) {
          Logger.error('Retry after authentication failed:', retryError);
          throw new Error('Authentication retry failed with the Vulnerability service');
        }
      }

      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message =
          error.response?.data?.message ||
          `Vulnerability Service request failed with status ${status}`;
        Logger.error(`Axios error during request: ${message}`, {
          url: error.config?.url,
          method: error.config?.method,
          status,
          data: error.response?.data,
        });
        throw new Error(message);
      }

      Logger.error('Unexpected error during request:', error);
      throw new Error('Unexpected error occurred while making a request to Vulnerability service');
    }
  }

  async get<T = any>(endpoint: string, queryParams?: Record<string, any>): Promise<T> {
    return this.request((headers) =>
      axios.get(`${this.serviceUrl}${endpoint}`, { headers, params: queryParams })
    );
  }

  async post<T = any>(endpoint: string, data: any): Promise<T> {
    return this.request((headers) =>
      axios.post(`${this.serviceUrl}${endpoint}`, data, { headers })
    );
  }

  async put<T = any>(endpoint: string, data: any): Promise<T> {
    return this.request((headers) => axios.put(`${this.serviceUrl}${endpoint}`, data, { headers }));
  }

  async delete<T = any>(endpoint: string): Promise<T> {
    return this.request((headers) => axios.delete(`${this.serviceUrl}${endpoint}`, { headers }));
  }

  // private async checkForKeyRotation(responseData: any): Promise<void> {
  // if (responseData && responseData.keyRotation && responseData.keyRotation.newApiKey) {
  //   await this.updateApiKey(responseData.keyRotation.newApiKey);
  // }
  // }

  // private async handleError(error: any): Promise<void> {
  //   if (axios.isAxiosError(error)) {
  //     if (error.response?.status === 401) {
  // const errorData = error.response.data;
  // if (errorData && errorData.keyExpired) {
  //   Logger.error('Vulnerability Service API Key has expired. Obtain a new key.');
  // }

  //   this.token = null;
  // }

  // if (error.response?.data && error.response.data.keyRotation) {
  //   await this.checkForKeyRotation(error.response.data);
  // }
  //   }
  // }
}

export default VulnerabilityServiceConnector;
