import { Types } from 'mongoose';
import { getDaysInMonth, set, isAfter, addMonths } from 'date-fns';

import { Report, ScheduledReport, ScheduledReportDocument } from '../../schemas';

import { FilterType, SelectRelative } from '@shared/models';

import { errors } from '@shared/utils/app-errors';

import { getNewReportBuffer, sendReportEmails } from './report.functions';
import LANGUAGES from '@root/shared/constants/languages';

export const getScheduledReportFiltersFormatted = async () => {
  // Filters that are not needed to be shown
  const skippedFilter = [
    'protected',
    'deleted',
    'errorMessage',
    'cdnFilePath',
    'updatedAt',
    'language',
  ];

  // Create the language options
  const languages = LANGUAGES.map((lang) => ({ key: lang, value: lang }));

  const extraFilters: { [key: string]: any } = {
    language: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: languages,
    },
  };

  return await ScheduledReport.createFilter(extraFilters, skippedFilter);
};

export const scheduledReportTrigger = async (): Promise<string> => {
  try {
    // Get all the scheduled reports that need to be send and are enabled
    const scheduledReports = await ScheduledReport.find({
      enabled: true,
      nextRun: { $lte: new Date() },
    });

    // Iterate over the scheduled reports
    for (const scheduledReport of scheduledReports) {
      // Send the report to the recipients
      await sendReportToUsers(scheduledReport, scheduledReport.recipients);

      // Calculate the next run date
      const nextRun = await calculateNextScheduledReport(
        scheduledReport.hour,
        scheduledReport.monthDay
      );

      // Update the next run date
      await ScheduledReport.findByIdAndUpdate(scheduledReport._id, { nextRun });
    }
  } catch (err) {
    throw err;
  }

  return 'Scheduled Report Triggered';
};

export const sendReportToUsers = async (
  scheduledReport: ScheduledReportDocument,
  recipients: string[],
  author?: string
): Promise<void> => {
  try {
    // Generate and send the report
    await sendScheduledReportToUsers(scheduledReport, recipients, author);

    // Update the last sent date
    await ScheduledReport.findByIdAndUpdate(scheduledReport._id, { lastRun: new Date() });
  } catch (err) {
    throw err;
  }
};

const sendScheduledReportToUsers = async (
  scheduledReport: ScheduledReportDocument,
  recipients: string[],
  author: string = 'SYSTEM'
): Promise<void> => {
  try {
    // Prepare the report name
    const name = await getReportNextName(scheduledReport._id);

    // Generate the report
    const pdfBuffer = await getNewReportBuffer({
      name,
      description: scheduledReport.description,
      sections: scheduledReport.sections,
      language: scheduledReport.language,
      author: author,
    });

    // Send the report to the recipients
    await sendReportEmails(name, pdfBuffer, recipients, author, scheduledReport.language);
  } catch (err) {
    throw err;
  }
};

const getReportNextName = async (scheduledReportId: Types.ObjectId): Promise<string> => {
  // Get the scheduled report
  const scheduledReport = await ScheduledReport.findById(scheduledReportId);
  if (!scheduledReport) throw errors.not_found('Scheduled Report');
  if (!scheduledReport.enabled) throw errors.disabled('Scheduled Report');

  // Get the latest report for the scheduled report based on the name regex (e.g. "Report Name - 1")
  const latestReport = await Report.findOne({
    name: new RegExp(`^${scheduledReport.name}`, 'i'),
  }).sort({ name: -1 });

  // If no report is found, return the initial name
  if (!latestReport) return `${scheduledReport.name} - 1`;

  // If a report is found, increment the number and return the new name
  const latestReportName = latestReport.name;

  // Check if the latest report name has a number
  if (!latestReportName.includes('-')) return `${scheduledReport.name} - 1`;

  // Get the latest report number
  const latestReportNumber = parseInt(latestReportName.split(' - ')[1], 10);

  // Return the next name
  return `${scheduledReport.name} - ${latestReportNumber + 1}`;
};

export const calculateNextScheduledReport = (time: string, dayOfMonth: number): string => {
  // Get the current date
  const now = new Date();

  // Get the number of days in the current month
  const currentMonthDays = getDaysInMonth(now);

  // Clamp the day to the last valid day of the current month
  const validDayOfMonth = Math.min(dayOfMonth, currentMonthDays);

  // Create the target date for the current month
  let targetDate = set(now, {
    date: validDayOfMonth,
    hours: parseInt(time.split(':')[0], 10),
    minutes: parseInt(time.split(':')[1], 10),
    seconds: 0,
    milliseconds: 0,
  });

  // If the target date has already passed this month, calculate for the next month
  if (!isAfter(targetDate, now)) {
    targetDate = addMonths(targetDate, 1);
    const nextMonthDays = getDaysInMonth(targetDate);

    // Clamp the day to the last valid day of the next month
    targetDate = set(targetDate, { date: Math.min(dayOfMonth, nextMonthDays) });
  }

  // Return the date in ISO 8601 format
  return targetDate.toISOString();
};
