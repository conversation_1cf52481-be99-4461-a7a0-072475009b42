import { Readable } from 'stream';

import { startSession, Types } from 'mongoose';

import { Service } from '@shared/schemas';
import { RportClient, RportGroup } from '@services/Rport/schemas';
import { Policy } from '@services/Compliance/schemas';
import { Report, ReportDocument } from '../../schemas/report.schema';

import { FilterType, SelectRelative } from '@shared/models';

import { reportSectionServiceMap, ReportSectionT } from '../models';

import {
  DeploymentData,
  ComplianceData,
  TechnologyCoverageData,
  InventoryData,
  ProactivityData,
  HostsData,
  OsUpdatesData,
  ReportData,
  SectionFetcherFunctions,
} from '../types/';

import { Logger } from '@shared/helpers/classes/logger.class';
import { t_country } from '@shared/utils/translation/translator';
import { Time } from '@shared/helpers/classes/times.class';
import { errors } from '@shared/utils/app-errors';
import { Notifier } from '@shared/helpers/classes/notifier.class';
import { t } from '@shared/utils/translation/translator';

import { getFiles, uploadFile, deleteFile } from '@root/soar/helpers/services';
import {
  getCountClientsAddedSince,
  getOsUpdateSummary,
  getTechnologyCoverageMetrics,
  getMostAffectedHosts,
  getHostsAffectedByDeployment,
} from '@services/Rport/helpers/functions';
import {
  getClientsCountByOS,
  getClientsCountPerCountry,
} from '@services/Rport/helpers/queries/client.queries';
import {
  getComplianceMetrics,
  getPoliciesStatusMetrics,
  getPolicyMetricsForReport,
} from '@services/Compliance/helpers/functions';
import {
  getControlAplicationPolicyStatus,
  getControlApplicationSummary,
} from '@services/Inventory/helpers/functions';
import {
  getGlobalScore,
  getHostScoreDistribution,
  getSummaryScoreByArea,
} from '@services/LevelOfProactivity/helpers/utils';

import { generatePDF } from '../pdf/generatePDF';

import { CLIENT_NAME } from '@shared/constants/env';
import LANGUAGES from '@root/shared/constants/languages';
import { TLanguage } from '@root/shared/types/languages.types';
import { Qualification } from '../types/proactivity.types';
import { toPercentage } from '@root/shared/utils/strings';

export const generateReport = async (
  reportId: string,
  reportName: string,
  language: TLanguage
): Promise<Buffer> => {
  try {
    // Update the report status to 'collecting_data'
    await updateReportStatus(reportId, 'collecting_data');

    // Get the data for the report
    const reportData = await getDataForReport(reportId, language);

    // Update the report status to 'generating_pdf'
    await updateReportStatus(reportId, 'generating_pdf');

    // Generate the PDF
    const pdfBuffer = await generatePDF(reportId, reportData, language);

    // Check if the report is not cancelled
    if (!(await checkCancelled(reportId))) throw errors.report_cancelled();

    // Update the report status to 'uploading_to_cdn'
    await updateReportStatus(reportId, 'uploading_to_cdn');

    // Upload the PDF to the CDN
    const cdnFilePath = await uploadPDFToCDN(reportId, reportName, pdfBuffer);

    // Update the report status to 'available' and save the CDN URL
    await updateReportStatus(reportId, 'available', cdnFilePath);

    return pdfBuffer;
  } catch (err) {
    Logger.error('Error generating report', err);
    throw err;
  }
};

export const updateReportStatus = async (
  reportId: string,
  status: string,
  cdnFilePath?: string,
  error?: string
) => {
  await Report.findByIdAndUpdate(reportId, { status, cdnFilePath, errorMessage: error });
};

// Return True if the report is not cancelled
const checkCancelled = async (reportId: string) => {
  const report = await Report.findById(reportId);
  if (!report) throw errors.not_found('Report');

  return report.status !== 'cancelled';
};

const getDataForReport = async (reportId: string, language: TLanguage) => {
  // Get report sections
  const reportSections = await Report.findById(reportId).select('sections');

  // Check if the report has sections
  if (!reportSections || !reportSections.sections || reportSections.sections.length === 0) {
    Logger.error(`Report ${reportId} does not have sections`);
    await updateReportStatus(reportId, 'cancelled', undefined, 'NO_SECTIONS');
    throw errors.not_found('Report sections');
  }

  // Get enabled sections
  const enabledSections = await getEnabledReportSections();

  // Filter the sections that are enabled
  const enabledReportSections = reportSections.sections.filter((section) =>
    enabledSections.includes(section)
  );

  if (enabledReportSections.length === 0) {
    Logger.error(`Report ${reportId} does not have enabled sections`);
    await updateReportStatus(reportId, 'cancelled', undefined, 'NO_ENABLED_SECTIONS');
    throw errors.not_found('Enabled report sections');
  }

  // Get the data for the enabled sections
  const sectionDataFetchers: SectionFetcherFunctions = {
    proactivity: getProactivitySectionData,
    hosts: () => getHostSectionData(language),
    osUpdates: getOsUpdatesSectionData,
    technologyCoverage: getTechnologySectionData,
    compliance: getComplianceSectionData,
    inventory: getInventorySectionData,
    deployments: getDeploymentSectionData,
  };

  // Initialize an empty object to collect the data for each section
  const reportData: ReportData = {};

  try {
    // Call the data fetcher for each enabled section and save the data
    await Promise.all(
      enabledReportSections.map(async (section) => {
        reportData[section] = await (sectionDataFetchers[section] as () => Promise<any>)();
      })
    );

    return reportData;
  } catch (err) {
    Logger.error('Error getting data for report', err);
    await updateReportStatus(reportId, 'cancelled', undefined, 'GETTING_DATA_ERROR');
    throw err;
  }
};

const uploadPDFToCDN = async (reportId: string, reportName: string, pdfBuffer: Buffer) => {
  // Generate file name based on report name and date
  const filename = `${reportName}.pdf`;

  // Normalice the client name
  const rootFolder = CLIENT_NAME.replace(/ /g, '_');

  // Generate file path to upload
  const path = `${rootFolder}/reports`;

  try {
    // Upload the PDF to the CDN
    const cdnFilePath = await uploadFile(filename, pdfBuffer, path);

    return cdnFilePath;
  } catch (err) {
    Logger.error('Error uploading PDF to CDN', err);
    await updateReportStatus(reportId, 'cancelled', undefined, 'UPLOADING_TO_CDN_ERROR');
  }
};

export const getHostSectionData = async (language: TLanguage): Promise<HostsData> => {
  const [hostsDistribution, countByOS, countRecent, countTotal] = await Promise.all([
    getClientsCountPerCountry(),
    getClientsCountByOS(),
    getCountClientsAddedSince(Time.daysAgo(30)),
    RportClient.countDocuments({ deleted: false }),
  ]);

  hostsDistribution.map((h) => {
    h.country = t_country(language, h.country);
  });

  const hostsOSDistribution: { [key: string]: number } = {};

  // Transforming array to object
  countByOS.forEach((os) => {
    hostsOSDistribution[os.os] = os.count;
  });

  return {
    hostsDistribution,
    hostsOSDistribution,
    hostsStatus: {
      hostsAddedInTheLast30Days: countRecent,
      totalHosts: countTotal,
    },
  };
};

export const getOsUpdatesSectionData = async (): Promise<OsUpdatesData> => {
  const { hostSummary, vulnerabilitiesSummary, updatesOSDistribution, updatesByAgeRanges } =
    await getOsUpdateSummary();

  return {
    updates: {
      securityUpdates: vulnerabilitiesSummary.security_updates_available,
      updates: vulnerabilitiesSummary.updates_available,
    },
    hosts: {
      hostWithSecurityUpdates: hostSummary.host_with_security_updates,
      hostWithUpdates: hostSummary.host_with_updates,
      hostUpToDate: hostSummary.host_up_to_date,
    },
    updatesOSDistribution,
    updatesByAgeRanges,
  };
};

export const getTechnologySectionData = async (): Promise<TechnologyCoverageData> => {
  const { total, counts, hostsWithNoPlatforms } = await getTechnologyCoverageMetrics();

  // Give format to the data from platforms
  const technologyCoverageData = Object.keys(counts).map((technology) => {
    const technologyData = counts[technology];

    // Calculate the percentage coverage
    const percentageCoverage =
      toPercentage((technologyData.running + technologyData.notRunning) / total) + '%';

    // calculate hosts without technology
    const hostsWithoutTechnology = total - technologyData.running - technologyData.notRunning;

    return {
      technology,
      licenses: technologyData.licenses || 0,
      running: technologyData.running,
      stopped: technologyData.notRunning,
      hostsWithoutTechnology: hostsWithoutTechnology,
      percentageCoverage: percentageCoverage,
    };
  });

  return { technologies: technologyCoverageData, total, hostsWithNoPlatforms };
};

export const getComplianceSectionData = async (): Promise<ComplianceData> => {
  // Get all enabled and non deleted policies
  const policies = await Policy.find({ enabled: true, deleted: false });

  if (!policies || policies.length === 0) {
    return {
      generalComplianceStatus: {
        compliant: 0,
        not_compliant: 0,
        reached: 0,
        not_reached: 0,
        targeted: 0,
        percentage_compliant: '0',
        percentage_reached: '0',
      },
      policiesStatus: { enabled: 0, disabled: 0 },
      policyData: [],
      invalidPolicies: [],
    };
  }

  // Get all policies with valid groups
  const policyGroups = policies.map((policy) => policy.group);
  const validGroups: Types.ObjectId[] = await RportGroup.distinct('_id', {
    _id: { $in: policyGroups },
  });
  const validPolicies = [],
    invalidPolicies = [];

  for (const policy of policies) {
    if (validGroups.find((g) => g.equals(policy.group))) {
      validPolicies.push(policy);
    } else {
      invalidPolicies.push(policy);
    }
  }

  const [complianceMetrics, policiesStatusMetrics, policyData] = await Promise.all([
    getComplianceMetrics(),
    getPoliciesStatusMetrics(),
    Promise.all(validPolicies.map((policy) => getPolicyMetricsForReport(policy.id))),
  ]);

  // calculate the percentage of compliance and reached
  const percentageCompliant = toPercentage(complianceMetrics.compliant / complianceMetrics.reached);
  const percentageReached = toPercentage(complianceMetrics.reached / complianceMetrics.targeted);

  // Give format to the data
  const complianceData: ComplianceData = {
    generalComplianceStatus: {
      ...complianceMetrics,
      percentage_compliant: percentageCompliant,
      percentage_reached: percentageReached,
    },
    policiesStatus: policiesStatusMetrics,
    policyData,
    invalidPolicies,
  };

  return complianceData;
};

export const getInventorySectionData = async (): Promise<InventoryData> => {
  const [controlApplicationPolicyStatus, controlApplicationSummary] = await Promise.all([
    getControlAplicationPolicyStatus(),
    getControlApplicationSummary(),
  ]);

  return {
    policiesStatus: controlApplicationPolicyStatus,
    policiesSummary: controlApplicationSummary,
  };
};

export const getDeploymentSectionData = async (): Promise<DeploymentData> => {
  const [mostAffectedHosts, hostsAffected, totalHosts] = await Promise.all([
    getMostAffectedHosts(Time.daysAgo(30)),
    getHostsAffectedByDeployment(Time.daysAgo(30)),
    RportClient.countDocuments({ deleted: false }),
  ]);

  // Slice top 10 most affected hosts
  const mostAffectedHostsSliced = mostAffectedHosts.slice(0, 10);

  return {
    mostAffectedHosts: mostAffectedHostsSliced,
    hostsAffected,
    totalHosts,
  };
};

export const getProactivitySectionData = async (): Promise<ProactivityData> => {
  const [globalScore, hostDistribution, summary] = await Promise.all([
    getGlobalScore(),
    getHostScoreDistribution(),
    getSummaryScoreByArea(),
  ]);

  const formattedGlobalScore: { qualification: Qualification; percentage?: number } =
    globalScore as any;

  // Process Data
  const proactivityByHosts = {
    total: hostDistribution.total,
    distribution: hostDistribution.distribution,
  };

  const proactivityByAreas = summary;

  return { globalScore: formattedGlobalScore, proactivityByHosts, proactivityByAreas };
};

export const deleteReport = async (report: ReportDocument) => {
  // Start mongo transaction
  const session = await startSession();
  session.startTransaction();
  try {
    if (report.cdnFilePath) {
      // Delete the report from CDN
      await deleteFile(report.cdnFilePath);
    }

    // Delete the report from the database
    await Report.deleteOne({ _id: report._id }).session(session);

    // Commit mongo transaction
    await session.commitTransaction();
  } catch (err) {
    // Session abort
    await session.abortTransaction();
    throw err;
  }

  session.endSession();
};

export const deleteOrphanReports = async () => {
  // Normalice the client name
  const rootFolder = CLIENT_NAME.replace(/ /g, '_');

  // Generate file path to upload
  const path = `${rootFolder}/reports`;

  try {
    // Get the reports from the cdn with the name
    const cdnReports = await getFiles(path);

    // Check if there are reports in the cdn
    if (!cdnReports.Contents) return;
    if (cdnReports.Contents.length === 0) return;

    // Check which reports have a registered cdnPath on the reports collection
    const reports = await Report.find({ cdnFilePath: { $exists: true } });

    // Get the keys of the reports in the CDN that are not in the database
    const orphanReports = cdnReports.Contents.filter(
      (report) => !reports.some((r) => r.cdnFilePath === report.Key)
    );

    if (!orphanReports) return;

    Logger.info('Removing orphan reports from CDN');

    // Delete the orphan reports
    await Promise.all(
      orphanReports.map(async (report) => {
        if (!report.Key) return;
        await deleteFile(report.Key);
      })
    );
  } catch (err) {
    throw err;
  }
};

export const getEnabledReportSections = async () => {
  // Get all enabled services
  const enabledServices = await Service.find({
    enabled: true,
    internalName: { $in: ['rport', 'compliance', 'inventory', 'level-of-proactivity'] },
  });

  // Map enabled services with report section service map and get the sections
  const enabledSections = enabledServices
    .map((service) => reportSectionServiceMap[service.internalName])
    .flatMap((sections) => sections);

  return enabledSections;
};

export const addDefaultSectionToReports = async () => {
  // Get all reports with undefined sections or empty array
  const reports = await Report.find({ sections: { $exists: false } });

  // Add default sections to reports
  const defaultSections = await getEnabledReportSections();

  await Report.updateMany(
    { _id: { $in: reports.map((r) => r._id) } },
    { $set: { sections: defaultSections } }
  );
};

export const sendReportEmails = async (
  filename: string,
  report: Readable | Buffer,
  emails: string[],
  transmitter: string,
  language: TLanguage
) => {
  // Construct the attachments
  const attachments = [
    {
      filename: filename || 'batuta_report.pdf',
      content: report,
      contentType: 'application/pdf',
    },
  ];

  // Send the emails with the buffer attached
  Notifier.sendEmail({
    to: emails,
    subject: t('email', language, 'REPORT_MAIL_TITLE'),
    templateName: 'batutaReport',
    templateValues: {
      title: t('email', language, 'REPORT_MAIL_TITLE'),
      greeting: t('email', language, 'REPORT_MAIL_GREETING', { username: transmitter }),
      description: t('email', language, 'REPORT_MAIL_DESCRIPTION', {
        username: transmitter,
      }),
      farewell: t('email', language, 'REPORT_MAIL_FAREWELL'),
      team: t('email', language, 'BATUTA_TEAM'),
    },
    language: language,
    attachments,
  });
};

export const getReportFiltersFormatted = async () => {
  // Filters that are not needed to be shown
  const skippedFilter = [
    'errorMessage',
    'cdnFilePath',
    'protected',
    'enabled',
    'deleted',
    'updatedAt',
    'language',
  ];

  // Create the language options
  const languages = LANGUAGES.map((lang) => ({ key: lang, value: lang }));

  const extraFilters: { [key: string]: any } = {
    language: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: languages,
    },
  };

  return await Report.createFilter(extraFilters, skippedFilter);
};

export const getNewReportBuffer = async ({
  name,
  description,
  language,
  author,
  sections,
}: {
  name: string;
  description?: string;
  language: TLanguage;
  author: string;
  sections: ReportSectionT[];
}): Promise<Buffer> => {
  // Create new report in db
  const newReport = await Report.create({
    name,
    description,
    status: 'initializing',
    language,
    createdBy: author,
    sections,
  });

  // Start the process of creating the report and return buffer with the pdf
  const pdfBuffer = await generateReport(newReport.id, newReport.name, newReport.language);

  return pdfBuffer;
};
