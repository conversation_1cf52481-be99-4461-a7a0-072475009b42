import { Router } from 'express';
import { body, query } from 'express-validator';

import { hasPermission } from '@shared/middleware/auth-middleware';
import { validateBody } from '@shared/middleware/validation-middleware';

import {
  getApplicationFilters,
  getApplicationFilterOptions,
  getApplications,
  getApplicationHosts,
  getApplicationVersions,
  uninstallApplication,
  getUniqueApplications,
  getApplicationVendors,
} from '../controllers';

const router = Router();

router.get('/filters', hasPermission('read:inventory.application'), getApplicationFilters);

router.get(
  '/filters/suggest-values',
  [
    query('field').optional({ checkFalsy: false }).isString(),
    query('query').optional({ checkFalsy: false }).isString().isLength({ max: 20 }),
  ],
  validateBody,
  hasPermission('read:inventory.application'),
  getApplicationFilterOptions
);

router.get(
  '/',
  [
    query('filter').optional({ checkFalsy: false }).isString(),
    query('limit').isFloat({ min: 1, max: 500 }).optional({ checkFalsy: false }),
    query('afterKey[name]').optional({ checkFalsy: false }).isString(),
    query('afterKey[vendor]').optional({ checkFalsy: false }).isString(),
    query('afterKey[version]').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  hasPermission('read:inventory.application'),
  getApplications
);

router.get(
  '/hosts',
  [
    query('limit').isFloat({ min: 1, max: 500 }).optional({ checkFalsy: false }),
    query('name').isString().notEmpty(),
    query('version').optional({ checkFalsy: false }).isString().notEmpty(),
    query('vendor').optional({ checkFalsy: false }).isString().notEmpty(),
    query('afterKey').optional({ checkFalsy: false }).isString(),
    query('filter').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  hasPermission('read:inventory.application'),
  getApplicationHosts
);

router.get(
  '/unique',
  [
    query('filter').optional({ checkFalsy: false }).isString(),
    query('limit').isFloat({ min: 1, max: 500 }).optional({ checkFalsy: false }),
    query('afterKey[name]').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  hasPermission('read:inventory.application'),
  getUniqueApplications
);

router.get(
  '/vendors',
  [
    query('app_name').isString(),
    query('limit').isFloat({ min: 1, max: 500 }).optional({ checkFalsy: false }),
    query('afterKey[vendor]').optional({ checkFalsy: false }).isString(),
  ],
  validateBody,
  hasPermission('read:inventory.application'),
  getApplicationVendors
);

router.get(
  '/versions',
  [query('name').isString().notEmpty(), query('vendor').optional({ checkFalsy: false }).isString()],
  validateBody,
  hasPermission('read:inventory.application'),
  getApplicationVersions
);

router.post(
  '/uninstall',
  [
    body('hosts').isArray().isLength({ min: 1, max: 500 }),
    body('app.name').isString().notEmpty(),
    body('app.version').optional({ checkFalsy: false }).isString().notEmpty(),
    body('app.vendor').optional({ checkFalsy: false }).isString().notEmpty(),
  ],
  validateBody,
  hasPermission('execute:inventory.application'),
  uninstallApplication
);

export default router;
