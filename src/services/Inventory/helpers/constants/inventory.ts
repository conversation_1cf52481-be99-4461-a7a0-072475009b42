export const COLUMNS = {
  SOFTWARES_BY_HOSTS: [
    '_id',
    'Applications.Comments',
    'Applications.EstimatedSize',
    'Applications.HelpLink',
    'Applications.IdentifyingNumber',
    'Applications.InstallDate',
    'Applications.InstallLocation',
    'Applications.InstallSource',
    'Applications.Language',
    'Applications.Name',
    'Applications.NoRemove',
    'Applications.NoRepair',
    'Applications.QuietUninstallString',
    'Applications.UninstallString',
    'Applications.URLUpdateInfo',
    'Applications.Vendor',
    'Applications.Version',
    'Applications.Category',
  ],
  SOFTWARES_VERSIONS: ['Applications.Name', 'Applications.Vendor', 'Applications.Version'],
  SOFTWARES_NAMES: ['Applications.Name', 'Applications.Vendor'],
  INVENTORIES: ['_id', 'ApplicationsTimestamp'],
  INVENTORIES_TO_EXPORT: [
    '_id',
    'ApplicationsTimestamp',
    'Applications.Comments',
    'Applications.IdentifyingNumber',
    'Applications.InstallDate',
    'Applications.Name',
    'Applications.Vendor',
    'Applications.Version',
    'Applications.Category',
  ],
};

export const MAX_LIMIT_ON_SEARCH = 200;
export const MAX_KEYWORD_ON_SEARCH = 20;

export const APPLICATION_POLICY_CHECK_PERIOD = 15; // 15 minutes;

export enum TTL {
  INVENTORIES = 5 * 60,
  SOFTWARES_BY_HOST = 0,
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  SOFTWARES = 0,
  SOFTWARES_NAMES = 5 * 60,
  SOFTWARES_VERSIONS = 5 * 60,
  // eslint-disable-next-line @typescript-eslint/no-duplicate-enum-values
  HOST_BY_SOFTWARE = 0,
  INVENTORY_FILTERS = 20 * 60,
  INVENTORY_SOFTWARE_FILTERS = 20 * 60,
  INVENTORY_AUTOCOMPLETE = 5 * 60,
}

export enum STATUS {
  COMPLIANT = 'COMPLIANT',
  NON_COMPLIANT = 'NON_COMPLIANT',
  WITHOUT_INVENTORY = 'WITHOUT_INVENTORY',
}
