export interface ApplicationAggregation {
  filtered_applications: {
    doc_count: number;
    total_unique_apps: { value: number };
    unique_apps: {
      after_key: {
        name: string;
        vendor: string;
        version: string;
      };
      buckets: {
        key: {
          name: string;
          vendor: string;
          version: string;
        };
        doc_count: number;
      }[];
    };
  };
}

export interface UniqueAppAggregation {
  doc_count: number;
  filtered_applications: {
    doc_count: number;
    total_count: { value: number };
    app_names: {
      after_key: { name: string };
      buckets: [{ key: { name: string }; doc_count: number }];
    };
  };
}

export interface UniqueVendorAggregation {
  doc_count: number;
  filtered_by_name: {
    doc_count: number;
    vendors: {
      after_key: { vendor: string };
      buckets: {
        key: { vendor: string };
        doc_count: number;
        vendor_count: { value: number };
      }[];
    };
    total_vendors: { value: number };
  };
}

export interface HostWithCountAppsAggregation {
  filtered_applications: {
    doc_count: number; // This is the value of the total hosts with the application
  };
}

export interface HostWithAppsAggregation {
  filtered_applications: {
    doc_count: number;
    hosts: {
      doc_count: number; // This is the quantity of hosts with the application in the current page
      host_ids: {
        doc_count_error_upper_bound: number;
        sum_other_doc_count: number;
        buckets: [
          // This is the list of hosts with the application
          {
            key: string;
            doc_count: number;
            application_details: {
              hits: {
                total: {
                  value: number;
                  relation: string;
                };
                max_score: number;
                hits: [
                  // Here is the data of the application in the host
                  {
                    _index: string;
                    _id: string;
                    _score: number;
                    _source: {
                      hostId: string; // Host Id
                    };
                    fields: {
                      identifying_number: string[]; // Identifying number of the application for the host
                      install_date: string[]; // Install date of the application for the host
                      uninstall_string: string[]; // Uninstall String of the application for the host
                    };
                  },
                ];
              };
            };
          },
        ];
      };
    };
  };
}

export interface VersionAggregation {
  filtered_applications: {
    doc_count: number;
    unique_versions: {
      doc_count_error_upper_bound: number;
      sum_other_doc_count: number;
      buckets: { key: string; doc_count: number }[];
    };
  };
}

export interface HostAppAggregation {
  buckets: [
    {
      key: string;
      apps: {
        filtered_apps: {
          app_info: {
            hits: {
              hits: {
                _source: {
                  Version: string;
                  UninstallString: string;
                  Vendor: string;
                  Name: string;
                };
              }[];
            };
          };
        };
      };
    },
  ];
}

export interface AppUniqueValuesAggregation {
  doc_count: number;
  unique_values: {
    buckets: { key: string; doc_count: number }[];
  };
}

export interface AppHostData extends BasicApp {
  rportId: string;
  uninstallString: string;
}

export interface BasicApp {
  name: string;
  version?: string;
  vendor?: string;
}
