import { Request } from 'express';
import { parse } from 'date-fns';

import { RportClient } from '@services/Rport/schemas';

import {
  createHostDataInElastic,
  deleteDocument,
  getElasticDocuments,
  getUniqueFieldValues,
} from '../connections/elastic';

import { Logger } from '@shared/helpers/classes/logger.class';
import { errors } from '@shared/utils/app-errors';
import { sortAndPaginate } from '@shared/utils/pagination';
import { flattenObject } from '@services/Inventory/helpers/utils';
import {
  getSuggestedValuesFromField,
  parseSchemaToFieldFormat,
} from '@shared/helpers/classes/schema-utils.class';

import { getValidHostFields } from '@services/Rport/helpers/functions';
import prescripts from '@services/Rport/helpers/prescripts';
import { executeCommandOnClient } from '@services/Rport/helpers/connections/rport';

import {
  buildElasticsearchQuery,
  isUninstallAppPendingOnHost,
  parseInventoryFilters,
  selectInventoryParser,
} from '../utils';

import {
  API_BASE_URL,
  CDN_BASE_URL,
  HOST_CLIENT_TOKEN,
  ELASTIC_INVENTORY_INDEX,
} from '@shared/constants/env';

import { HOST_SKIPPED_FIELDS } from '@services/Rport/helpers/constants/groups';

import { COLUMNS } from '../constants';

import { userHasPermissionToFunctionality } from '@root/shared/helpers/functions/validate-permission.function';
import {
  getCVEsTotalFromSoftwareList,
  getTotalVulnsByHostId,
} from '@root/services/Vulnerabilities/helpers/vulnerabilities.functions';
import { UserDocument } from '@root/shared/schemas';

export const getWindowsAppsInventoryFromHost = async (hostId: string) => {
  let hostsExistsInElastic;

  try {
    hostsExistsInElastic = await createHostDataInElastic(hostId);
  } catch (error) {
    Logger.error(error);
    return `Unable to send inventory command to ${hostId}. Error: Host does not exist in Elastic.`;
  }

  if (!hostsExistsInElastic) {
    return `Unable to send inventory command to ${hostId}. Error: Host does not exist in Elastic.`;
  }

  let inventoryCommand = `$tempfile = "$(Get-Date -Format FileDateTime)-get-inventory.ps1"; Invoke-WebRequest -Uri "${CDN_BASE_URL}/inventory/windows/Get-Inventory.ps1" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -gw "${API_BASE_URL}/gw/host" -ct "${HOST_CLIENT_TOKEN}" -in "${ELASTIC_INVENTORY_INDEX}" -cat "Applications"; Remove-Item $tempfile 2>&1 > $null`;

  if (prescripts.windows) {
    inventoryCommand = prescripts.windows.addPrescripts(inventoryCommand);
  }

  try {
    await executeCommandOnClient(hostId, inventoryCommand, 300, 'powershell');
    return `Sent inventory command to ${hostId}`;
  } catch (error) {
    return `Unable to send inventory command to ${hostId}. Error: ${error}`;
  }
};

export const getInventories = async (
  columns: string[] = COLUMNS.INVENTORIES,
  offset: number = 0,
  limit: number | undefined = 500,
  filter: string = '',
  sort: string = '' // Wait for the proper sorting implementation
): Promise<{
  meta: { offset: number; count: number; resources: number };
  data: Record<string, any>[];
}> => {
  // Split Filters
  const { mongoQuery, elasticFilters } = await parseInventoryFilters(filter);

  // Apply host filters in mongo
  const hosts = await RportClient.find({ ...mongoQuery, deleted: false }) // Find non deleted hosts
    .select('rportId name')
    .sort('name');
  // Create a hashmap for the hosts
  const hostnameMap = new Map<string, string>();
  // Fill the hashmap with the hosts
  hosts.forEach((element) => hostnameMap.set(element.rportId, element.name));

  // Get query for elastic with _ids for hosts
  const elasticQuery = buildElasticsearchQuery(
    elasticFilters,
    {
      terms: {
        'hostId.keyword': hosts.map((host) => host.rportId),
      },
    },
    { 'Applications.Category': 'Applications' }
  );

  // Get data from elastic
  const { meta, data } = await getElasticDocuments({
    index: ELASTIC_INVENTORY_INDEX!,
    columns,
    offset,
    limit,
    query: elasticQuery,
    sortField: 'ApplicationsTimestamp', // Sort  by timestamp by default
    sortOrder: 'desc',
  });

  // Select parser based on columns
  const selectParser = selectInventoryParser(columns);

  // Join data with hostnames
  const inventories = data.map((inventory: Record<string, any>) => {
    // Get host name from hashmap
    const hostname = hostnameMap.get(inventory._id);

    // Parse data
    const parsedData = selectParser(inventory);

    return {
      ...parsedData,
      hostname: hostname ? hostname : '',
    };
  });

  return {
    data: inventories,
    meta,
  };
};

export const _getInventoriesToExport = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = ''
) => {
  const { data } = await getInventories(COLUMNS.INVENTORIES_TO_EXPORT, offset, 10000, filter, sort);

  // Use formatInventory
  const inventories: Record<string, any>[] = [];

  // Loop to each host
  for (let i = 0; i < data.length; i++) {
    const hostInventory = data[i];

    // map the applications
    const applications =
      hostInventory.applications?.map((app: Record<string, any>) => ({
        description: app.Comments,
        identifyingNumber: app.IdentifyingNumber,
        version: app.Version,
        vendor: app.Vendor,
        installDate: app.InstallDate && parse(app.InstallDate, 'yyyyMMdd', new Date()),
        name: app.Name,
        // destructuring host inventory data context
        hostName: hostInventory.hostname,
        lastUpdate: hostInventory.lastUpdate,
        _id: hostInventory._id,
      })) || [];

    // add the host inventory to the inventories
    inventories.push(...applications);
  }

  return inventories;
};

export const getHostInventoryById = async (
  rportId: string,
  limit: number,
  offset: number,
  user: UserDocument | undefined
) => {
  // Format the filter for getInventories
  const filter = `rportId:IS:${rportId}`;

  // Getting the inventory
  const { data } = await getInventories(COLUMNS.SOFTWARES_BY_HOSTS, 0, 1, filter);

  // Get Host from mongo
  const host = await RportClient.findOne({ rportId, deleted: false, enabled: true });
  if (!host) throw errors.not_found('Host');
  if (host.deleted) throw errors.not_found('Host');
  if (!host.enabled) throw errors.host_not_enabled();

  // Check if there is data
  if (data.length === 0) {
    return {
      _id: rportId,
      hostname: host.name,
      applications: { meta: { offset, count: 0, resources: 0 }, data: [] },
    };
  }

  // Getting inventory
  const inventory = data[0];

  const applications: {
    identifyingNumber: string;
    name: string;
    version: string;
    vendor: string;
    description: string;
    installDate?: Date;
    uninstallString: string;
    uninstalling: boolean;
  }[] = [];

  const applicationPromises =
    inventory.applications?.map(async (application: any) => {
      const app = {
        name: application.Name,
        ...(application.Vendor !== 'UNKNOWN' && { vendor: application.Vendor }),
        ...(application.Version !== 'UNKNOWN' && { version: application.Version }),
      };

      const hostsWithPendingUninstall = await isUninstallAppPendingOnHost(app, rportId);

      applications.push({
        identifyingNumber: application.IdentifyingNumber,
        name: application.Name,
        version: application.Version,
        vendor: application.Vendor,
        description: application.Comments,
        installDate: application.InstallDate
          ? parse(application.InstallDate, 'yyyyMMdd', new Date())
          : undefined,
        uninstallString: application.UninstallString,
        uninstalling: hostsWithPendingUninstall,
      });
    }) || [];

  await Promise.all(applicationPromises);

  const {
    currentOffset,
    data: paginatedData,
    totalItems,
  } = sortAndPaginate(applications, offset, limit, 'name', 'asc');

  const payload: any = {
    _id: rportId,
    hostname: host.name,
    applications: {
      meta: {
        offset: +currentOffset,
        count: totalItems,
        resources: paginatedData.length,
      },
      data: paginatedData,
    },
  };

  // Check if user has permission to view data about vulnerabilities
  if (user) {
    const userHasPermission = await userHasPermissionToFunctionality(
      user,
      'read:vulnerabilities.vulnerabilities'
    );
    if (userHasPermission) {
      const countVulnInHost = await getTotalVulnsByHostId(rportId);
      payload.haveVulns = countVulnInHost > 0;

      const { data: vulnData } = await getCVEsTotalFromSoftwareList(paginatedData);
      payload.applications.data = vulnData;
    }
  }

  return payload;
};

export const exportInventory = async (
  filter: any,
  limit?: number,
  offset?: number,
  sort?: string,
  req?: Request | Record<string, any>
) => {
  // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
  const rportId = req?.params?.rportId!;
  const response = await getHostInventoryById(rportId, 10000, 0, req?.user);

  return response.applications.data;
};

export const getInventoryFilters = () => {
  // Get valid fields for RportClients
  const hostRules = parseSchemaToFieldFormat(RportClient.schema, [
    ...HOST_SKIPPED_FIELDS,
    'enabled',
    'uninstallingStartingTime',
    'uninstalling',
    'createdAt',
    'updatedAt',
    '_id',
    'addressHistory.date',
    'updatesRefreshedAt',
    'updatesStatus.rebootRequired',
    'updatesStatus.isSecurityUpdate',
    'updatesStatus.title',
    'platforms.lastChangeAt',
    'platforms.checkedAt',
  ]);

  const parsedHostFilters = flattenObject(hostRules);

  // Get valid fields for Elastic side filtering (inventory)
  // const { filter } = await getAppFiltersElastic();
  // Give format, merge and return

  return {
    ...parsedHostFilters,
    'Applications.Category': {
      type: 'STRING',
      operators: ['IS', 'IS_NOT', 'CONTAINS', 'NOT_CONTAINS'],
    },
  };
};

export const getAutoCompleteValuesInventory = async (field: string, query: string) => {
  // Get valid fields from RportClients
  const validRuleFields = getValidHostFields();

  // Check if field is for mongo
  if (validRuleFields.includes(field)) {
    return await getSuggestedValuesFromField(field, query, RportClient);
  }

  return await getUniqueFieldValues(field, query, 20);
};

export const removeHostInventory = async (rportId: string) => {
  try {
    await deleteDocument(ELASTIC_INVENTORY_INDEX!, rportId);
  } catch (error) {
    Logger.error('Unable to delete host in Elastic:', error);
  }
};
