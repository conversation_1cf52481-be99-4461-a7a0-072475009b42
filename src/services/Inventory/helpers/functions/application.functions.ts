import { RportClient } from '@services/Rport/schemas';

import {
  ApplicationAggregation,
  BasicApp,
  HostAppAggregation,
  HostWithAppsAggregation,
  HostWithCountAppsAggregation,
  VersionAggregation,
  UniqueAppAggregation,
  UniqueVendorAggregation,
} from '../models/';

import {
  getAvailableFields,
  getUniqueFieldValues,
  runElasticRawQuery,
} from '../connections/elastic';

import { errors } from '@shared/utils/app-errors';

import { getHostNamesMap } from '@services/Rport/helpers/functions/client.functions';

import {
  buildElasticsearchQuery,
  generateFieldFilters,
  getApplicationsElasticQuery,
  getHostWithAppsQuery,
  countHostWithAppsQuery,
  getAppVersionsQuery,
  getAppInformationQuery,
  isUninstallAppPendingOnHost,
  getUniqueApplicationsQuery,
  getVendorsByAppNameQuery,
} from '../utils';

import { COLUMNS } from '../constants';

export const getApplicationsFromElastic = async (
  limit: number,
  filter: string = '',
  afterKey?: BasicApp
) => {
  // Get Host from mongo
  const validHosts = await RportClient.distinct('rportId', { enabled: true, deleted: false });

  const query = buildElasticsearchQuery(filter);

  const aggregation = getApplicationsElasticQuery(limit, query, validHosts, afterKey);

  const response = await runElasticRawQuery(aggregation);

  const aggResult = response.aggregations?.applications as ApplicationAggregation;

  if (!aggResult) {
    return { applications: [], afterKey: {} };
  }

  const { unique_apps, total_unique_apps } = aggResult.filtered_applications;

  const applications = unique_apps.buckets.map((bucket) => ({
    ...bucket.key,
    count: bucket.doc_count,
  }));

  return { applications, afterKey: unique_apps.after_key, total: total_unique_apps.value };
};

export const getHostsWithApplication = async (
  app: BasicApp,
  limit: number = 100,
  mongoQuery: Record<string, any> = {},
  afterKey?: string
) => {
  // Get Valid Hosts from mongo
  const validHosts = await RportClient.distinct('rportId', {
    ...mongoQuery,
    enabled: true,
    deleted: false,
  });

  const [resultResponse, countResponse] = await getAppsData(app, limit, validHosts, afterKey);

  const hosts = await processAppsData(resultResponse, app);

  const hostNamesMap = await getHostNamesMap(hosts);

  setHostNames(hosts, hostNamesMap);

  const count = countResponse.aggregations?.applications_count as HostWithCountAppsAggregation;
  const result = countResponse.aggregations?.applications_count as HostWithAppsAggregation;

  return {
    data: hosts,
    meta: {
      afterKey: hosts.length > 0 ? hosts[hosts.length - 1]._id : null,
      count: count.filtered_applications.doc_count ?? 0,
      resources: result.filtered_applications.doc_count ?? 0,
    },
  };
};

export const getAppVersionsFromElastic = async (name: string, vendor?: string) => {
  const query = getAppVersionsQuery(name, vendor);

  const response = await runElasticRawQuery(query);

  const filteredVersions = response.aggregations?.applications as VersionAggregation;

  const versions =
    filteredVersions.filtered_applications.unique_versions.buckets?.map((bucket) => ({
      version: bucket.key,
      count: bucket.doc_count,
    })) || [];

  return versions;
};

export const getHostApplicationsFromElastic = async (
  hostIds: string[],
  app: { name: string; vendor?: string; version?: string }
) => {
  const query = getAppInformationQuery(hostIds, app);

  const response = await runElasticRawQuery(query);

  const hostsData = response.aggregations?.hosts as HostAppAggregation;

  const hosts = hostsData.buckets
    .filter((bucket) => bucket.apps.filtered_apps.app_info.hits.hits.length === 1)
    .map((bucket) => {
      const host = bucket.key;
      const application = bucket.apps.filtered_apps.app_info.hits.hits[0]?._source;
      return {
        rportId: host,
        name: application.Name,
        version: application.Version,
        vendor: application.Vendor,
        uninstallString: application.UninstallString,
      };
    });

  return hosts;
};

export const getAppFiltersElastic = async () => {
  const availableFields = await getAvailableFields();
  const filteredAvailableFields: Record<string, string> = {};

  for (const key in availableFields) {
    if (key.startsWith('Applications.') && COLUMNS.SOFTWARES_BY_HOSTS.includes(key)) {
      filteredAvailableFields[key] = availableFields[key];
    }
  }

  const fields = Object.keys(filteredAvailableFields);
  const filter = generateFieldFilters(filteredAvailableFields, fields);

  return { fields, filter };
};

// Function to run the ElasticSearch queries
const getAppsData = async (
  app: BasicApp,
  limit: number,
  hostIds: string[] = [],
  afterKey?: string
) => {
  const resultQuery = getHostWithAppsQuery(app, limit, hostIds, afterKey);
  const countQuery = countHostWithAppsQuery(app, hostIds);

  return Promise.all([runElasticRawQuery(resultQuery), runElasticRawQuery(countQuery)]);
};

// Function to process ElasticSearch results
const processAppsData = async (resultResponse: any, app: BasicApp) => {
  const result = resultResponse.aggregations?.applications as HostWithAppsAggregation;
  const rawHosts = result.filtered_applications.hosts.host_ids.buckets;

  const hostIds: string[] = [];

  const hostPromises = rawHosts.map(async (host) => {
    const data = host.application_details.hits.hits[0];
    const hostId = data._source.hostId;
    hostIds.push(hostId);

    const hostsWithPendingUninstall = await isUninstallAppPendingOnHost(app, hostId);

    return {
      _id: hostId,
      installDate: data.fields.install_date[0] ?? null,
      uninstallString: data.fields.uninstall_string[0] ?? null,
      uninstalling: hostsWithPendingUninstall,
    };
  });

  const settledHostPromises = await Promise.allSettled(hostPromises);

  return settledHostPromises
    .filter((result) => result.status === 'fulfilled')
    .map((result) => result.value);
};

// Function to set host names
export const setHostNames = (
  hosts: any[],
  hostNamesMap: Map<string, { name: string; enabled: boolean }>
) => {
  hosts.forEach((host) => {
    host.hostname = hostNamesMap.get(host._id)?.name || '';
    host.enabled = hostNamesMap.get(host._id)?.enabled ?? false;
  });
};

export const getAutoCompleteValuesForApplications = async (field: string, query: string) => {
  // Get valid fields
  const { filter } = await getAppFiltersElastic();
  if (!filter[field]) throw errors.not_valid('Field');

  // Get unique values based of the field type
  return await getUniqueFieldValues(field, query);
};

export const getUniqueAppsFromElastic = async (
  limit: number,
  filter: string = '',
  afterKey?: { name: string }
) => {
  // Get Host from mongo
  const validHosts = await RportClient.distinct('rportId', { enabled: true, deleted: false });

  const query = buildElasticsearchQuery(filter);

  const aggregation = getUniqueApplicationsQuery(limit, query, validHosts, afterKey);

  const response = await runElasticRawQuery(aggregation);

  const aggResult = response.aggregations;

  if (!aggResult) {
    return { applications: [], afterKey: {} };
  }

  const {
    filtered_applications: {
      app_names,
      total_count: { value },
    },
  } = aggResult.applications as UniqueAppAggregation;

  const applications = app_names.buckets.map((bucket) => ({
    ...bucket.key,
    count: bucket.doc_count,
  }));

  return { applications, afterKey: app_names.after_key, total: value };
};

export const getUniqueVendorsFromElastic = async (
  appName: string,
  limit: number,
  afterKey?: { vendor: string }
): Promise<{
  vendors: { vendor: string; count: number }[];
  afterKey?: { vendor: string };
  total: number;
}> => {
  // Get Host from mongo
  const validHosts = await RportClient.distinct('rportId', { enabled: true, deleted: false });

  const aggregation = getVendorsByAppNameQuery(appName, limit, validHosts, afterKey);

  const response = await runElasticRawQuery(aggregation);

  const aggResult = response.aggregations;

  if (!aggResult) {
    return { vendors: [], total: 0 };
  }

  const {
    filtered_by_name: { total_vendors, vendors },
  } = aggResult.applications as UniqueVendorAggregation;

  const parsedVendors = vendors.buckets.map((bucket) => ({
    ...bucket.key,
    count: bucket.doc_count,
  }));

  return { vendors: parsedVendors, afterKey: vendors.after_key, total: total_vendors.value };
};
