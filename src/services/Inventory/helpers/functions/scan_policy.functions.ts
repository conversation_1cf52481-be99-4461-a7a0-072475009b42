import { Types } from 'mongoose';

import { QueuesTask } from '@services/Queues/schemas';
import { RportGroup } from '@services/Rport/schemas';
import { InventoryScanPolicy } from '../../schemas';
import { InventoryScanPolicyDocument } from '../../schemas/scan_policy.schema';

import { getOnlineHostsFromGroup } from '@services/Rport/helpers/functions';
import { getAndValidateClientsConnected } from '@services/Rport/helpers/utils/rport-platform.utils';
import { runFunctionInBatchWithDelay } from '@services/Rport/helpers/utils/rport-clients.utils';

import TasksFunctions from '@services/Queues/helpers/functions/task.function';
import { errors } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

import { getWindowsAppsInventoryFromHost } from './inventory.functions';

import { getNextCheck } from '@shared/utils/dates.utils';

import {
  FINISHED,
  PENDING,
  CANCELLED,
  IN_PROGRESS,
  VALIDATING,
} from '@services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@services/Queues/helpers/constants/tasks';
import { TaskParams } from '@root/services/Queues/helpers/types/task.types';
import { KnownQueuesTaskDocument } from '@root/services/Queues/schemas/task.schema';

// Cronner function for running the scan policies automatically
export const runScanPolicies = async () => {
  const policies = await InventoryScanPolicy.find({
    enabled: true,
    deleted: false,
    nextCheck: { $lt: new Date() },
  });

  policies.map(async (policy) => {
    try {
      await initScanPolicy(policy);
      Logger.info(`Inventory Scan Policy "${policy.name}" started successfully`);
    } catch (error) {
      Logger.error(`Inventory Scan Policy "${policy.name}" failed to start`, error);
    }
  });

  return 'Scan Policies executed successfully';
};

export const runInventoryScan = async (
  policy: InventoryScanPolicyDocument,
  connectedHosts: string[]
) => {
  // Run the scan on the hosts connected
  await runFunctionInBatchWithDelay(connectedHosts, executeScan, 100, 1000);

  // Set Task for disconnected hosts
  await setScanTask(policy._id, connectedHosts);

  await setNextCheck(policy);
  await InventoryScanPolicy.updateOne({ _id: policy._id }, { lastCheck: new Date() });
};

export const initScanPolicy = async (policy: InventoryScanPolicyDocument) => {
  const isGroupvalid = await checkScanPolicyGroup(policy);
  if (!isGroupvalid) throw errors.not_valid('Group');

  const connectedHosts = await getOnlineHostsFromGroup(policy.group);
  if (connectedHosts.length === 0) return;

  // Run the scan on the hosts connected
  await runInventoryScan(policy, connectedHosts);
};

export const executeScan = async (hostIds: string[]) => {
  for (const hostId of hostIds) {
    getWindowsAppsInventoryFromHost(hostId);
  }
};

export const setScanTask = async (
  inventoryScanPolicyId: Types.ObjectId,
  affectedHosts: string[]
) => {
  // Getting current task
  const currentTask = await getCurrentScanTask(inventoryScanPolicyId);
  // Finish the current task if exists
  if (currentTask) {
    await QueuesTask.updateOne<KnownQueuesTaskDocument<TASK_NAMES.SCAN>>(
      { _id: currentTask._id },
      {
        status: FINISHED,
        finished: new Date(),
        statusDetail: 'Task completed by a new inventory request for the same policy',
      }
    );
  }

  // Create task to scan the hosts disconnected
  await TasksFunctions.createTask(TASK_NAMES.SCAN, 1, {
    scanPolicyId: inventoryScanPolicyId,
    affectedHosts,
  });
};

const getCurrentScanTask = async (inventoryScanPolicyId: Types.ObjectId) => {
  const query = {
    name: TASK_NAMES.SCAN,
    params: {
      scanPolicyId: inventoryScanPolicyId,
    } as TaskParams<TASK_NAMES.SCAN>,
    status: { $in: [PENDING, IN_PROGRESS, VALIDATING] },
  };

  return await QueuesTask.findOne<KnownQueuesTaskDocument<TASK_NAMES.SCAN>>(query);
};

export const setNextCheck = async (policy: InventoryScanPolicyDocument) => {
  const nextCheck = getNextCheck(policy.periodicity, new Date());

  await InventoryScanPolicy.updateOne({ _id: policy._id }, { nextCheck });
};

export const checkScanPolicyGroup = async (policy: InventoryScanPolicyDocument) => {
  // Check if the group exists
  const groupExists = await RportGroup.findOne({
    _id: policy.group,
    deleted: false,
    enabled: true,
  });
  // If the group does not exist update policy
  if (!groupExists) {
    await InventoryScanPolicy.updateOne({ _id: policy._id }, { groupDeleted: true });
    return false;
  }

  return true;
};

export const cancelCurrentScanTask = async (
  inventoryScanPolicyId: Types.ObjectId,
  statusDetail?: string
) => {
  const currentTask = await getCurrentScanTask(inventoryScanPolicyId);
  if (currentTask) {
    await QueuesTask.updateOne<KnownQueuesTaskDocument<TASK_NAMES.SCAN>>(
      { _id: currentTask._id },
      { status: CANCELLED, statusDetail }
    );
  }
};

export const runInventoryScanOnHost = async (hostId: string) => {
  // Check if host is connected
  const { clientIdsConnected, clientIdsDisconnected } = await getAndValidateClientsConnected([
    hostId,
  ]);

  if (clientIdsConnected.length === 0 && clientIdsDisconnected.length === 0) {
    throw errors.not_valid('Host Selection');
  }

  // if host is online run the scan
  if (clientIdsConnected.length > 0) {
    await executeScan(clientIdsConnected);
    return 'Inventory Scan Started Successfully';
  }

  // if host is offline create a task to run the scan when it comes online
  await TasksFunctions.createTask(TASK_NAMES.SCAN_CLIENT, 1, {
    hostId: clientIdsDisconnected[0],
  });
  return 'Host is offline, scan will start when it comes online';
};
