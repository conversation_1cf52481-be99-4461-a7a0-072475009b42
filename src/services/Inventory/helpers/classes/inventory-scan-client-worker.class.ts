import AbstractWorker from '@services/Queues/helpers/worker/abstractWorker';

import { Task } from '@services/Queues/helpers/types';

import { getWindowsAppsInventoryFromHost } from '@services/Inventory/helpers/functions/inventory.functions';
import { getAndValidateClientsConnected } from '@services/Rport/helpers/utils/rport-platform.utils';

import { FINISHED, PENDING } from '@services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
export class InventoryScanPolicyOnClientWorker extends AbstractWorker<
  Task<TASK_NAMES.SCAN_CLIENT>
> {
  hostId: string;
  isConnected: boolean = false;

  constructor(task: Task<TASK_NAMES.SCAN_CLIENT>) {
    super(task);
    this.hostId = task.params.hostId;
  }

  async validate(): Promise<boolean> {
    try {
      const { clientIdsConnected } = await getAndValidateClientsConnected([this.hostId]);

      const isHostConnected = clientIdsConnected.includes(this.hostId);

      this.isConnected = isHostConnected;

      return isHostConnected;
    } catch {}

    return false;
  }

  async run() {
    try {
      await getWindowsAppsInventoryFromHost(this.hostId);

      return {
        _id: this.task._id,
        finished: new Date(),
        status: FINISHED,
        statusDetail: 'Inventory Scan Started Successfully',
      };
    } catch (error) {
      throw {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: error?.toString(),
      };
    }
  }
}
