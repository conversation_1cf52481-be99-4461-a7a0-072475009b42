import _ from 'lodash';
import { Types } from 'mongoose';
import { isEmpty, union } from 'lodash';

import { errors } from '@root/shared/utils/app-errors';
import { CANCELLED, PENDING } from '@root/services/Queues/helpers/constants/status';
import { QueuesTask } from '@root/services/Queues/schemas';
import { Logger } from '@root/shared/helpers/classes/logger.class';

import {
  ApplicationPolicy as ApplicationPolicySchema,
  ApplicationPolicyStatus,
} from '../../schemas';
import { ApplicationPolicyDocument } from '../../schemas/application_policy.schema';
import { ApplicationPolicyStatusDocument } from '../../schemas/application_policy_status.schema';
import {
  APPLICATION_POLICY_CHECK_PERIOD,
  STATUS_HOSTS_DETAILS_PIPELINE,
  STATUS,
} from '../constants';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
import { getNextCheck } from '@root/shared/utils/dates.utils';
import {
  checkChangesAndUpdateStatus,
  getHostsByCompliantStatus,
  getHostsByInventoryStatus,
} from './application_policy_status.utils';
import { TaskParams } from '@root/services/Queues/helpers/types/task.types';
import { KnownQueuesTaskDocument } from '@root/services/Queues/schemas/task.schema';
import { HostsReportModel } from '../models';
import { getStatusHostsDetailsPipeline } from '../constants/queries';

export const updateInventoryApplicationPolicyTask = async (
  inventoryApplicationPolicyId: string | Types.ObjectId,
  updates: Record<string, any>,
  extraParams: Record<string, any> = {},
  extraFilters: Record<string, any> = {}
) => {
  const task = await QueuesTask.findOneAndUpdate<KnownQueuesTaskDocument<TASK_NAMES.NOTIFY>>(
    {
      name: TASK_NAMES.NOTIFY,
      params: {
        inventoryApplicationPolicyId: new Types.ObjectId(inventoryApplicationPolicyId),
      } as TaskParams<TASK_NAMES.NOTIFY>,
      ...extraFilters,
    },
    updates,
    extraParams
  );

  if (!task) throw errors.not_found('Notify Task');

  return task;
};

export const updateInventoryApplicationPolicyCurrentTask = async (
  inventoryApplicationPolicyId: string | Types.ObjectId,
  updates: Record<string, any>,
  extraParams: Record<string, any> = {}
) => {
  const updatedTask = await updateInventoryApplicationPolicyTask(
    inventoryApplicationPolicyId,
    updates,
    extraParams,
    { status: PENDING }
  );

  return updatedTask;
};

export const updateInventoryApplicationPolicy = async (
  inventoryApplicationPolicyId: string | Types.ObjectId,
  updates: Record<string, any>,
  extraParams: Record<string, any> = {}
) => {
  const applicationPolicy = await ApplicationPolicySchema.findByIdAndUpdate(
    inventoryApplicationPolicyId,
    updates,
    extraParams
  );

  if (!applicationPolicy) throw errors.not_found('ApplicationPolicy');

  return applicationPolicy;
};

export const addStatusToApplicationPolicy = (
  policy: ApplicationPolicyDocument,
  policyStatus: ApplicationPolicyStatusDocument
) => {
  const policyWithStatus: Record<string, any> = { ...policy?.toObject() };
  if (policyStatus) {
    const { _id, status, rportIds, policyReport } = policyStatus;

    const { hostsWithInventory, hostsWithoutInventory } = getHostsByInventoryStatus(policyReport);
    const { hostsCompliant, hostsNonCompliant } = getHostsByCompliantStatus(hostsWithInventory);

    policyWithStatus.status = {
      id: _id.toString(),
      status,
      hosts: {
        total: rportIds.length,
        compliant: hostsCompliant.length,
        nonCompliant: hostsNonCompliant.length,
        withoutInventory: hostsWithoutInventory.length,
      },
    };
  }

  return policyWithStatus;
};

export const getHostsDetails = async (
  policyStatusId: Types.ObjectId | string | undefined,
  policyId: Types.ObjectId | string | undefined,
  query: Record<string, any> = {},
  extraQuery: Record<string, any> = {},
  sortQuery: Record<string, any> = {},
  offset: number = 0,
  limit: number = 100,
  extraPipeline: any[] = []
) => {
  const matchStage = policyStatusId
    ? { _id: new Types.ObjectId(policyStatusId) }
    : { policyId: new Types.ObjectId(policyId) };

  const [details, count] = await Promise.all([
    ApplicationPolicyStatus.aggregate([
      { $match: matchStage },
      ...STATUS_HOSTS_DETAILS_PIPELINE,
      ...extraPipeline,
      { $match: { $and: [query, extraQuery] } },
      { $sort: _.isEmpty(sortQuery) ? { name: 1 } : sortQuery },
      { $skip: +offset || 0 },
      ...(limit > 0 ? [{ $limit: +limit || 100 }] : []),
    ]),
    ApplicationPolicyStatus.aggregate([
      { $match: matchStage },
      ...STATUS_HOSTS_DETAILS_PIPELINE,
      ...extraPipeline,
      { $match: { $and: [query, extraQuery] } },
      { $count: 'count' },
    ]).then((result) => result[0]?.count ?? 0),
  ]);

  return { details, count };
};

export const updateApplicationPolicyStatusByChanges = async (
  policyReport: HostsReportModel[] | null,
  inventoryApplicationPolicy: ApplicationPolicyDocument,
  inventoryApplicationPolicyStatus: ApplicationPolicyStatusDocument,
  newClientIds: string[] = [],
  removedClientIds: string[] = []
): Promise<ApplicationPolicyStatusDocument> => {
  if (!inventoryApplicationPolicy || !inventoryApplicationPolicyStatus || !policyReport)
    return null;

  // Update check dates of application policy
  const currentDate = new Date();
  await ApplicationPolicySchema.findByIdAndUpdate(inventoryApplicationPolicy._id, {
    lastCheck: currentDate,
  });

  // Remove ids in removedClientIds
  let updatedPolicyStatus = await ApplicationPolicyStatus.findByIdAndUpdate(
    inventoryApplicationPolicyStatus._id,
    {
      $pull: {
        policyReport: { rportId: { $in: removedClientIds } },
      },
    },
    { new: true }
  );
  if (!updatedPolicyStatus) return null;

  // Array para almacenar las operaciones de actualización
  const bulkOperations = [];

  for (const clientId of newClientIds) {
    const currentReport = policyReport.find(({ rportId }) => rportId === clientId);
    const existingReportIndex = updatedPolicyStatus?.policyReport.findIndex(
      ({ rportId }) => rportId === clientId
    );

    if (existingReportIndex !== -1) {
      bulkOperations.push({
        updateOne: {
          filter: { _id: updatedPolicyStatus?._id, 'policyReport.rportId': clientId },
          update: { $set: { 'policyReport.$': currentReport } },
        },
      });
    } else {
      bulkOperations.push({
        updateOne: {
          filter: { _id: updatedPolicyStatus?._id },
          update: { $addToSet: { policyReport: currentReport } },
        },
      });
    }
  }

  // Ejecutar las operaciones de actualización como una operación de bulkWrite
  await ApplicationPolicyStatus.bulkWrite(bulkOperations);

  // Obtener el documento actualizado
  updatedPolicyStatus = await ApplicationPolicyStatus.findById(updatedPolicyStatus?._id);
  if (!updatedPolicyStatus) return null;

  // Update rportIds y status
  const newRportIds = union(updatedPolicyStatus.rportIds, newClientIds).filter(
    (id) => !removedClientIds.includes(id)
  );
  const newPolicyReport = updatedPolicyStatus.policyReport;

  const { hostsWithInventory, hostsWithoutInventory } = getHostsByInventoryStatus(newPolicyReport);

  const compliant =
    hostsWithoutInventory.length === 0 && hostsWithInventory.every(({ compliant }) => compliant);
  const newStatus = compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT;

  updatedPolicyStatus = await ApplicationPolicyStatus.findByIdAndUpdate(
    updatedPolicyStatus?._id,
    {
      $set: {
        rportIds: newRportIds,
        status: newStatus,
      },
    },
    { new: true }
  );

  return updatedPolicyStatus;
};

export const applicationPolicySwitch = async (
  applicationPolicyId: string | Types.ObjectId,
  userId: string | Types.ObjectId | null,
  updates: Record<string, any> = {}
) => {
  const pipeline: any[] = [{ $set: { enabled: { $not: '$enabled' } } }];
  if (userId) pipeline.push({ $set: { updatedBy: userId } });
  if (updates && !isEmpty(updates)) pipeline.push({ $set: updates });
  let updatedApplicationPolicy = await ApplicationPolicySchema.findByIdAndUpdate(
    applicationPolicyId,
    pipeline,
    { new: true }
  );
  if (!updatedApplicationPolicy) throw errors.not_found('ApplicationPolicy');

  const now = new Date();
  const nextNotifyDate = getNextCheck(APPLICATION_POLICY_CHECK_PERIOD, now);
  if (updatedApplicationPolicy.enabled) {
    const notifyTask = await updateInventoryApplicationPolicyTask(
      applicationPolicyId,
      {
        status: PENDING,
        nextCheck: now,
        pending: now,
        statusDetail: '',
      },
      {},
      { status: CANCELLED }
    );
    updatedApplicationPolicy = await ApplicationPolicySchema.findByIdAndUpdate(
      updatedApplicationPolicy._id,
      {
        $set: {
          nextCheck: now,
          nextNotify: nextNotifyDate,
        },
      },
      { new: true }
    );
    await QueuesTask.findByIdAndUpdate<KnownQueuesTaskDocument<TASK_NAMES.NOTIFY>>(
      notifyTask?._id,
      { retryAfter: nextNotifyDate }
    );

    try {
      await updateInventoryApplicationPolicyStatus(applicationPolicyId);
    } catch (error: any) {
      Logger.error(`Error updating inventory application policy status: ${error}`);
    }
  } else {
    await updateInventoryApplicationPolicyCurrentTask(applicationPolicyId, {
      cancelled: now,
      status: CANCELLED,
      statusDetail: 'The inventory application policy is disabled',
    });
  }

  return updatedApplicationPolicy;
};

export const updateInventoryApplicationPolicyStatus = async (
  applicationPolicyId: string | Types.ObjectId
) => {
  const policyStatus = await checkChangesAndUpdateStatus(applicationPolicyId, true);
  return policyStatus;
};

export const getHostsSoftwaresDetails = async (
  policyStatusId: Types.ObjectId | string | undefined,
  policyId: Types.ObjectId | string | undefined,
  query: Record<string, any> = {},
  extraQuery: Record<string, any> = {},
  sortQuery: Record<string, any> = {},
  offset: number = 0,
  limit: number = 100,
  extraPipeline: any[] = [],
  addWithoutInventory: boolean = false
) => {
  const matchStage = policyStatusId
    ? { _id: new Types.ObjectId(policyStatusId) }
    : { policyId: new Types.ObjectId(policyId) };

  const [details, count] = await Promise.all([
    ApplicationPolicyStatus.aggregate([
      { $match: matchStage },
      ...getStatusHostsDetailsPipeline(addWithoutInventory),
      ...extraPipeline,
      { $match: { $and: [query, extraQuery] } },
      { $sort: _.isEmpty(sortQuery) ? { name: 1 } : sortQuery },
      { $skip: +offset || 0 },
      ...(limit > 0 ? [{ $limit: +limit || 100 }] : []),
    ]),
    ApplicationPolicyStatus.aggregate([
      { $match: matchStage },
      ...getStatusHostsDetailsPipeline(addWithoutInventory),
      ...extraPipeline,
      { $match: { $and: [query, extraQuery] } },
      { $count: 'count' },
    ]).then((result) => result[0]?.count ?? 0),
  ]);

  return { details, count };
};

export const getAndUpdateNextDate = async (
  period: number,
  lastField: string,
  nextField: string,
  policyId: string | Types.ObjectId | undefined,
  retryAfter: Date
) => {
  if (!policyId) return null;

  const currentDate = new Date();
  let nextDate = getNextCheck(period, retryAfter);
  nextDate = new Date(Math.max(nextDate.getTime(), currentDate.getTime()));

  await ApplicationPolicySchema.findOneAndUpdate(
    { _id: policyId },
    { [lastField]: new Date(), [nextField]: nextDate }
  );

  return nextDate;
};
