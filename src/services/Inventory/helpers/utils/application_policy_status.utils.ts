import { QueryFilterModel, RelativeType } from '@root/shared/models';
import {
  buildExtraFilters,
  buildFilterObject,
} from '@root/shared/helpers/classes/schema-utils.class';

import {
  APPLICATION_POLICY_CHECK_PERIOD,
  APPLICATION_POLICY_STATUS_BY_RPORT_ID_CUSTOM_FILTERS,
  APPLICATION_POLICY_STATUS_CUSTOM_FILTERS,
  STATUS,
} from '../constants';
import { getHostIdsFromGroup } from '@root/services/Rport/helpers/functions';
import {
  ApplicationPolicyDocument,
  ApplicationPolicy as ApplicationPolicySchema,
} from '@root/services/Inventory/schemas/application_policy.schema';
import { Types } from 'mongoose';
import { errors } from '@root/shared/utils/app-errors';
import { RportGroup } from '@root/services/Rport/schemas';
import { applicationPolicySwitch, getAndUpdateNextDate } from './application_policy.utils';
import { ApplicationPolicyStatus, ApplicationPolicyStatusDocument } from '../../schemas';
import { isEqual } from 'lodash';
import { generateReport } from './application_policy_report.utils';
import { getHostApplicationsFromElastic, uninstallAppFromHosts } from '../functions';
import { HostsReportModel } from '../models';

const customPolicyStatusQueryBuilder = (key: string, relative: RelativeType, value: string) => {
  if (APPLICATION_POLICY_STATUS_CUSTOM_FILTERS.includes(key)) {
    return { [key]: buildFilterObject(relative, value) };
  }

  return {};
};

export const parseApplicationPolicyStatusExtraFilters = (filterObject: QueryFilterModel) => {
  return buildExtraFilters(filterObject, customPolicyStatusQueryBuilder);
};

const customPolicyStatusByRportIdQueryBuilder = (
  key: string,
  relative: RelativeType,
  value: string
) => {
  if (APPLICATION_POLICY_STATUS_BY_RPORT_ID_CUSTOM_FILTERS.includes(key)) {
    return { [key]: buildFilterObject(relative, value) };
  }

  return {};
};

export const parseApplicationPolicyStatusByRportIdExtraFilters = (
  filterObject: QueryFilterModel
) => {
  return buildExtraFilters(filterObject, customPolicyStatusByRportIdQueryBuilder);
};

export const saveApplicationPolicyStatus = async (
  policyReport: HostsReportModel[],
  clientIds: string[],
  policy: ApplicationPolicyDocument,
  policyStatus: ApplicationPolicyStatusDocument | null
): Promise<ApplicationPolicyStatusDocument | null> => {
  if (!policy || clientIds.length == 0) return Promise.resolve(null);

  const { hostsWithInventory, hostsWithoutInventory } = getHostsByInventoryStatus(policyReport);
  const compliant =
    hostsWithoutInventory.length === 0 && hostsWithInventory.every(({ compliant }) => compliant);

  let newPolicyStatus = null;
  if (policyStatus) {
    newPolicyStatus = await ApplicationPolicyStatus.findByIdAndUpdate(
      policyStatus?._id,
      {
        policyReport,
        rportIds: clientIds,
        status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
      },
      { new: true }
    );
  } else {
    newPolicyStatus = await ApplicationPolicyStatus.create({
      policyId: policy?._id,
      policyReport,
      rportIds: clientIds,
      status: compliant ? STATUS.COMPLIANT : STATUS.NON_COMPLIANT,
    });
  }

  return newPolicyStatus;
};

export const createTaskForAutomaticUninstall = async (
  policyReport: HostsReportModel[],
  clientIds: string[],
  policy: ApplicationPolicyDocument
) => {
  if (!policy || clientIds.length == 0) return null;

  const softwareToAutomaticUninstall = policy.policies.filter(
    (rule) => !rule.mustBePresent && rule.automaticUninstall
  );

  if (softwareToAutomaticUninstall.length === 0) return null;

  const softwareToUninstallMap: Record<
    string,
    { app: { name: string; vendor?: string }; hosts: string[] }
  > = {};
  for (const app of softwareToAutomaticUninstall) {
    const { _id, name, vendor } = app;
    softwareToUninstallMap[_id.toString()] = { app: { name, vendor }, hosts: [] };
  }

  const hostsWithReports = policyReport.filter(({ status }) => status !== STATUS.WITHOUT_INVENTORY);

  for (const host of hostsWithReports) {
    for (const software of host.policies) {
      const softwareId = software._id.toString();
      const softwareToUninstall = softwareToUninstallMap[softwareId];
      if (!software.mustBePresent && softwareToUninstall) {
        softwareToUninstallMap[softwareId].hosts.push(host.rportId);
      }
    }
  }

  for (const { app, hosts } of Object.values(softwareToUninstallMap)) {
    if (hosts.length > 0) {
      try {
        const hostsWithApps = await getHostApplicationsFromElastic(hosts, app);
        await uninstallAppFromHosts(hostsWithApps, app, {});
      } catch (error) {}
    }
  }
};

export const checkChangesAndUpdateStatus = async (
  policyId: string | Types.ObjectId,
  forceUpdate: boolean = false
) => {
  const policy = await ApplicationPolicySchema.findById(policyId);
  if (!policy) throw errors.not_found('Application Policy');

  if (!policy.enabled) return false;

  const group = await RportGroup.findById(policy.group);
  if (!group || group.deleted || !group.enabled) {
    await applicationPolicySwitch(policyId, null, { groupDeleted: true });
    return false;
  }

  const policyStatus = await ApplicationPolicyStatus.findOne({ policyId });
  const clientIds = await getHostIdsFromGroup(policy.group);

  if (clientIds.length > 0 || forceUpdate) {
    const hasChanges = !isEqual(clientIds.sort(), policyStatus?.rportIds.sort());

    if (forceUpdate || !policyStatus || hasChanges) {
      const policyReport = await generateReport(clientIds, policy.policies);
      await saveApplicationPolicyStatus(policyReport!, clientIds, policy, policyStatus);
      await createTaskForAutomaticUninstall(policyReport!, clientIds, policy);

      await getAndUpdateNextDate(
        APPLICATION_POLICY_CHECK_PERIOD,
        'lastCheck',
        'nextCheck',
        policy._id,
        new Date()
      );
    }
  }
};

export const checkChangesAndUpdateAllStatus = async () => {
  const policies = await ApplicationPolicySchema.find({ enabled: true });

  for (const policy of policies) {
    await checkChangesAndUpdateStatus(policy._id);
  }
};

export const getHostsByInventoryStatus = (policyReport: HostsReportModel[]) => {
  const hostsWithoutInventory = [] as HostsReportModel[];
  const hostsWithInventory = [] as HostsReportModel[];

  for (const report of policyReport) {
    if (report.status === STATUS.WITHOUT_INVENTORY) {
      hostsWithoutInventory.push(report);
    } else {
      hostsWithInventory.push(report);
    }
  }

  return {
    hostsWithoutInventory,
    hostsWithInventory,
  };
};

export const getHostsByCompliantStatus = (policyReport: HostsReportModel[]) => {
  const hostsCompliant = [] as HostsReportModel[];
  const hostsNonCompliant = [] as HostsReportModel[];

  for (const report of policyReport) {
    if (report.status === STATUS.COMPLIANT) {
      hostsCompliant.push(report);
    } else {
      hostsNonCompliant.push(report);
    }
  }

  return {
    hostsCompliant,
    hostsNonCompliant,
  };
};
