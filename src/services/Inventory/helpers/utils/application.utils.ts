import { BasicApp } from '../models';

export const getApplicationsElasticQuery = (
  limit: number = 100,
  query: Record<string, any> = { bool: {} },
  hostIds: string[],
  afterKey?: BasicApp
) => {
  const Filterquery = { bool: { filter: [{ terms: { 'hostId.keyword': hostIds } }] } };

  return {
    size: 0,
    query: Filterquery,
    aggs: {
      applications: {
        nested: {
          path: 'Applications',
        },
        aggs: {
          filtered_applications: {
            filter: query,
            aggs: {
              total_unique_apps: {
                cardinality: {
                  script: {
                    source:
                      "doc['Applications.Name.keyword'].value + '|' + (doc['Applications.Vendor.keyword'].size() != 0 ? doc['Applications.Vendor.keyword'].value : 'UNKNOWN') + '|' + (doc['Applications.Version.keyword'].size() != 0 ? doc['Applications.Version.keyword'].value : 'UNKNOWN')",
                    lang: 'painless',
                  },
                },
              },
              unique_apps: {
                composite: {
                  size: limit,
                  sources: [
                    {
                      name: {
                        terms: {
                          field: 'Applications.Name.keyword',
                          order: 'asc',
                        },
                      },
                    },
                    {
                      vendor: {
                        terms: {
                          script: {
                            source:
                              "doc['Applications.Vendor.keyword'].size() != 0 ? doc['Applications.Vendor.keyword'].value : 'UNKNOWN'",
                            lang: 'painless',
                          },
                          order: 'asc',
                        },
                      },
                    },
                    {
                      version: {
                        terms: {
                          script: {
                            source:
                              "doc['Applications.Version.keyword'].size() != 0 ? doc['Applications.Version.keyword'].value : 'UNKNOWN'",
                            lang: 'painless',
                          },
                          order: 'asc',
                        },
                      },
                    },
                  ],
                  // add after key if provided
                  ...(afterKey && { after: afterKey }),
                },
              },
            },
          },
        },
      },
    },
  };
};

export const getHostWithAppsQuery = (
  app: BasicApp,
  limit: number = 100,
  hostIds: string[],
  afterKey?: string
) => {
  // Add params based on app conditionally
  const params = {
    ...(app.name && { app_name: app.name }),
    ...(app.vendor && { app_vendor: app.vendor }),
    ...(app.version && { app_version: app.version }),
  };

  // Build filter terms and remove undefined filters
  const filterTerms = [
    app.name && { term: { 'Applications.Name.keyword': app.name } },
    app.vendor && { term: { 'Applications.Vendor.keyword': app.vendor } },
    app.version && { term: { 'Applications.Version.keyword': app.version } },
  ].filter(Boolean);

  const filterQuery = [
    { terms: { 'hostId.keyword': hostIds } },
    afterKey && { range: { 'hostId.keyword': { gt: afterKey } } },
  ].filter(Boolean);

  // Generate Query
  return {
    size: 0,
    query: { bool: { filter: filterQuery } },
    aggs: {
      applications: {
        nested: {
          path: 'Applications',
        },
        aggs: {
          filtered_applications: {
            filter: {
              bool: {
                must: filterTerms,
              },
            },
            aggs: {
              hosts: {
                reverse_nested: {},
                aggs: {
                  host_ids: {
                    terms: {
                      field: 'hostId.keyword',
                      size: limit, // Handle pagination
                      order: { _key: 'asc' }, // Sorting to get consistent results
                    },
                    aggs: {
                      application_details: {
                        top_hits: {
                          size: 1,
                          _source: ['hostId'],
                          script_fields: {
                            install_date: {
                              script: {
                                source: `
                                  for (app in params._source.Applications) {
                                    if (app.Name == params.app_name && app.Vendor == params.app_vendor && app.Version == params.app_version) {
                                      return app.InstallDate;
                                    }
                                  }
                                  return null;
                                `,
                                lang: 'painless',
                                params: params,
                              },
                            },
                            identifying_number: {
                              script: {
                                source: `
                                  for (app in params._source.Applications) {
                                    if (app.Name == params.app_name && app.Vendor == params.app_vendor && app.Version == params.app_version) {
                                      return app.IdentifyingNumber;
                                    }
                                  }
                                  return null;
                                `,
                                lang: 'painless',
                                params: params,
                              },
                            },
                            uninstall_string: {
                              script: {
                                source: `
                                  for (app in params._source.Applications) {
                                    if (app.Name == params.app_name && app.Vendor == params.app_vendor && app.Version == params.app_version) {
                                      return app.UninstallString;
                                    }
                                  }
                                  return null;
                                `,
                                lang: 'painless',
                                params: params,
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  };
};

export const countHostWithAppsQuery = (app: BasicApp, hostIds: string[]) => {
  // Build filter terms and remove undefined filters
  const filterTerms = [
    { term: { 'Applications.Name.keyword': app.name } },
    app.vendor && { term: { 'Applications.Vendor.keyword': app.vendor } },
    app.version && { term: { 'Applications.Version.keyword': app.version } },
  ].filter(Boolean);

  // Generate the query
  return {
    size: 0,
    query: {
      bool: {
        filter: [{ terms: { 'hostId.keyword': hostIds } }],
      },
    },
    aggs: {
      applications_count: {
        nested: {
          path: 'Applications',
        },
        aggs: {
          filtered_applications: {
            filter: {
              bool: {
                must: filterTerms,
              },
            },
          },
        },
      },
    },
  };
};

export const getAppVersionsQuery = (name: string, vendor?: string) => {
  return {
    size: 0,
    aggs: {
      applications: {
        nested: {
          path: 'Applications',
        },
        aggs: {
          filtered_applications: {
            filter: {
              bool: {
                must: [
                  {
                    term: {
                      'Applications.Name.keyword': name,
                    },
                  },
                ],
                should: vendor
                  ? [
                      {
                        term: {
                          'Applications.Vendor.keyword': vendor,
                        },
                      },
                    ]
                  : [],
                minimum_should_match: vendor ? 1 : 0,
              },
            },
            aggs: {
              unique_versions: {
                terms: {
                  field: 'Applications.Version.keyword',
                  size: 10000,
                },
              },
            },
          },
        },
      },
    },
  };
};

export const getAppInformationQuery = (hostIds: string[], app: BasicApp) => {
  const appFilterTerms = [
    { term: { 'Applications.Name.keyword': app.name } },
    app.vendor && { term: { 'Applications.Vendor.keyword': app.vendor } },
    app.version && { term: { 'Applications.Version.keyword': app.version } },
  ].filter(Boolean);

  return {
    query: {
      bool: {
        must: [
          {
            terms: {
              'hostId.keyword': hostIds,
            },
          },
          {
            nested: {
              path: 'Applications',
              query: {
                bool: {
                  must: appFilterTerms,
                },
              },
            },
          },
        ],
      },
    },
    aggs: {
      hosts: {
        terms: {
          field: 'hostId.keyword',
          size: hostIds.length,
        },
        aggs: {
          apps: {
            nested: {
              path: 'Applications',
            },
            aggs: {
              filtered_apps: {
                filter: {
                  bool: {
                    must: appFilterTerms,
                  },
                },
                aggs: {
                  app_info: {
                    top_hits: {
                      _source: {
                        includes: [
                          'Applications.Name',
                          'Applications.Vendor',
                          'Applications.Version',
                          'Applications.UninstallString',
                        ],
                      },
                      size: 1,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    size: 0,
  };
};

export const getUniqueApplicationsQuery = (
  limit: number = 100,
  query: Record<string, any> = { bool: {} },
  hostIds: string[],
  afterKey?: { name: string }
) => {
  const Filterquery = { bool: { filter: [{ terms: { 'hostId.keyword': hostIds } }] } };

  return {
    size: 0,
    query: Filterquery,
    aggs: {
      applications: {
        nested: {
          path: 'Applications',
        },
        aggs: {
          filtered_applications: {
            filter: query,
            aggs: {
              app_names: {
                composite: {
                  size: limit,
                  sources: [
                    {
                      name: {
                        terms: {
                          field: 'Applications.Name.keyword',
                          order: 'asc',
                        },
                      },
                    },
                  ],
                  ...(afterKey && { after: afterKey }),
                },
                aggs: {
                  app_count: {
                    value_count: {
                      field: 'Applications.Name.keyword',
                    },
                  },
                },
              },
              total_count: {
                cardinality: {
                  field: 'Applications.Name.keyword',
                },
              },
            },
          },
        },
      },
    },
  };
};

export const getVendorsByAppNameQuery = (
  appName: string,
  limit: number = 100,
  hostIds: string[],
  afterKey?: { vendor: string }
) => {
  const Filterquery = {
    bool: {
      filter: [
        { terms: { 'hostId.keyword': hostIds } },
        {
          nested: {
            path: 'Applications',
            query: {
              term: {
                'Applications.Name.keyword': appName,
              },
            },
          },
        },
      ],
    },
  };

  return {
    size: 0,
    query: Filterquery,
    aggs: {
      applications: {
        nested: {
          path: 'Applications',
        },
        aggs: {
          filtered_by_name: {
            filter: {
              term: {
                'Applications.Name.keyword': appName,
              },
            },
            aggs: {
              vendors: {
                composite: {
                  size: limit,
                  sources: [
                    {
                      vendor: {
                        terms: {
                          script: {
                            source:
                              "doc['Applications.Vendor.keyword'].size() != 0 ? doc['Applications.Vendor.keyword'].value : 'UNKNOWN'",
                            lang: 'painless',
                          },
                          order: 'asc',
                        },
                      },
                    },
                  ],
                  ...(afterKey && { after: afterKey }),
                },
                aggs: {
                  vendor_count: {
                    value_count: {
                      field: 'Applications.Vendor.keyword',
                    },
                  },
                },
              },
              total_vendors: {
                cardinality: {
                  field: 'Applications.Vendor.keyword',
                },
              },
            },
          },
        },
      },
    },
  };
};
