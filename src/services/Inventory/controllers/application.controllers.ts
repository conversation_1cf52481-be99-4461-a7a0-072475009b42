import { Request<PERSON><PERSON><PERSON> } from 'express';

import { RportClient } from '@services/Rport/schemas';

import { BasicApp } from '../helpers/models';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@root/shared/utils/app-errors';

import {
  getAppFiltersElastic,
  getApplicationsFromElastic,
  getHostsWithApplication,
  getAppVersionsFromElastic,
  getHostApplicationsFromElastic,
  uninstallAppFromHosts,
  getAutoCompleteValuesForApplications,
} from '../helpers/functions';
import { parseInventoryFilters } from '../helpers/utils';
import {
  getUniqueAppsFromElastic,
  getUniqueVendorsFromElastic,
} from '../helpers/functions/application.functions';
import { userHasPermissionToFunctionality } from '@root/shared/helpers/functions/validate-permission.function';

import { getCVEsTotalFromSoftwareList } from '@root/services/Vulnerabilities/helpers/vulnerabilities.functions';

export const getApplicationFilters: RequestHandler = catchAsync(async (req, res) => {
  const { filter } = await getAppFiltersElastic();

  res.status(200).json({ filter });
});

export const getApplicationFilterOptions: RequestHandler = catchAsync(async (req, res) => {
  const { field, query } = req.query as { field: string; query: string };

  // Get unique values for the field
  const values = await getAutoCompleteValuesForApplications(field, query);

  res.status(200).json({ values });
});

export const getApplications: RequestHandler = catchAsync(async (req, res) => {
  const {
    limit = '25',
    filter = '',
    afterKey,
  } = req.query as {
    limit: string;
    filter: string;
    afterKey?: { name: string; vendor?: string; version?: string };
  };

  // Validating after key
  if (afterKey) {
    if (!afterKey.name) throw errors.not_valid('After Key Name');
    if (!afterKey.vendor) afterKey.vendor = 'UNKNOWN';
    if (!afterKey.version) afterKey.version = 'UNKNOWN';
  }

  // Get Applications from Elastic
  const {
    applications,
    afterKey: nextAfterKey,
    total,
  } = await getApplicationsFromElastic(+limit, filter, afterKey);

  let result = applications;

  // Check if user has permission to view vulnerabilities
  const userHasPermission = await userHasPermissionToFunctionality(
    req.user!,
    'read:vulnerabilities.vulnerabilities'
  );
  if (userHasPermission) {
    const { data } = await getCVEsTotalFromSoftwareList(applications);
    result = data;
  }

  res.status(200).json({
    data: result,
    meta: { count: total, resources: applications.length, afterKey: nextAfterKey },
  });
});

export const getApplicationHosts: RequestHandler = catchAsync(async (req, res) => {
  const {
    name,
    version,
    vendor,
    limit = '25',
    afterKey,
    filter,
  } = req.query as {
    name: string;
    version?: string;
    vendor?: string;
    limit?: string;
    afterKey?: string;
    filter?: string;
  };

  // Parse filter string to object
  const { mongoQuery } = await parseInventoryFilters(filter || '');

  const response = await getHostsWithApplication(
    { name, version, vendor },
    +limit,
    mongoQuery,
    afterKey
  );

  res.status(200).json(response);
});

export const uninstallApplication: RequestHandler = catchAsync(async (req, res) => {
  const { hosts, app } = req.body as {
    hosts: string[];
    app: BasicApp;
  };

  const validHosts = await RportClient.find({
    rportId: { $in: hosts },
    deleted: false,
    enabled: true,
  }).distinct('rportId');

  if (validHosts.length === 0) throw errors.not_valid('Some Hosts are');

  const hostsWithApps = await getHostApplicationsFromElastic(validHosts, app);

  const job = await uninstallAppFromHosts(hostsWithApps, app, req.user);

  res.status(201).json(job);
});

export const getUniqueApplications: RequestHandler = catchAsync(async (req, res) => {
  const {
    limit = '25',
    filter = '',
    afterKey,
  } = req.query as {
    limit: string;
    filter: string;
    afterKey?: { name: string };
  };

  // Validating after key
  if (afterKey) {
    if (!afterKey.name) throw errors.not_valid('After Key Name');
  }

  // Get Applications from Elastic
  const {
    applications,
    afterKey: nextAfterKey,
    total,
  } = await getUniqueAppsFromElastic(+limit, filter, afterKey);

  res.status(200).json({
    data: applications,
    meta: { count: total, resources: applications.length, afterKey: nextAfterKey },
  });
});

export const getApplicationVendors: RequestHandler = catchAsync(async (req, res) => {
  const {
    limit = '25',
    afterKey,
    app_name: appName,
  } = req.query as { app_name: string; limit: string; afterKey?: { vendor: string } };

  // Validating after key
  if (afterKey) {
    if (!afterKey.vendor) throw errors.not_valid('After Key Vendor');
  }

  // Get Applications from Elastic
  const {
    vendors,
    afterKey: nextAfterKey,
    total,
  } = await getUniqueVendorsFromElastic(appName, +limit, afterKey);

  res.status(200).json({
    data: vendors,
    meta: { count: total, resources: vendors.length, afterKey: nextAfterKey },
  });
});

export const getApplicationVersions: RequestHandler = catchAsync(async (req, res) => {
  const { name = '', vendor = '' } = req.query as { name: string; vendor?: string };

  const response = await getAppVersionsFromElastic(name, vendor);

  return res.status(200).json({ versions: response, meta: { count: response.length } });
});
