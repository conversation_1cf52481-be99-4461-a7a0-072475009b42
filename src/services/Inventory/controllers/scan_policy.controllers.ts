import { Types } from 'mongoose';
import { RequestHand<PERSON> } from 'express';

import { RportClient, RportGroup } from '@services/Rport/schemas';
import { InventoryScanPolicy, InventoryScanPolicyDocument } from '../schemas';

import { GetAllQuery } from '@shared/types';

import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';

import { getHostCount } from '@services/Rport/helpers/functions';

import {
  cancelCurrentScanTask,
  initScanPolicy,
  runInventoryScanOnHost,
} from '../helpers/functions/scan_policy.functions';

export const inventoryScanPolicyFilters: RequestHandler = catchAsync(async (_, res) => {
  const extraFilters = {};
  const skippedFilter: string[] = [
    'deleted',
    'createdAt',
    'updatedAt',
    'groupDeleted',
    'protected',
    'description',
  ];

  const [filter, fields] = await InventoryScanPolicy.createFilter(extraFilters, skippedFilter);
  res.status(200).json({ filter, fields });
});

export const getAllInventoryScanPolicies: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery] = InventoryScanPolicy.parseFilter(filter, sort);

  const [inventoryScanPolicies, filteredResultsCount] = await Promise.all([
    InventoryScanPolicy.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0)
      .populate('group', ['name']),
    InventoryScanPolicy.countDocuments(query),
  ]);

  const finalGroups: Record<string, any>[] = [];

  const policyPromises = inventoryScanPolicies.map(async (inventoryScanPolicy) => {
    const group = inventoryScanPolicy.group;
    let count = 0;
    if (!inventoryScanPolicy.groupDeleted) {
      count = (await getHostCount(group)) || 0;
    }

    finalGroups.push({ ...inventoryScanPolicy.toObject(), count });
  });

  await Promise.all(policyPromises);

  res.status(200).json({
    meta: {
      count: filteredResultsCount,
      resources: inventoryScanPolicies.length,
      offset: +(offset ?? 0),
    },
    data: finalGroups,
  });
});

export const getOneInventoryScanPolicy: RequestHandler = catchAsync(async (req, res) => {
  const inventoryScanPolicyId = req.params.id;

  const inventoryScanPolicy = await InventoryScanPolicy.findById(inventoryScanPolicyId);
  if (!inventoryScanPolicy || inventoryScanPolicy.deleted) {
    throw errors.not_found('Inventory Scan Policy');
  }

  const count = (await getHostCount(inventoryScanPolicy.group)) || 0;

  res.status(200).json({ ...inventoryScanPolicy.toObject(), count });
});

export const createInventoryScanPolicy: RequestHandler = catchAsync(async (req, res) => {
  const { name, description, group, periodicity } = req.body as {
    name: string;
    group: string | Types.ObjectId;
    periodicity: number;
    description?: string;
  };

  const existingInventoryScanPolicy = await InventoryScanPolicy.findOne({ name, deleted: false });
  if (existingInventoryScanPolicy) throw errors.already_exists('Inventory Scan Policy');

  const newInventoryScanPolicy = await InventoryScanPolicy.create({
    name,
    periodicity,
    group,
    description,
    nextCheck: new Date(),
    lastCheck: new Date(),
    updatedBy: req.user?.email,
    createdBy: req.user?.email,
  });

  await initScanPolicy(newInventoryScanPolicy);

  res.status(201).json(newInventoryScanPolicy);
});

export const updateInventoryScanPolicy: RequestHandler = catchAsync(async (req, res) => {
  const inventoryScanPolicyId = req.params.id;

  const { name, description, group, periodicity } = req.body as {
    name?: string;
    description?: string;
    group?: string;
    periodicity?: number;
  };

  // Fetch the existing inventory scan policy
  const existingPolicy = await InventoryScanPolicy.findById(inventoryScanPolicyId);
  if (!existingPolicy || existingPolicy.deleted) {
    throw errors.not_found('Inventory Scan Policy');
  }
  if (!existingPolicy.enabled) throw errors.not_enabled('Inventory Scan Policy');

  const updateFields: Partial<InventoryScanPolicyDocument> = {};

  // Collect fields to update only if they are different from the existing values
  if (name !== undefined && name !== existingPolicy.name) updateFields.name = name;
  if (description !== undefined && description !== existingPolicy.description)
    updateFields.description = description;
  if (group !== undefined && existingPolicy.group.toString() !== group) {
    updateFields.group = group;
    const groupExists = await RportGroup.findOne({ _id: group, deleted: false, enabled: true });

    if (!groupExists) throw errors.not_found('Group');

    updateFields.groupDeleted = false;
  }
  if (periodicity !== undefined && periodicity !== existingPolicy.periodicity)
    updateFields.periodicity = periodicity;

  // Proceed if there are fields to update
  if (Object.keys(updateFields).length > 0) {
    const updatedPolicy = await InventoryScanPolicy.findByIdAndUpdate(
      inventoryScanPolicyId,
      { ...updateFields, updatedBy: req.user?.email },
      { runValidators: true, new: true }
    );

    if (!updatedPolicy) {
      throw errors.not_found('Inventory Scan Policy');
    }

    // Initialize scan policy if group or periodicity was updated
    if (updateFields.group !== undefined || updateFields.periodicity !== undefined) {
      await initScanPolicy(updatedPolicy);
    }

    res.status(200).json(updatedPolicy);
  } else {
    res.status(200).json(existingPolicy);
  }
});

export const setScanPolicyStatus: RequestHandler = catchAsync(async (req, res) => {
  const inventoryScanPolicyId = req.params.id;

  // Find the inventory scan policy
  let inventoryScanPolicy = await InventoryScanPolicy.findById(inventoryScanPolicyId);
  if (!inventoryScanPolicy || inventoryScanPolicy.deleted) {
    throw errors.not_found('Inventory Scan Policy');
  }

  if (inventoryScanPolicy.enabled) {
    // Disable the scan policy
    inventoryScanPolicy = await InventoryScanPolicy.findByIdAndUpdate(
      inventoryScanPolicyId,
      { enabled: false },
      { new: true }
    );
    if (!inventoryScanPolicy) throw errors.not_found('Inventory Scan Policy');
    // Cancel the current task
    await cancelCurrentScanTask(inventoryScanPolicy._id, 'Policy Disabled');
  } else {
    // Enable the scan policy
    inventoryScanPolicy = await InventoryScanPolicy.findByIdAndUpdate(
      inventoryScanPolicyId,
      { enabled: true },
      { new: true }
    );
    if (!inventoryScanPolicy) throw errors.not_found('Inventory Scan Policy');

    await initScanPolicy(inventoryScanPolicy);
  }

  res.status(200).json(inventoryScanPolicy);
});

export const removeInventoryScanPolicy: RequestHandler = catchAsync(async (req, res) => {
  const inventoryScanPolicyId = req.params.id;

  // Find the inventory scan policy
  const inventoryScanPolicy = await InventoryScanPolicy.findById(inventoryScanPolicyId);
  if (!inventoryScanPolicy || inventoryScanPolicy.deleted) {
    throw errors.not_found('Inventory Scan Policy');
  }

  await InventoryScanPolicy.findByIdAndUpdate(inventoryScanPolicyId, { deleted: true });

  // Cancel the current task
  await cancelCurrentScanTask(inventoryScanPolicy._id, 'Policy Removed');

  res.status(204).json({});
});

export const scanByPolicyId: RequestHandler = catchAsync(async (req, res) => {
  const inventoryScanPolicyId = req.params.id;

  const inventoryScanPolicy = await InventoryScanPolicy.findById(inventoryScanPolicyId);
  if (!inventoryScanPolicy || inventoryScanPolicy.deleted) {
    throw errors.not_found('Inventory Scan Policy');
  }

  await initScanPolicy(inventoryScanPolicy);

  res.status(200).json(inventoryScanPolicy);
});

export const scanByRportId: RequestHandler = catchAsync(async (req, res) => {
  const rportId = req.params.rportId;

  const host = await RportClient.findOne({
    rportId,
    deleted: false,
  });
  if (!host) throw errors.not_found('Client');
  if (!host.enabled) throw errors.host_not_enabled();
  if (host.osKernel !== 'windows') throw errors.not_supported('Inventory Scan');

  const response = await runInventoryScanOnHost(rportId);

  res.status(201).json({ message: response });
});
