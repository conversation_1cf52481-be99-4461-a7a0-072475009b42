import { isEmpty } from 'lodash';
import { Request, RequestHandler } from 'express';

import catchAsync from '@shared/utils/catch-async';

import { errors } from '@shared/utils/app-errors';
import { GetAllQuery } from '@root/shared/types';

import { ApplicationPolicy, ApplicationPolicyStatus } from '../schemas';
import { getHostIdsFromGroup } from '@root/services/Rport/helpers/functions';
import {
  getHostsDetails,
  getHostsSoftwaresDetails,
} from '../helpers/utils/application_policy.utils';
import { ApplicationPolicyStatusDocument } from '../schemas/application_policy_status.schema';
import { APPLICATION_POLICY_STATUS_CUSTOM_FILTERS, STATUS } from '../helpers/constants';
import {
  ArrayRelative,
  BooleanRelative,
  NumberRelative,
  StringRelative,
} from '@root/shared/models';
import { exportFileOf } from '@root/services/Export/controllers';
import { ServiceResponse } from '@root/shared/models/service-response';
import { RportGroup } from '@root/services/Rport/schemas';
import {
  parseApplicationPolicyStatusExtraFilters,
  parseApplicationPolicyStatusByRportIdExtraFilters,
} from '../helpers/utils';
import { generateReportAndUpdateApplicationPolicyStatusByChanges } from '../helpers/utils/application_policy_report.utils';

export const getAllApplicationPolicyStatuses: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const [query, sortQuery] = ApplicationPolicyStatus.parseFilter(filter, sort);

  const [policyStatuses, filteredResultsCount] = await Promise.all([
    ApplicationPolicyStatus.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0),
    ApplicationPolicyStatus.countDocuments(query),
  ]);

  return ServiceResponse.get(policyStatuses, filteredResultsCount, +(offset ?? 0)).send(res);
});

export const getOneApplicationPolicyStatus: RequestHandler = catchAsync(async (req, res) => {
  const policyStatusId = req.params.id;

  const policyStatus = await ApplicationPolicyStatus.findById(policyStatusId);
  if (!policyStatus) throw errors.not_found('ApplicationPolicyStatus');

  res.status(200).json(policyStatus);
});

export const getOneApplicationPolicyStatusByPolicyId: RequestHandler = catchAsync(
  async (req, res) => {
    const policyId = req.params.id;

    const policyStatus = await ApplicationPolicyStatus.findOne({ policyId });
    if (!policyStatus) throw errors.not_found('ApplicationPolicyStatus');

    res.status(200).json(policyStatus);
  }
);

const _getFieldsFromHostsData = (item: Record<string, any>, field: string) => {
  if (['total', 'notPassed', 'passed'].includes(field)) return item.rules[field];
  return item[field];
};

const _getOneApplicationPolicyStatusHosts = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = '',
  req: Request
) => {
  const policyId = req.params.id;

  const [query, sortQuery, extraFilter] = ApplicationPolicyStatus.parseFilter(
    filter,
    sort,
    APPLICATION_POLICY_STATUS_CUSTOM_FILTERS
  );
  const extraQuery = parseApplicationPolicyStatusExtraFilters(extraFilter);

  const { details, count } = await getHostsDetails(
    undefined,
    policyId,
    query,
    extraQuery,
    sortQuery,
    offset,
    limit
  );

  return { details, count };
};

export const getOneApplicationPolicyStatusHosts: RequestHandler = catchAsync(async (req, res) => {
  const { filter, sort, limit, offset } = req.query as GetAllQuery;
  const policyStatusId = req.params.id;

  // Building the query based on the constructed filters
  const [query, sortQuery, extraFilter] = ApplicationPolicyStatus.parseFilter(
    filter,
    sort,
    APPLICATION_POLICY_STATUS_CUSTOM_FILTERS
  );
  const extraQuery = parseApplicationPolicyStatusExtraFilters(extraFilter);

  const { details: hostsDetails, count: hostsDetailsCount } = await getHostsDetails(
    policyStatusId,
    undefined,
    query,
    extraQuery,
    sortQuery,
    offset,
    limit
  );

  return ServiceResponse.get(hostsDetails, hostsDetailsCount, +(offset ?? 0)).send(res);
});

const _getOneApplicationPolicyStatusHostByRportId = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = '',
  req?: Request | Record<string, any>
) => {
  if (req === undefined) throw errors.internal_error();
  const policyId = req.params.id;

  const [query, sortQuery, extraFilter] = ApplicationPolicyStatus.parseFilter(
    filter,
    sort,
    APPLICATION_POLICY_STATUS_CUSTOM_FILTERS
  );
  const extraQuery = parseApplicationPolicyStatusByRportIdExtraFilters(extraFilter);

  const { details, count } = await getHostsSoftwaresDetails(
    undefined,
    policyId,
    query,
    extraQuery,
    sortQuery,
    offset,
    limit,
    [{ $match: { rportId: req.params.rportId } }]
  );
  if (!details.length) throw errors.not_found('ApplicationPolicyStatus');

  return { details, count };
};

export const getOneApplicationPolicyStatusHostByRportId: RequestHandler = catchAsync(
  async (req, res) => {
    const { filter, sort, limit, offset } = req.query as GetAllQuery;

    const { details, count } = await _getOneApplicationPolicyStatusHostByRportId(
      filter,
      limit,
      offset,
      sort,
      req
    );

    return ServiceResponse.get(details, count, +(offset ?? 0)).send(res);
  }
);

export const exportApplicationPolicyStatusHostByRportId: RequestHandler = catchAsync(
  async (req, res) => {
    return await exportFileOf(
      req,
      res,
      _getOneApplicationPolicyStatusHostByRportId,
      ({ details }) => details
    );
  }
);

export const getOneApplicationPolicyStatusHostsByPolicyId: RequestHandler = catchAsync(
  async (req, res) => {
    const { filter, sort, limit, offset } = req.query as GetAllQuery;

    const { details, count } = await _getOneApplicationPolicyStatusHosts(
      filter,
      limit,
      offset,
      sort,
      req
    );

    return ServiceResponse.get(details, count, +(offset ?? 0)).send(res);
  }
);

export const _getAllApplicationPolicyStatus = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = '',
  req?: Request | Record<string, any>
) => {
  if (req === undefined) throw errors.internal_error();
  const policyId = req.params.id;

  const policyStatus = await ApplicationPolicyStatus.findOne({ policyId }).populate('policyId', [
    'policies',
  ]);
  if (!policyStatus) throw errors.not_found('ApplicationPolicyStatus');

  const [query, sortQuery, extraFilter] = ApplicationPolicyStatus.parseFilter(
    filter,
    sort,
    APPLICATION_POLICY_STATUS_CUSTOM_FILTERS
  );
  const extraQuery = parseApplicationPolicyStatusByRportIdExtraFilters(extraFilter);
  const isExport = limit < 0;

  const { details } = await getHostsSoftwaresDetails(
    undefined,
    policyId,
    query,
    extraQuery,
    sortQuery,
    offset,
    limit,
    [],
    isExport
  );

  return details;
};

export const exportApplicationPolicyStatusHostsByPolicyId: RequestHandler = catchAsync(
  async (req, res) => {
    return await exportFileOf(req, res, _getAllApplicationPolicyStatus);
  }
);

export const applicationPolicyStatusFilters: RequestHandler = catchAsync(async (_, res) => {
  const skippedFilter: string[] = [];
  const [filter, fields] = await ApplicationPolicyStatus.createFilter(undefined, skippedFilter);

  res.status(200).json({ filter, fields });
});

export const applicationPolicyStatusHostsFilters: RequestHandler = catchAsync(async (_, res) => {
  const filter = {
    compliancePercentage: {
      type: 'NUMBER',
      relatives: Object.values(NumberRelative),
    },
    lastVerifiedAt: {
      type: 'DATE',
      relatives: Object.values(NumberRelative),
    },
    name: {
      type: 'STRING',
      relatives: Object.values(StringRelative),
    },
    status: {
      type: 'STRING',
      relatives: Object.values(StringRelative),
      options: Object.entries(STATUS).map(([key, value]) => ({ key, value })),
    },
    compliant: {
      type: 'BOOLEAN',
      relatives: Object.values(BooleanRelative),
    },
  };
  const fields = ['compliancePercentage', 'lastVerifiedAt', 'name', 'status', 'compliant'];
  res.status(200).json({ filter, fields });
});

export const applicationPolicyStatusHostByRportIdFilters: RequestHandler = catchAsync(
  async (_, res) => {
    const filter = {
      name: {
        type: 'STRING',
        relatives: Object.values(StringRelative),
      },
      vendor: {
        type: 'STRING',
        relatives: Object.values(StringRelative),
      },
      versions: {
        type: 'STRING',
        relatives: Object.values(ArrayRelative),
      },
      compliant: {
        type: 'BOOLEAN',
        relatives: Object.values(BooleanRelative),
      },
      mustBePresent: {
        type: 'BOOLEAN',
        relatives: Object.values(BooleanRelative),
      },
    };
    const fields = ['name', 'vendor', 'versions', 'compliant', 'mustBePresent'];
    res.status(200).json({ filter, fields });
  }
);

export const recheckApplicationPolicyStatus: RequestHandler = catchAsync(async (req, res) => {
  const { host, group } = req.body as {
    host: string;
    group: string;
  };

  let applicationPolicies: any[] = [];
  if (group) {
    // Get all application policies with this group
    const groupRecord = await RportGroup.findById(group);
    if (!groupRecord) throw errors.not_found('Group');

    applicationPolicies = await ApplicationPolicy.find({ group, enabled: true });
  } else if (host) {
    // Get groups with this host
    const groupsWithPolicyIds = await ApplicationPolicy.distinct('group', { enabled: true });

    const hostGroups = [];
    for (const groupId of groupsWithPolicyIds) {
      const group = await RportGroup.findById(groupId);
      if (isEmpty(group) || (group && !group.enabled)) continue;

      const hostsInGroup = await getHostIdsFromGroup(groupId);
      if (hostsInGroup.includes(host)) hostGroups.push(groupId);
    }

    // Get reports with this host
    const policyStatuses = await ApplicationPolicyStatus.find(
      { clientIds: host, deleted: false },
      { policyId: 1, _id: 0 }
    );
    const policyIds = policyStatuses.map(({ policyId }) => policyId);
    // Get application policies with this host
    applicationPolicies = await ApplicationPolicy.find({
      $or: [{ group: { $in: hostGroups } }, { id: { $in: policyIds } }],
      enabled: true,
    });
  }

  const policyIds = applicationPolicies.map(({ _id }) => _id);
  const policyStatuses = await ApplicationPolicyStatus.find({
    policyId: { $in: policyIds },
    deleted: false,
  });

  let newHosts: string[] = [];
  let removedHosts: string[] = [];
  for (const policy of applicationPolicies) {
    const policyStatus = policyStatuses.find(
      ({ policyId }) => policyId.toString() == policy._id.toString()
    ) as ApplicationPolicyStatusDocument;

    if (policyStatus) {
      const currentRportIds = await getHostIdsFromGroup(policy.group);

      newHosts = newHosts.concat(
        currentRportIds.filter((rportId) => !policyStatus?.rportIds.includes(rportId))
      );
      if (host && !newHosts.includes(host)) newHosts.push(host);
      removedHosts = removedHosts.concat(
        policyStatus.rportIds.filter((rportId) => !currentRportIds.includes(rportId))
      );

      generateReportAndUpdateApplicationPolicyStatusByChanges(
        policy,
        policyStatus,
        newHosts,
        removedHosts
      );
    }
  }

  res.status(200).json({ updatedStatusIds: policyIds });
});
