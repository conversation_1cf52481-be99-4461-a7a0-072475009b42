import { isEqual } from 'lodash';
import { Types } from 'mongoose';
import { RequestHandler } from 'express';

import catchAsync from '@shared/utils/catch-async';
import TasksFunctions from '@root/services/Queues/helpers/functions/task.function';

import { errors } from '@shared/utils/app-errors';
import { GetAllQuery } from '@root/shared/types';

import { ApplicationPolicyStatusDocument } from '../schemas/application_policy_status.schema';
import { ApplicationPolicy, ApplicationPolicyStatus } from '../schemas';
import { ApplicationPolicy as ApplicationPolicyType } from '../helpers/models';
import { ApplicationPolicyNotifications } from '../helpers/models/application_policy.model';
import {
  addStatusToApplicationPolicy,
  applicationPolicySwitch,
  updateInventoryApplicationPolicyStatus,
} from '../helpers/utils/application_policy.utils';

import { CANCELLED } from '@root/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@root/services/Queues/helpers/constants/tasks';
import { getNextCheck } from '@root/shared/utils/dates.utils';
import { ServiceResponse } from '@root/shared/models/service-response';
import {
  updateInventoryApplicationPolicy,
  updateInventoryApplicationPolicyCurrentTask,
} from '../helpers/utils';
import { Logger } from '@root/shared/helpers/classes/logger.class';

export const getAllApplicationPolicies: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const [query, sortQuery] = ApplicationPolicy.parseFilter(filter, sort);

  const [policies, filteredResultsCount] = await Promise.all([
    ApplicationPolicy.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name')
      .limit(limit ?? 100)
      .skip(offset ?? 0)
      .populate('group', ['name'])
      .populate('createdBy', ['email', 'name'])
      .populate('updatedBy', ['email', 'name']),
    ApplicationPolicy.countDocuments(query),
  ]);

  const statuses = await ApplicationPolicyStatus.find({
    policyId: { $in: policies.map(({ _id: id }) => id) },
  });

  res.status(200).json({
    meta: { count: filteredResultsCount, resources: policies.length, offset: +(offset ?? 0) },
    data: policies.map((policy) =>
      addStatusToApplicationPolicy(
        policy,
        statuses.find(
          ({ policyId }) => policyId.toString() == policy._id.toString()
        ) as ApplicationPolicyStatusDocument
      )
    ),
  });
});

export const getOneApplicationPolicy: RequestHandler = catchAsync(async (req, res) => {
  const policyId = req.params.id;

  const policy = await ApplicationPolicy.findById(policyId)
    .populate('group', ['name'])
    .populate('createdBy', ['email', 'name'])
    .populate('updatedBy', ['email', 'name']);
  if (!policy) throw errors.not_found('ApplicationPolicy');

  const status = await ApplicationPolicyStatus.findOne({ policyId });
  const policyWithStatus = addStatusToApplicationPolicy(policy, status);

  res.status(200).json(policyWithStatus);
});

export const createApplicationPolicy: RequestHandler = catchAsync(async (req, res) => {
  const userId = req.user?.id;
  const author = req.user!.email;
  const { name, group, periodicity, policies, notifications } = req.body as {
    name: string;
    group: string | Types.ObjectId;
    periodicity: number;
    notifications: ApplicationPolicyNotifications;
    policies: ApplicationPolicyType[];
  };

  const policy = await ApplicationPolicy.findOne({ name, deleted: false });
  if (policy) throw errors.already_exists('ApplicationPolicy');

  const policiesWithDefaults = policies.map((policy) => ({
    ...policy,
    automaticUninstall: policy.automaticUninstall ?? false,
  }));

  const newApplicationPolicy = await ApplicationPolicy.create({
    name,
    group,
    periodicity,
    policies: policiesWithDefaults,
    notifications,
    createdBy: userId,
    updatedBy: userId,
    nextNotify: getNextCheck(periodicity, new Date()),
  });

  // Notify task
  await TasksFunctions.createTask(
    TASK_NAMES.NOTIFY,
    1,
    {
      inventoryApplicationPolicyId: newApplicationPolicy._id,
    },
    {
      author,
      retryAfter: newApplicationPolicy.nextNotify,
    }
  );

  try {
    await updateInventoryApplicationPolicyStatus(newApplicationPolicy._id);
  } catch (error: any) {
    Logger.error(`Error updating inventory application policy status: ${error}`);
  }

  res.status(201).json({ policy: newApplicationPolicy });
});

export const updateApplicationPolicy: RequestHandler = catchAsync(async (req, res) => {
  const userId = req.user?.id;
  const applicationPolicyId = req.params.id;

  let policy = await ApplicationPolicy.findById(applicationPolicyId);
  if (!policy) throw errors.not_found('ApplicationPolicy');

  const rulesHasChanges =
    req.body.hasOwnProperty('policies') && !isEqual(policy.policies, req.body.policies);

  await ApplicationPolicy.findByIdAndUpdate(
    applicationPolicyId,
    { ...req.body, updatedBy: userId },
    { runValidators: true, new: true }
  );

  policy = await ApplicationPolicy.findById(applicationPolicyId)
    .populate('group', ['name'])
    .populate('createdBy', ['email', 'name'])
    .populate('updatedBy', ['email', 'name']);
  if (!policy) throw errors.not_found('ApplicationPolicy');

  if (rulesHasChanges) {
    const policyStatus = await ApplicationPolicyStatus.findOne({ policyId: policy.id });
    if (policyStatus) await policyStatus.deleteOne();
  }

  try {
    await updateInventoryApplicationPolicyStatus(applicationPolicyId);
  } catch (error: any) {
    Logger.error(`Error updating inventory application policy status: ${error}`);
  }

  res.status(200).json({ policy });
});

export const removeApplicationPolicy: RequestHandler = catchAsync(async (req, res) => {
  const userId = req.user?.id;
  const applicationPolicyId = req.params.id;

  const policy = await ApplicationPolicy.findById(applicationPolicyId);
  if (!policy) throw errors.not_found('ApplicationPolicy');

  await updateInventoryApplicationPolicy(applicationPolicyId, {
    deleted: true,
    enabled: false,
    updatedBy: userId,
  });

  await updateInventoryApplicationPolicyCurrentTask(applicationPolicyId, {
    cancelled: new Date(),
    status: CANCELLED,
    statusDetail: 'The inventory application policy is deleted',
  });

  res.status(204).json({});
});

export const switchEnabledApplicationPolicy: RequestHandler = catchAsync(async (req, res) => {
  const userId = req.user?.id;
  const applicationPolicyId = req.params.id;

  const applicationPolicy = await applicationPolicySwitch(applicationPolicyId, userId);

  res.status(200).json(applicationPolicy);
});

export const applicationPolicyFilters: RequestHandler = catchAsync(async (_, res) => {
  const skippedFilter: string[] = [
    'deleted',
    'groupDeleted',
    'nextCheck',
    'lastNotify',
    'nextNotify',
    'protected',
    'createdAt',
    'updatedAt',
    'periodicity',
  ];
  const [filter, fields] = await ApplicationPolicy.createFilter(undefined, skippedFilter);

  res.status(200).json({ filter, fields });
});

export const updateApplicationPolicyStatus: RequestHandler = catchAsync(async (req, res) => {
  const applicationPolicyId = req.params.id;

  const inventoryApplicationPolicyStatus =
    await updateInventoryApplicationPolicyStatus(applicationPolicyId);

  return ServiceResponse.patch(inventoryApplicationPolicyStatus || {}).send(res);
});
