import { Types } from 'mongoose';

import { RportGroup } from '@services/Rport/schemas';
import { QueuesTask } from '@services/Queues/schemas';
import {
  AptFix,
  AptFixDocument,
  AptMitigationPolicy,
  AptMitigationPolicyDocument,
} from '../../schemas';

import { Logger } from '@shared/helpers/classes/logger.class';
import { errors } from '@shared/utils/app-errors';
import { getNextCheck } from '@shared/utils/dates.utils';

import { getHostIdsFromGroup, getHostByShellType } from '@services/Rport/helpers/functions';

import TasksFunctions from '@services/Queues/helpers/functions/task.function';

import { runFixAndSaveResult } from './';

import { TASK_NAMES } from '@services/Queues/helpers/constants/tasks';
import {
  FINISHED,
  IN_PROGRESS,
  PENDING,
  VALIDATING,
} from '@services/Queues/helpers/constants/status';
import { KnownQueuesTaskDocument } from '@root/services/Queues/schemas/task.schema';
import { TaskParams } from '@root/services/Queues/helpers/types/task.types';

// Cronner function for running the scan policies automatically
export const runMitigationPolicies = async () => {
  const policies = await AptMitigationPolicy.find({
    enabled: true,
    deleted: false,
    nextCheck: { $lt: new Date() },
  });

  policies.map(async (policy) => {
    try {
      await initMitigationPolicy(policy);
      Logger.info(`Apt Mitigation Policy "${policy.name}" started successfully and updated`);
    } catch (error) {
      Logger.error(`Apt Mitigation Policy "${policy.name}" failed to start`, error);
    }
  });

  return 'Scan Policies executed successfully';
};

export const initMitigationPolicy = async (policy: AptMitigationPolicyDocument) => {
  if (!policy.group) return;

  const isGroupvalid = await checkPolicyGroup(policy);
  if (!isGroupvalid) throw errors.not_valid('Group');

  const hostIds = await getHostIdsFromGroup(policy.group);

  // Loop through the policy fixes and run them on the connected hosts
  await runMitigationPolicy(policy, hostIds);

  // Update the last and next check
  await updateExecutionTime(policy);
};

export const checkPolicyGroup = async (policy: AptMitigationPolicyDocument) => {
  // Check if the group exists
  const groupExists = await RportGroup.findOne({
    _id: policy.group,
    deleted: false,
    enabled: true,
  });

  // If the group does not exist update policy
  if (!groupExists) {
    await AptMitigationPolicy.updateOne({ _id: policy._id }, { group: undefined });
    return false;
  }

  return true;
};

export const runMitigationPolicy = async (
  policy: AptMitigationPolicyDocument,
  hostIds: string[]
) => {
  // Run all the fixes in parallel
  await Promise.all(
    policy.fixes.map(async (fixId) => {
      // Get the fix from the DB
      const fix = await AptFix.findById(fixId);
      if (!fix) return;

      const { connectedHosts } = await getHostByShellType(fix.shellType, hostIds);

      const batutaJobId = await runFixAndSaveResult(
        fix,
        policy.action,
        connectedHosts,
        policy.createdBy
      );

      // Set Task for disconnected hosts
      await setMitigationTask(policy, fix, connectedHosts, batutaJobId);
    })
  );
};

export const setMitigationTask = async (
  policy: AptMitigationPolicyDocument,
  fix: AptFixDocument,
  affectedHosts: string[],
  jobId: Types.ObjectId
) => {
  // Getting current task
  const currentTask = await QueuesTask.findOne<
    KnownQueuesTaskDocument<TASK_NAMES.RUN_APT_MITIGATION_POLICY>
  >({
    name: TASK_NAMES.RUN_APT_MITIGATION_POLICY,
    params: {
      mitigationPolicyId: policy._id,
      fixId: fix._id,
    } as TaskParams<TASK_NAMES.RUN_APT_MITIGATION_POLICY>,
    status: { $in: [PENDING, IN_PROGRESS, VALIDATING] },
  });

  // Finish the current task if exists
  if (currentTask) {
    await QueuesTask.updateOne<KnownQueuesTaskDocument<TASK_NAMES.RUN_APT_MITIGATION_POLICY>>(
      { _id: currentTask._id },
      {
        status: FINISHED,
        finished: new Date(),
        statusDetail: 'Task completed by a new apt mitigation policy execution',
      }
    );
  }

  // Create task to scan the hosts disconnected
  await TasksFunctions.createTask(TASK_NAMES.RUN_APT_MITIGATION_POLICY, 1, {
    mitigationPolicyId: policy._id,
    fixId: fix._id,
    affectedHosts,
    action: policy.action,
    jobId,
  });
};

export const cancelCurrentPolicyTasks = async (
  policy: AptMitigationPolicyDocument,
  statusDetail?: string
) => {
  const currentTasks = await QueuesTask.find<
    KnownQueuesTaskDocument<TASK_NAMES.RUN_APT_MITIGATION_POLICY>
  >({
    name: TASK_NAMES.RUN_APT_MITIGATION_POLICY,
    params: {
      mitigationPolicyId: policy._id,
    } as TaskParams<TASK_NAMES.RUN_APT_MITIGATION_POLICY>,
    status: { $in: [PENDING, IN_PROGRESS, VALIDATING] },
  });

  await QueuesTask.updateMany<KnownQueuesTaskDocument<TASK_NAMES.RUN_APT_MITIGATION_POLICY>>(
    { _id: { $in: [...currentTasks.map((task) => task._id)] } },
    { status: FINISHED, finished: new Date(), statusDetail }
  );
};

export const updateExecutionTime = async (policy: AptMitigationPolicyDocument) => {
  const nextCheck = getNextCheck(policy.frequency);

  // Update the next check
  await AptMitigationPolicy.updateOne({ _id: policy._id }, { nextCheck, lastCheck: new Date() });
};
