import { Request } from 'express';
import { PipelineStage, Types } from 'mongoose';

import { AptFix, HostFixStatus } from '../../schemas';

import { errors } from '@shared/utils/app-errors';

import { getClientsFromRport } from '@services/Rport/helpers/functions';
import { buildExtraFilters } from '@shared/helpers/classes/schema-utils.class';

import { HOST_FIX_STATES_FIELDS, HOST_FIX_STATES_FILTERS } from '../constants';

// Function to set the verifying status for the hosts that does not has any status on the fix that is been applied
export const checkAndSetVerifyingStatus = async (
  hosts: string[],
  fixId: Types.ObjectId | string
) => {
  // Get the host that has not status on the fix
  const hostWithStatus = await HostFixStatus.find({ host: { $in: hosts }, fix: fixId });

  const hostWithoutStatus = hosts.filter(
    (host) => hostWithStatus.findIndex((h) => h.host === host) === -1
  );

  // Create the host fix status for the new hosts and set the status to verifying
  await HostFixStatus.insertMany(
    hostWithoutStatus.map((host) => ({
      host,
      fix: fixId,
      status: 'verifying',
    }))
  );
};

export const removeVerifyingStatus = async () => {
  await HostFixStatus.deleteMany({ status: 'verifying' });
};

export const hostFixStatesFilters = () => {
  return [HOST_FIX_STATES_FILTERS, HOST_FIX_STATES_FIELDS];
};

export const parseHostFixStatesFilters = async (filterObject: any) => {
  return buildExtraFilters(filterObject);
};

export const getHostFixStatus = async (
  filter: string = '',
  limit: number | undefined = 500,
  offset: number = 0,
  sort: string = ''
) => {
  // Building the query based on the constructed filters
  const [_, sortQuery, extraFilter] = HostFixStatus.parseFilter(
    filter,
    sort,
    HOST_FIX_STATES_FIELDS
  );

  // getting filters
  const extraQuery = await parseHostFixStatesFilters(extraFilter);

  // Base Aggregation
  const pipeline: PipelineStage[] = [
    // Match the host fix states with pending status
    { $match: { deleted: false } },
    // Populate the fix to get the name and importance
    { $lookup: { from: 'aptfixes', localField: 'fix', foreignField: '_id', as: 'fix' } },
    { $unwind: '$fix' },
    // Group by host and count the number of fixes by importance
    {
      $group: {
        _id: '$host',
        high: {
          $sum: {
            $cond: [
              { $and: [{ $eq: ['$fix.importance', 'high'] }, { $eq: ['$status', 'pending'] }] },
              1,
              0,
            ],
          },
        },
        medium: {
          $sum: {
            $cond: [
              { $and: [{ $eq: ['$fix.importance', 'medium'] }, { $eq: ['$status', 'pending'] }] },
              1,
              0,
            ],
          },
        },
        low: {
          $sum: {
            $cond: [
              { $and: [{ $eq: ['$fix.importance', 'low'] }, { $eq: ['$status', 'pending'] }] },
              1,
              0,
            ],
          },
        },
        applied: { $sum: { $cond: [{ $eq: ['$status', 'applied'] }, 1, 0] } },
      },
    },
    // Populate the host to get the name, enabled, and deleted status
    { $lookup: { from: 'rportclients', localField: '_id', foreignField: 'rportId', as: 'host' } },
    { $unwind: '$host' },
    // Skip deleted and disabled hosts
    { $match: { 'host.deleted': false, 'host.enabled': true } },
    // Sort by the number of fixes by importance (high, medium, low)
    { $sort: { high: -1, medium: -1, low: -1 } },
    // Project the data to get the hostname and the number of fixes by importance
    {
      $project: {
        hostname: '$host.name',
        high: 1,
        medium: 1,
        low: 1,
        applied: 1,
      },
    },
  ];

  // Pipeline for counting the total number of results
  const countPipeline: PipelineStage[] = [...pipeline];
  countPipeline.push({ $count: 'total' });

  // Applying filters to the pipeline
  pipeline.push({ $match: extraQuery });

  // Sort Results by the sort query or by hostname
  pipeline.push({ $sort: sortQuery ?? { hostname: 1 } });

  // Skip Results
  pipeline.push({ $skip: offset ? +offset : 0 });

  // Limit Results
  if (limit !== undefined) {
    const parsedLimit = limit === -1 ? Number.MAX_SAFE_INTEGER : Number(limit);
    pipeline.push({ $limit: parsedLimit });
  }

  const [filteredResults, filteredResultsCount] = await Promise.all([
    HostFixStatus.aggregate(pipeline),
    HostFixStatus.aggregate(countPipeline),
  ]);

  return {
    meta: {
      count: filteredResultsCount?.[0]?.total ?? 0,
      resources: filteredResults.length,
      offset: +(offset ?? 0),
    },
    data: filteredResults,
  };
};

export const getHostsByFix = async (
  filter: string = '',
  limit: number = 100,
  offset: number = 0,
  sort: string = '',
  req?: Request | Record<string, any>
) => {
  const fixId = req?.params?.id;
  if (!fixId) throw errors.not_found('Fix ID');

  // Get and validate the fix
  const fix = await AptFix.findById(fixId);
  if (!fix) throw errors.not_found('Fix');

  // Split the filters and find the status
  const filtersSplitted = filter.split('|');

  // Get the status filter
  const statusFilter = filtersSplitted.find((f) => f.split(':')[0] === 'mitigationStatus');
  const status = statusFilter ? statusFilter.split(':')[2] : undefined;

  // Build the query
  const query: any = { fix: fixId };
  if (status) query.status = status;

  // Fetch host fixes
  const hostFixes = await HostFixStatus.find(query).select('host status updatedAt');

  if (hostFixes.length === 0) {
    return {
      meta: {
        total: 0,
        resources: 0,
        offset: +offset,
      },
      data: [],
    };
  }

  // Create a hashmap with the host id and the status
  const hostFixesMap = new Map<string, { status: string; updatedAt: Date }>();
  hostFixes.forEach((hostFix) =>
    hostFixesMap.set(hostFix.host, { status: hostFix.status, updatedAt: hostFix.updatedAt })
  );

  const hostIds = Array.from(hostFixesMap.keys());

  // Update the filter with host IDs
  const hostIdFilters = hostIds.map((id) => `rportId:is:${id}`).join('|');
  const newFilter = filter.length > 0 ? `${filter}|${hostIdFilters}` : hostIdFilters;

  // Fetch clients from Rport
  const responseData = await getClientsFromRport(newFilter, limit, offset);

  // Format the response
  const hosts = responseData.data.map((host: any) => {
    const mitigationStatus = hostFixesMap.get(host.id)?.status ?? undefined;
    const lastCheck = hostFixesMap.get(host.id)?.updatedAt ?? undefined;

    return {
      id: host.id,
      name: host.name,
      connection_state: host.connection_state,
      disconnected_at: host.disconnected_at,
      tags: host.tags,
      address: host.address,
      os: host.os,
      osKernel: host.os_kernel,
      osVersion: host.os_version,
      osFamily: host.os_family,
      enabled: host.enabled,
      mitigationStatus,
      lastCheck,
    };
  });

  return {
    meta: responseData.meta,
    data: hosts,
  };
};
