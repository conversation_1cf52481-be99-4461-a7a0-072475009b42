import _ from 'lodash';
import { Types } from 'mongoose';

import { RportClientDocument, RportJob } from '@services/Rport/schemas';
import { AptFix, AptFixDocument, AptFixTag, HostFixStatus } from '@services/APT/schemas';

import { ShellTypesByKernel } from '@shared/models';
import { ArrayRelative, FilterType } from '@shared/models';
import { AptFixAction } from '../models/apt-fix.model';

import { RportClient, RportMultipleJob } from '@services/Rport/helpers/types';

import { errors } from '@shared/utils/app-errors';
import { getOSFromShellType } from '@shared/utils/shellType';
import { delay } from '@shared/utils/delay';
import { Logger } from '@shared/helpers/classes/logger.class';

import TasksFunctions from '@services/Queues/helpers/functions/task.function';
import {
  executeCommandOnClientsOrGroups,
  getMultiClientJobDetail,
} from '@services/Rport/helpers/connections/rport';
import prescripts from '@services/Rport/helpers/prescripts';
import { runFunctionInBatchWithDelay } from '@services/Rport/helpers/utils/rport-clients.utils';
import { getHostByShellType } from '@services/Rport/helpers/functions';

import { checkAndSetVerifyingStatus } from './host-fix-status.functions';

import { TASK_NAMES } from '@services/Queues/helpers/constants/tasks';
import { APT_RUN_FIX_COMMAND_TIMEOUT } from '../constants/fixes';

// Fix Extra Filters
export const getFixFilters = async () => {
  const tags = await AptFixTag.find({}).select('_id name');
  const tagsOptions = tags.map((option: any) => ({ key: option.name, value: option._id })) || [];
  return {
    tags: {
      type: FilterType.SELECT,
      relatives: Object.values(ArrayRelative),
      options: tagsOptions,
    },
  };
};

export const getPendingFixesForHost = async (
  host: RportClientDocument,
  status: string = 'pending',
  fixes: (string | Types.ObjectId)[] = []
) => {
  // Get ShellTypes from osKernel
  const shellTypes = ShellTypesByKernel[host.osKernel];
  if (!shellTypes) throw errors.not_valid('Host');

  // Validate the fixes in DB
  const validFixes = await AptFix.find({
    ...(fixes.length > 0 ? { _id: { $in: fixes } } : {}),
    enabled: true,
    deleted: false,
    shellType: { $in: shellTypes },
  });

  // Check which fixes are pending
  const hostFixes = await HostFixStatus.find({
    host: host.rportId,
    fix: { $in: validFixes.map((fix) => fix._id.toString()) },
    status,
  });

  return hostFixes.map((hostFix) => validFixes.find((fix) => _.isEqual(fix._id, hostFix.fix)));
};

export const getPendingFixesIdsForHost = async (
  validHost: any,
  status: string,
  fixes: (string | Types.ObjectId)[]
) => {
  const pendingFixes = await getPendingFixesForHost(validHost, status, fixes);
  return pendingFixes.map((fix) => fix!._id);
};

export const runFixAction = async (
  fixId: string | Types.ObjectId,
  hosts: string[],
  action: AptFixAction,
  useQueue: boolean = false,
  author: string
) => {
  try {
    // Get & Validate the fix
    const fix = await AptFix.findById(fixId);
    if (!fix) throw errors.not_found('Fix');

    // Get the online hosts
    const { connectedHosts, disconnectedHosts } = await getHostByShellType(fix.shellType, hosts);

    // Run the fix and save the result
    const jobId = await runFixAndSaveResult(fix, action, connectedHosts, author);

    // Create task if queue is enabled
    if (useQueue) {
      // Create task for offline hosts
      await createHostsFixTask(
        fix._id,
        action,
        disconnectedHosts,
        [...connectedHosts, ...disconnectedHosts],
        jobId
      );
    }

    return jobId;
  } catch (error) {
    throw error;
  }
};
export const runFixAndSaveResult = async (
  fix: AptFixDocument,
  action: AptFixAction,
  hostIds: string[],
  author: string
) => {
  // Run the fix on the connected hosts
  const jobIds = await initFixCommand(fix, action, hostIds);

  // Save the fix results
  const batutaJobId = await saveFixResults(
    jobIds,
    hostIds,
    author,
    `${fix.name}-${action}`,
    fix.shellType
  );

  return batutaJobId;
};

export const runFixAndUpdateResult = async (
  fix: AptFixDocument,
  action: AptFixAction,
  hostIds: string[],
  batutaJobId: Types.ObjectId | string
) => {
  let jobIds: string[] = [];
  try {
    // Run the fix on the connected hosts
    jobIds = await initFixCommand(fix, action, hostIds);
  } catch (error) {
    // If the command fails, remove the host fix status
    await HostFixStatus.deleteMany({ host: { $in: hostIds }, fix: fix._id, status: 'verifying' });
    throw errors.job_could_not_run();
  }

  // Update the fix results
  const updatedBatutaJobId = await updateFixResults(batutaJobId, jobIds, hostIds);

  return updatedBatutaJobId;
};

const createHostsFixTask = async (
  fixId: Types.ObjectId,
  action: AptFixAction,
  affectedHosts: string[],
  selectedHosts: string[],
  job: Types.ObjectId
) => {
  // Create task to scan the hosts disconnected
  await TasksFunctions.createTask(TASK_NAMES.RUN_APT_MITIGATION_ON_HOSTS, 1, {
    fixId,
    action,
    affectedHosts,
    selectedHosts,
    job,
  });
};

const initFixCommand = async (fix: AptFixDocument, action: AptFixAction, hostIds: string[]) => {
  if (hostIds.length === 0) {
    return [];
  }

  await checkAndSetVerifyingStatus(hostIds, fix._id);

  // Send the command to the hosts
  const result = await executeFix(fix, action, hostIds);

  if (result.length === 0) {
    // If the command fails, remove the host fix status
    await HostFixStatus.deleteMany({ host: { $in: hostIds }, fix: fix._id, status: 'verifying' });
    throw errors.job_could_not_run();
  }

  return result;
};

// Function to handle fix execution and job creation
const executeFix = async (fix: AptFixDocument, action: AptFixAction, hosts: string[]) => {
  // Run the fix on the connected hosts
  const jobIds = await runFunctionInBatchWithDelay<string | undefined>(
    hosts,
    (chunk) => runFixCommand(chunk, fix, action),
    500,
    1000
  );

  return jobIds.filter((jobId) => jobId !== undefined) as string[];
};

const saveFixResults = async (
  jobIds: string[],
  selectedHosts: string[],
  author: string,
  commandDescription: string,
  osKernel: string
) => {
  // Create the job in the DB
  const job = await RportJob.create({
    jobId: null,
    jobIds: jobIds.flatMap((ids) => ids) || [],
    author: author,
    command: `APT_MITIGATION_POLICY_FIX_${commandDescription}`,
    timeout: 300,
    os: osKernel,
    clients: selectedHosts || [],
    clientsInQueue: [],
    useQueue: true,
  });

  return job._id;
};

const updateFixResults = async (
  batutaJobId: Types.ObjectId | string,
  jobIds: string[],
  selectedHosts: string[]
) => {
  // Update the job
  const job = await RportJob.findByIdAndUpdate(
    batutaJobId,
    {
      $push: {
        jobIds: { $each: jobIds },
        clients: { $each: selectedHosts },
      },
      $pull: {
        clientsInQueue: { $in: selectedHosts },
      },
    },
    { new: true }
  );

  return job ? job._id : undefined;
};

const runFixCommand = async (hostIds: string[], fix: AptFixDocument, action: AptFixAction) => {
  // Get Os Kernel from fix
  const osKernel = getOSFromShellType(fix.shellType);
  if (!osKernel) throw errors.not_valid('Shell Type');

  let fixCommand = fix.scripts[action];
  if (prescripts[osKernel]) {
    fixCommand = prescripts[osKernel].addPrescripts(fixCommand);
  }

  let executionResult: string | undefined = undefined;

  const cwd = osKernel === 'windows' ? 'C:\\Windows\\Temp' : '/tmp';

  try {
    const result = await executeCommandOnClientsOrGroups(
      hostIds,
      [],
      fixCommand,
      APT_RUN_FIX_COMMAND_TIMEOUT,
      fix.shellType,
      cwd
    );

    executionResult = result.jid;
  } catch (error) {
    Logger.error(`Error executing fix command: ${error}`);
    return undefined;
  }

  try {
    // Run check for the execution result
    checkFixResponse(action, hostIds, executionResult, fix._id);
  } catch (error) {
    Logger.error(`Error checking fix response: ${error}`);
  }

  return executionResult;
};

const checkFixResponse = async (
  action: AptFixAction,
  hostIds: string[],
  jobId: string,
  fixId: Types.ObjectId
) => {
  // Create a variable to store the current time
  const startedAt = new Date();

  // Get response checker function
  const checker = getResponseChecker(action);

  // Host to check
  let hostToCheck = [...hostIds];

  // Create a while loop that will run until 5 minutes have passed
  while (new Date().getTime() - startedAt.getTime() < 300000) {
    // Get the job status from rport
    const jobResult = await getMultiClientJobDetail(jobId);

    // Check in parallel for the host in the jobs
    const hostResults = await Promise.all(
      hostToCheck.map((hostId) => checker(fixId, jobResult, hostId))
    );

    // Remove the hosts that are already checked
    hostToCheck = hostToCheck.filter((_, index) => hostResults[index] === 'pending');

    // If all hostResults are true, break the loop
    if (hostResults.every((result) => result === 'success' || result === 'failed')) {
      break;
    }

    // Wait for 5 seconds
    await delay(5000);
  }
};

const getResponseChecker = (
  action: AptFixAction
): ((
  fixId: Types.ObjectId,
  job: RportMultipleJob,
  hostId: string
) => Promise<'pending' | 'success' | 'failed'>) => {
  switch (action) {
    case 'test':
      return checkTestJobStatus;
    case 'fix':
      return (...parameters) => checkFixApplyRollback(...parameters, 'applied');
    case 'rollback':
      return (...parameters) => checkFixApplyRollback(...parameters, 'pending');
    default:
      throw errors.not_valid('Fix Action');
  }
};

const checkTestJobStatus = async (
  fixId: Types.ObjectId,
  job: RportMultipleJob,
  hostId: string
): Promise<'pending' | 'success' | 'failed'> => {
  // Check if the job is failed or pending
  const jobHost = await getSuccessfulJob(fixId, job, hostId);
  if (jobHost === 'failed' || jobHost === 'pending') {
    return jobHost as 'failed' | 'pending';
  }

  // Check the output of the job
  const testOutput = jobHost.result.stdout;

  // Check if the output is true or false
  if (testOutput.includes('True')) {
    await HostFixStatus.updateOne({ host: hostId, fix: fixId }, { status: 'applied' });
  }

  // Check if the output is false
  if (testOutput.includes('False')) {
    await HostFixStatus.updateOne({ host: hostId, fix: fixId }, { status: 'pending' });
  }

  // If the output is not true or false, remove the host fix status
  if (!testOutput.includes('True') && !testOutput.includes('False')) {
    await HostFixStatus.deleteOne({ host: hostId, fix: fixId, status: 'verifying' });
    return 'failed';
  }

  return 'success';
};

const checkFixApplyRollback = async (
  fixId: Types.ObjectId,
  job: RportMultipleJob,
  hostId: string,
  newStatus: 'pending' | 'applied'
): Promise<'pending' | 'success' | 'failed'> => {
  // Check if the job is failed or pending
  const jobHost = await getSuccessfulJob(fixId, job, hostId);
  if (jobHost === 'failed' || jobHost === 'pending') {
    return jobHost as 'failed' | 'pending';
  }

  // Update the host fix status to new status depeding if is rollback or apply
  await HostFixStatus.updateOne({ host: hostId, fix: fixId }, { status: newStatus });

  return 'success';
};

const getSuccessfulJob = async (fixId: Types.ObjectId, job: RportMultipleJob, hostId: string) => {
  // Search for the host in the job
  const jobHost = job.jobs.find((job) => job.client_id === hostId);

  if (!jobHost) {
    // If not found is still trying to reach the host
    return 'pending';
  }

  if (jobHost.status !== 'successful' && jobHost.status !== 'running') {
    // Remove the host fix status
    await HostFixStatus.deleteOne({ host: hostId, fix: fixId, status: 'verifying' });
    return 'failed';
  }

  if (jobHost.status === 'running') {
    return 'pending';
  }

  return jobHost;
};

// ----------------- Apply All Fixes or Rollbacks to Host  -----------------
const applyActionsToHost = async (
  host: RportClient,
  fixes: Types.ObjectId[] | string[],
  action: 'fix' | 'rollback',
  useQueue: boolean,
  author: string
) => {
  // Check if the host is online
  if (host.connection_state === 'connected') {
    // Run the actions
    await Promise.all(
      fixes.map((fix) => {
        runFixAction(fix, [host.id], action, false, author); // Sending false to avoid creating an individual task
      })
    );
    return `The process to ${action} the fixes has started`;
  }
  if (useQueue) {
    // Create a task to run the actions
    await createHostFixesTask(host.id, fixes, action, author);
    return `The process to ${action} the fixes has been scheduled for the host`;
  }

  return `The host is not connected, the process to ${action} the fixes could not be started`;
};

export const applyAllFixesToHost = async (
  host: RportClient,
  fixes: Types.ObjectId[] | string[],
  useQueue: boolean,
  author: string
) => {
  return applyActionsToHost(host, fixes, 'fix', useQueue, author);
};

export const applyAllRollbacksToHost = async (
  host: RportClient,
  fixes: Types.ObjectId[] | string[],
  useQueue: boolean,
  author: string
) => {
  return applyActionsToHost(host, fixes, 'rollback', useQueue, author);
};

const createHostFixesTask = async (
  hostId: string,
  fixes: Types.ObjectId[] | string[],
  action: AptFixAction,
  author: string
) => {
  // Create task to run fixes on the host
  await TasksFunctions.createTask(TASK_NAMES.RUN_APT_MITIGATIONS_ON_HOST, 1, {
    hostId,
    fixes,
    action,
    author,
  });
};
