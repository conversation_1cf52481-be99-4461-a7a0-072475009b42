import { Types } from 'mongoose';

import { QueuesTask } from '@services/Queues/schemas';

import { Logger } from '@root/shared/helpers/classes/logger.class';

import TasksFunctions from './task.function';
import QueuesFunctions from './queue.function';

import { createWorkerForTask } from '../worker';

import {
  VALIDATING,
  PENDING,
  IN_PROGRESS,
  FINISHED,
  EXPIRED,
  CANCELLED,
} from '../constants/status';
import {
  DAYS_TO_EXPIRED,
  DAYS_TO_REMOVE_AFTER_FINISHED,
  NO_EXPIRED_TASKS,
  NoExpiredTaskName,
  TASK_NAMES,
} from '../constants/tasks';
import { ValidationUpdateReturn } from '../worker/abstractWorker';
import { TaskParams, ValidTaskName } from '../types/task.types';
import { KnownQueuesTaskDocument } from '../../schemas/task.schema';

export const expireTasks = async () => {
  // Get tasks with more than 45 days after creation
  // and expire this (update expired date, status, and status details)
  const now = new Date();
  const expiredDate = new Date(now);
  expiredDate.setDate(expiredDate.getDate() - DAYS_TO_EXPIRED);

  const queueTasks = await QueuesTask.updateMany<
    KnownQueuesTaskDocument<Exclude<ValidTaskName, NoExpiredTaskName>>
  >(
    {
      status: PENDING,
      name: { $nin: NO_EXPIRED_TASKS },
      createdAt: { $lt: expiredDate },
    },
    {
      $set: {
        status: EXPIRED,
        statusDetail: 'This task expired by being more than 45 days in PENDING status.',
        expired: now,
      },
    }
  );

  return queueTasks.modifiedCount;
};

export const clearFinishedTasks = async () => {
  // Get tasks with more than 90 days after finished status
  // and remove this
  const expiredDate = new Date();
  expiredDate.setDate(expiredDate.getDate() - DAYS_TO_REMOVE_AFTER_FINISHED);

  const queueTasks = await QueuesTask.deleteMany({
    status: { $in: [FINISHED, EXPIRED, CANCELLED] },
    updatedAt: { $lt: expiredDate },
  });

  return queueTasks.deletedCount;
};

export const runPendingTasks = async (): Promise<number> => {
  // El cron revisa la colección de mongo de tasks en búsqueda de tareas con status in progress y las agrupa por queue
  // Revisa según el maximumConcurrency si tiene disponibilidad para ejecutar tareas en esa queue
  // Si no tiene, no ejecuta nada -> termina aca
  // Si tiene espacio continua en el siguiente paso
  const queuesWithAvailability = await QueuesFunctions.getAllQueuesWithInProgressTasks();

  if (queuesWithAvailability.length == 0) return 0;

  // El cron revisa la colección de mongo de tasks en búsqueda de tareas con status pending y las agrupa por queue, para pedir solo la información necesaria la query a realizar va a realizarse de la siguiente manera:
  // Buscar en la queue con disponibilidad las tasks que puede realizar
  // Buscar en la colección tasks filtrando por taskName y status pending
  // Filtrar por retryAfter nulo o menor a la fecha-hora actual
  // Ordenando por prioridad descendente
  const queuesWithPendingTasks =
    await QueuesFunctions.getAllQueuesWithPendingTasks(queuesWithAvailability);

  // Teniendo ya la lista de tareas a realizar, se busca en la constante de queues-class y hace el siguiente proceso
  const tasksWorked = [];

  await Promise.all(
    queuesWithPendingTasks.map(async (queue) => {
      await QueuesFunctions.updateQueue(queue._id, { running: true });

      const inProgressTasks = queuesWithAvailability.find((q) => q.name === queue.name)?.tasks;
      if (inProgressTasks === undefined) return null;
      let availability = queue.maximumConcurrency - inProgressTasks.length;
      const pendingTasks = queue.tasks;

      for (const task of pendingTasks) {
        // Verifica si la task aun no ha sido tomada por otro worker, cancelada o finalizada por otro proceso
        const currentTask = await QueuesTask.findById(task._id);
        if (!currentTask || currentTask.status !== PENDING) continue;

        // Genera una instancia de esa clase con la tarea a realizar
        const worker = createWorkerForTask(task);

        // Ejecuta validate
        const date = new Date();
        await TasksFunctions.updateTask(task._id, { status: VALIDATING, validating: date });
        const isValid = await worker.validate();

        // Si el validate es false, sigue buscando tareas
        let newStateAfterValid: ValidationUpdateReturn = await worker.validationUpdates(isValid);
        if (isValid) {
          newStateAfterValid = { ...newStateAfterValid, status: IN_PROGRESS };
          if (newStateAfterValid.status == IN_PROGRESS) newStateAfterValid.inProgress = date;
        } else {
          newStateAfterValid = { ...newStateAfterValid, status: PENDING };
          if (newStateAfterValid.status == PENDING) newStateAfterValid.pending = date;
        }
        await TasksFunctions.updateTask(task._id, newStateAfterValid);
        if (!isValid) continue;

        // Si el validate es true, ejecuta el método run como promesa y actualiza la tarea en la base con status: in progress
        // Cuando el método run termina ya sea por then o catch llegaría el nuevo valor que tendría la task que tener en la base y se actualiza la misma
        tasksWorked.push(task);
        worker
          .run()
          .then((task) => TasksFunctions.updateTask(task._id as string | Types.ObjectId, task))
          .catch((task) => TasksFunctions.updateTask(task._id as string | Types.ObjectId, task));

        // Validar disponibilidad de nuevas tareas
        availability = availability - 1;
        if (availability === 0) break;
      }

      await QueuesFunctions.updateQueue(queue._id, { running: false });
    })
  );

  return tasksWorked.length;
};

export const removeFinishedCheckStatusTasks = async (): Promise<number> => {
  try {
    return await TasksFunctions.removeTasksByFilter({
      params: {
        rportJobId: { $exists: false },
        commandName: 'checkStatus',
      } as Partial<Record<keyof TaskParams<TASK_NAMES.RUN_COMMAND>, any>>,
      status: FINISHED,
    });
  } catch (error) {
    Logger.error(`Unable to delete finished checkStatus tasks: ${error}`);
    return 0;
  }
};
