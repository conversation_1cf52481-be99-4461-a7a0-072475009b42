import { Types } from 'mongoose';
import { QueuesQueue, QueuesTask } from '@services/Queues/schemas';
import { IN_PROGRESS, PENDING, TaskStatus } from '../constants/status';
import { Queue } from '../types';
import { TASK_NAMES } from '../constants/tasks';
import { QUEUE_WORKER_CLASSES } from '../worker';
import { TaskParams } from '../types/task.types';

export const composeQuery = (
  filters: { [key: string]: string | number } = {},
  include?: string[],
  exclude?: string[]
) => {
  // building query for tasks filtering
  const query: { [key: string]: any } = {
    $and: [
      include?.length ? { tasks: { $in: include } } : {},
      exclude?.length ? { tasks: { $nin: exclude } } : {},
    ],
  };

  // Filtering name
  if ('name' in filters && filters.name) {
    query['name'] = { $regex: filters.name, $options: 'i' };
  }

  if ('maximumConcurrency' in filters && filters.maximumConcurrency) {
    query['maximumConcurrency'] = filters.maximumConcurrency;
  }

  return query;
};

export const getAllQueues = async (
  filter?: { [key: string]: string | number },
  include?: string[],
  exclude?: string[]
) => {
  const query = composeQuery(filter, include, exclude);

  const queues = await QueuesQueue.find(query);

  return queues;
};

export const getOneQueue = async (queueId: string | Types.ObjectId) => {
  const queue = await QueuesQueue.findOne({
    _id: queueId,
  });

  return queue;
};

export const createQueue = async (
  name: string,
  maximumConcurrency: number = 1,
  tasks: string[] = []
) => {
  const queue = await QueuesQueue.create({ name, maximumConcurrency, tasks });

  return queue;
};

export const updateQueue = async (
  queueId: string | Types.ObjectId,
  queueData: { [key: string]: any } = {}
) => {
  const queue = await QueuesQueue.findByIdAndUpdate(queueId, queueData, {
    runValidators: true,
  });

  if (queue == null) return null;

  const updatedQueue = await QueuesQueue.findById(queue._id);

  return updatedQueue;
};

export const deleteQueue = async (queueId: string | Types.ObjectId) => {
  const queue = await QueuesQueue.findByIdAndDelete(queueId);
  return queue;
};

export const getAllQueuesWithInProgressTasks = async () => {
  return await QueuesQueue.aggregate<{
    _id: Types.ObjectId | string;
    id: string;
    name: keyof typeof QUEUE_WORKER_CLASSES;
    tasks: {
      name: (typeof TASK_NAMES)[keyof typeof TASK_NAMES];
      _id: Types.ObjectId | string;
    }[];
  }>([
    { $match: { running: false, enabled: true } },
    {
      $lookup: {
        from: QueuesTask.collection.name,
        localField: 'tasks',
        foreignField: 'name',
        as: 'tasks',
        pipeline: [
          { $match: { $expr: { $eq: ['$status', IN_PROGRESS] } } },
          { $limit: 10 },
          { $project: { name: 1 } },
        ],
      },
    },
    // Add a field to calculate the length of the tasks array
    {
      $addFields: {
        tasksCount: { $size: '$tasks' },
      },
    },
    // Match documents where tasksCount is less than maximumConcurrency
    {
      $match: {
        $expr: { $lt: ['$tasksCount', '$maximumConcurrency'] },
      },
    },
    // Only return needed fields
    {
      $project: {
        name: 1,
        tasks: 1,
      },
    },
  ]);
};

export const getAllQueuesWithPendingTasks = async (queuesWithAvailability: Queue[]) => {
  return await QueuesQueue.aggregate<{
    _id: string | Types.ObjectId;
    name: keyof typeof QUEUE_WORKER_CLASSES;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
    enabled: boolean;
    maximumConcurrency: number;
    protected: boolean;
    running: boolean;
    tasks: {
      _id: string | Types.ObjectId;
      deleted: boolean;
      enabled: boolean;
      protected: boolean;
      author: string;
      name: (typeof TASK_NAMES)[keyof typeof TASK_NAMES];
      priority: number;
      retries: number;
      status: TaskStatus;
      statusDetail: string;
      params: TaskParams<(typeof TASK_NAMES)[keyof typeof TASK_NAMES]>;
      pending: Date;
      createdAt: Date;
      updatedAt: Date;
      validating: Date;
      finished: Date;
    }[];
  }>([
    {
      $match: {
        running: false,
        enabled: true,
        name: { $in: queuesWithAvailability.map((q) => q.name) },
      },
    },
    {
      $lookup: {
        from: QueuesTask.collection.name,
        localField: 'tasks',
        foreignField: 'name',
        as: 'tasks',
        pipeline: [
          {
            $match: {
              status: PENDING,
              $or: [
                { retryAfter: null },
                { retryAfter: { $exists: false } },
                { retryAfter: { $lte: new Date() } },
              ],
            },
          },
          { $sort: { priority: -1 } },
          { $limit: 20 },
        ],
      },
    },
  ]);
};

export const resetQueues = async () => {
  const queues = await QueuesQueue.updateMany(
    {},
    { running: false },
    {
      runValidators: true,
    }
  );

  return queues;
};

const QueuesFunctions = {
  createQueue,
  deleteQueue,
  getAllQueues,
  getAllQueuesWithInProgressTasks,
  getAllQueuesWithPendingTasks,
  getOneQueue,
  resetQueues,
  updateQueue,
};
export default QueuesFunctions;
