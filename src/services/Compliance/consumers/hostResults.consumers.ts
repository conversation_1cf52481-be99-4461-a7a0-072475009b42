import { ResultsQueueBatch } from '../helpers/types';
import { ControlResultHost, PolicyHostLastResult, PolicyRun, PolicyRunHost } from '../schemas';
import { RawControlResultHostModel } from '../helpers/models';

import { Logger } from '@root/shared/helpers/classes/logger.class';
import { RportClient } from '@root/services/Rport/schemas';
import { BatchAccumulator } from '@shared/helpers/classes/batch-accumulator.class';
import { BatchMessage } from '@shared/types';
import { SOAR_ID } from '@root/shared/constants/env';

// Create an instance of the accumulator
const batchAccumulator = new BatchAccumulator();

export const processPolicyRunResults = async (data: ResultsQueueBatch) => {
  try {
    const { policyRunId, hostId, ranAt, outputBatch, status } = data;

    if (!policyRunId || !hostId || !outputBatch) {
      Logger.error('Invalid data received for saving policy run results');
      return;
    }

    const batchMessage = data as BatchMessage<RawControlResultHostModel>;
    const combinedOutput = batchAccumulator.addMessage(
      batchMessage,
      `${SOAR_ID}-${hostId}-${policyRunId}`
    );

    if (combinedOutput) {
      Logger.info(
        `Processing policy run host results for Policy Run with ID: ${policyRunId} and host with ID: ${hostId}`
      );

      const policyRun = await PolicyRun.findById(policyRunId);
      const host = await RportClient.findOne({ rportId: hostId, deleted: false });

      if (policyRun === null)
        Logger.error(
          `Unable to process policy run results. Policy Run with ID ${policyRunId} not found.`
        );
      if (host === null)
        Logger.error(`Unable to process policy run results. Host with ID ${hostId} not found.`);

      const result = await PolicyRunHost.findOneAndUpdate(
        {
          policy_run: policyRunId,
          host,
          status: 'PENDING',
        },
        {
          ran_at: ranAt,
          status,
        },
        {
          runValidators: true,
          new: true,
        }
      );

      if (result !== null) {
        await Promise.all([
          ControlResultHost.insertMany(
            combinedOutput.map((control: RawControlResultHostModel) => ({
              policyRunHost: result,
              policyRun: policyRunId,
              policy: result.policy,
              controlId: control.ID,
              name: control.Name,
              severity: control.Severity,
              result: control.Result,
              recommended: control.Recommended,
              testResult: control.TestResult,
              severityFinding: control.SeverityFinding,
            }))
          ),
          PolicyHostLastResult.findOneAndUpdate(
            {
              policy: result.policy,
              host,
            },
            {
              policy: result.policy,
              host,
              policyRun: policyRunId,
              policyRunHost: result,
              ran_at: ranAt,
              status,
              updatedAt: result.updatedAt,
              totalControls: combinedOutput.length,
              passedControls: combinedOutput.filter((control) => control.TestResult === 'Passed')
                .length,
              successPercentage:
                combinedOutput.length > 0 &&
                Number.isFinite(
                  combinedOutput.filter((control) => control.TestResult === 'Passed').length
                )
                  ? parseFloat(
                      (
                        (combinedOutput.filter((control) => control.TestResult === 'Passed')
                          .length /
                          combinedOutput.length) *
                        100
                      ).toFixed(2)
                    )
                  : 0,
              $setOnInsert: {
                createdAt: new Date(),
              },
            },
            {
              upsert: true,
            }
          ),
        ]);

        Logger.info(
          `Successfully saved results for Policy Run with ID: ${policyRunId} and host with ID: ${hostId}`
        );
      } else {
        Logger.error(
          `PolicyRunHost in pending state not found: Policy Run ID: ${policyRunId} and Host ID: ${hostId}`
        );
      }
    }
  } catch (error) {
    Logger.error('Error saving policy run results:', error);
  }
};
