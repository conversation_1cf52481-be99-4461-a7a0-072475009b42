import { Document, Schema, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import {
  PolicyModel,
  PolicyLifeCycleModel,
  PolicyScheduleModel,
  PolicyOperatingModeModel,
} from '../helpers/models';

import { MAX_ENTITY_DESCRIPTION, MAX_ENTITY_NAME } from '@shared/constants/max-entities';

const LifeCycleSchema = new Schema<PolicyLifeCycleModel>({
  start_date: Date,
  end_date: Date,
});

const ScheduleSchema = new Schema<PolicyScheduleModel>({
  month: {
    type: [Number], // cero-based months: 0-11
  },
  week_day: {
    type: [Number], // cero-based week days: 0-6 -> 0: Sunday, 1: Monday, ...
  },
  runtime_window: {
    start_time: String, // Time field
    end_time: String, // Time field
  },
});

const OperatingModeSchema = new Schema<PolicyOperatingModeModel>({
  update_results: {
    type: String,
    enum: ['ONE_TIME', 'PERIODIC'],
    required: true,
  },
  results_expiration: {
    type: Number,
    required: false,
  },
});

const policySchema = schemaFactory({
  name: { type: String, required: true, unique: true, maxlength: MAX_ENTITY_NAME },
  description: { type: String, required: false, maxlength: MAX_ENTITY_DESCRIPTION },
  playbook: {
    type: Types.ObjectId,
    ref: 'Playbook',
    required: true,
  },
  group: {
    type: Types.ObjectId,
    ref: 'RportGroup',
    required: true,
  },
  life_cycle: { type: LifeCycleSchema, required: true },
  schedule: { type: ScheduleSchema, required: true },
  operating_mode: { type: OperatingModeSchema, required: true },
  execution_mode: {
    type: String,
    enum: ['CRON', 'MANUAL'],
    default: 'CRON',
    required: true,
  },
});

policySchema.index({ playbook: 1 });
policySchema.index({ group: 1 });
policySchema.index({ execution_mode: 1 });

export type PolicyDocument = PolicyModel & Document & { _id: Types.ObjectId };
export const Policy = modelFactory<PolicyModel>('Policy', policySchema);
