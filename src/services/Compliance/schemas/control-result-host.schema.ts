import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@root/shared/utils/model-factory';

import { ControlResultHostModel } from '../helpers/models';

const controlResultHostSchema = schemaFactory(
  {
    policyRunHost: {
      type: Types.ObjectId,
      ref: 'PolicyRunHost',
      required: true,
    },
    policyRun: {
      type: Types.ObjectId,
      ref: 'PolicyRun',
      required: true,
    },
    policy: {
      type: Types.ObjectId,
      ref: 'Policy',
      required: true,
    },
    controlId: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    severity: {
      type: String,
    },
    result: {
      type: String,
    },
    recommended: {
      type: String,
    },
    testResult: {
      type: String,
    },
    severityFinding: {
      type: String,
    },
  },
  {
    timestamps: false,
  },
  false
);

controlResultHostSchema.index({ policyRunHost: 1 });
controlResultHostSchema.index({ policyRun: 1 });
controlResultHostSchema.index({ policy: 1 });
controlResultHostSchema.index({ testResult: 1 });
controlResultHostSchema.index({ controlId: 1 });
controlResultHostSchema.index({ severity: 1 });
controlResultHostSchema.index({ policyRunHost: 1, testResult: 1 });
controlResultHostSchema.index({ policy: 1, testResult: 1 });
controlResultHostSchema.index({ controlId: 1, testResult: 1 });
controlResultHostSchema.index({ policyRunHost: 1, testResult: 1, severity: 1 });
controlResultHostSchema.index({ policyRunHost: 1, controlId: 1, severity: 1 });

export type ControlResultHostDocument =
  | (ControlResultHostModel & Document & { _id: Types.ObjectId })
  | null;
export const ControlResultHost = modelFactory<ControlResultHostModel>(
  'ControlResultHost',
  controlResultHostSchema
);
