import { Request, RequestHandler } from 'express';

import { Policy, PolicyRunHost } from '../schemas';
import { RportClient } from '@services/Rport/schemas';

import { GetAllQuery } from '@shared/types';

import { errors } from '@shared/utils/app-errors';
import catchAsync from '@shared/utils/catch-async';

import { exportFileOf } from '@services/Export/controllers/';

import { getHostIdsFromGroup } from '@services/Rport/helpers/functions';

import { getNonComplianceHostByControl } from '../helpers/queries';
import { getControlFilters, _getAllControls } from '../helpers/functions';
import { CONTROL_FILTERS } from '../helpers/constants';

export const controlFilters: RequestHandler = catchAsync(async (_, res) => {
  const extraFilters = getControlFilters();
  const skippedFilter = [
    'policy',
    'policy_run',
    'host',
    'ran_at',
    'status',
    'deleted',
    'enabled',
    'createdAt',
    'updatedAt',
    'protected',
  ];
  const [filter, fields] = await PolicyRunHost.createFilter(
    extraFilters,
    skippedFilter,
    CONTROL_FILTERS
  );
  res.status(200).json({ filter, fields });
});

export const getAllControls: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const responseData = await _getAllControls(filter, limit, offset, sort, req);

  return res.status(200).json(responseData);
});

export const exportAllControls: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(req, res, _getAllControls, ({ data }) => data);
});

const _getHostByControl = async (
  _filter: any,
  _limit?: number,
  _offset?: number,
  _sort?: string,
  req?: Request | Record<string, any>
) => {
  const policyId = req?.params?.policyId;
  const controlId = req?.params?.id;

  const policy = await Policy.findById(policyId);
  if (policy === null) throw errors.not_found('Policy');

  // Getting rport id group members
  const rportHostIds = await getHostIdsFromGroup(policy.group);
  // Getting mongo id group members
  const targetedHostIds = await RportClient.find({
    rportId: { $in: rportHostIds },
    deleted: false,
  }).distinct('_id');

  const results = await getNonComplianceHostByControl(
    policy._id,
    targetedHostIds,
    controlId as string
  );

  return results;
};

export const getHostByControl: RequestHandler = catchAsync(async (req, res) => {
  const { filter, limit, offset, sort } = req.query as GetAllQuery;

  const responseData = await _getHostByControl(filter, limit, offset, sort, req);

  return res.status(200).json(responseData);
});

export const exportHostByControl: RequestHandler = catchAsync(async (req, res) => {
  return await exportFileOf(
    req,
    res,
    _getHostByControl,
    ({ nonfulfilled_hosts }) => nonfulfilled_hosts
  );
});
