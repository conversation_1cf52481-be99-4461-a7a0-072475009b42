import { PipelineStage } from 'mongoose';

import { ScannedHost, ScannedHostDailyMetrics } from '../../schemas';

import { ScannedHostMetricsTimeline } from '../models';

import { Logger } from '@shared/helpers/classes/logger.class';

import { calculateScannedHostMetrics } from './scannedHost.functions';

export const storeScannedHostDailyMetrics = async (): Promise<string> => {
  try {
    const currentTime = new Date();

    // Validate if the current time in UTC is 11:59
    if (currentTime.getUTCHours() === 11 && currentTime.getUTCMinutes() === 59) {
      Logger.info('Starting to calculate and store daily scanned host metrics');

      // Get current scannedHosts
      const scannedHosts = await ScannedHost.find({});
      if (scannedHosts.length === 0) {
        return `Stored new scanned host metrics for today`;
      }

      // Insert values in ScannedHostDailyMetrics
      const newDocs = scannedHosts.flatMap((asset) => {
        const { _id, status, networks } = asset;

        // If the networks array is empty
        if (networks.length === 0) {
          return [{ status, metadata: { assetId: _id } }];
        }

        // If the networks array is not empty
        return networks.map((network) => ({
          status,
          metadata: { assetId: _id, networkId: network },
        }));
      });

      // Insert new documents into ScannedHostDailyMetrics
      await ScannedHostDailyMetrics.insertMany(newDocs);

      return `Stored new scanned host metrics for today`;
    }

    // If not 11:59 UTC
    return 'Skipped storing metrics - not the scheduled time (11:59 UTC)';
  } catch (error) {
    Logger.error('Error storing daily scanned host metrics:', error);
    throw error;
  }
};

export const getLast30DaysScannedHostMetrics = async (
  networkIds: string[]
): Promise<ScannedHostMetricsTimeline> => {
  // Get today's metrics (real-time calculation) with optional network filter
  const todayMetrics = await calculateScannedHostMetrics(networkIds);
  const today = new Date();

  // Calculate the date 29 days ago
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 29);
  // Yesterday at 23:59:59
  const YesterdayDate = new Date();
  YesterdayDate.setDate(YesterdayDate.getDate() - 1);
  YesterdayDate.setHours(23, 59, 59, 999);

  // Get historical metrics for the last 29 days since yesterday

  const pipeline: PipelineStage[] = [
    {
      // Match the documents created in the last 29 days
      $match: {
        createdAt: { $gte: startDate, $lte: YesterdayDate },
        ...(networkIds.length > 0 && { 'metadata.networkId': { $in: networkIds } }),
      },
    },
    {
      // Dedupe each (day, assetId, status)
      $group: {
        _id: {
          day: { $dateTrunc: { date: '$createdAt', unit: 'day' } },
          assetId: '$metadata.assetId',
          status: '$status',
        },
      },
    },
    {
      // Count per (day, status)
      $group: {
        _id: { day: '$_id.day', status: '$_id.status' },
        count: { $sum: 1 },
      },
    },
    {
      // Pivot Status into fields, one doc per day
      $group: {
        _id: '$_id.day',
        managed: {
          $sum: { $cond: [{ $eq: ['$_id.status', 'managed'] }, '$count', 0] },
        },
        non_managed: {
          $sum: { $cond: [{ $eq: ['$_id.status', 'non_managed'] }, '$count', 0] },
        },
        non_manageable: {
          $sum: { $cond: [{ $eq: ['$_id.status', 'non_manageable'] }, '$count', 0] },
        },
      },
    },
    {
      // Sort by day in descending order
      $sort: { _id: -1 },
    },
    {
      // Limit to the last 29 days
      $limit: 29,
    },
    {
      // Project the fields to include only the day and counts
      $project: {
        _id: 0,
        day: '$_id',
        managed: 1,
        non_managed: 1,
        non_manageable: 1,
      },
    },
  ];

  // Run the aggregation pipeline
  const historicalMetrics = await ScannedHostDailyMetrics.aggregate<{
    day: string;
    managed: number;
    non_managed: number;
    non_manageable: number;
  }>(pipeline);

  // Prepare the results with today's metrics and structure
  const results: ScannedHostMetricsTimeline = {
    managed: [],
    non_managed: [],
    non_manageable: [],
  };

  // Insert today's metrics into the result
  results.managed.push({ x: today.toISOString(), y: todayMetrics.managed });
  results.non_managed.push({ x: today.toISOString(), y: todayMetrics.non_managed });
  results.non_manageable.push({ x: today.toISOString(), y: todayMetrics.non_manageable });

  // Add historical data points
  historicalMetrics.forEach((record) => {
    // Get the date in the required format from createdAt
    const dateString = new Date(record.day).toISOString();
    // Add each metric type to its respective array
    results.managed.push({ x: dateString, y: record.managed });
    results.non_managed.push({ x: dateString, y: record.non_managed });
    results.non_manageable.push({ x: dateString, y: record.non_manageable });
  });

  return results;
};
