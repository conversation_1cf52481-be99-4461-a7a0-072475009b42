import { Types } from 'mongoose';

import { Network, ScannedHost } from '../../schemas';

import { errors } from '@shared/utils/app-errors';

export const getAssetsFromNetwork = async (networkId: Types.ObjectId): Promise<number> => {
  try {
    // Find the network by ID
    const network = await Network.findById(networkId);
    if (!network) throw errors.not_found('Network');

    // Get the scannedHosts associated with the network
    const scannedHostsCount = await ScannedHost.countDocuments({ networks: network._id });

    // Return the count of scanned hosts
    return scannedHostsCount;
  } catch (err) {
    throw err;
  }
};

export const getNetworksFromScan = async (
  interfaces: { name: string; cidr: string }[]
): Promise<Types.ObjectId[]> => {
  try {
    // Get the existing networks
    const existingNetworks = await Network.find({
      cidr: { $in: interfaces.map((iface) => iface.cidr) },
    }).select('_id cidr');

    // Push the existing networks to the networks array
    const networkIds: Types.ObjectId[] = existingNetworks.map((n) => n._id);

    // Set to store existing CIDRs
    const existingCidrs = new Set(existingNetworks.map((n) => n.cidr));

    // Get the non existing networks
    const newInterfaces = interfaces.filter((iface) => !existingCidrs.has(iface.cidr));

    if (newInterfaces.length > 0) {
      const created = await Network.insertMany(
        newInterfaces.map((iface) => ({
          name: iface.cidr,
          cidr: iface.cidr,
          description: `Red detectada y creada automáticamente a partir del escaneo pasivo.`,
          createdBy: 'SYSTEM',
          updatedBy: 'SYSTEM',
        }))
      );
      networkIds.push(...created.map((n) => n._id));
    }

    return networkIds;
  } catch (err) {
    throw err;
  }
};
