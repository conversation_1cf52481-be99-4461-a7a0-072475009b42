import { SchemaBasicModel } from '@shared/models';
import { ManagedStatus } from './scannedHost.model';

export interface ScannedHostMetricsTimeline {
  managed: { x: string; y: number }[];
  non_managed: { x: string; y: number }[];
  non_manageable: { x: string; y: number }[];
}

export interface ScannedHostMetrics {
  managed: number;
  non_managed: number;
  non_manageable: number;
}

export interface ScannedHostDailyMetricsModel extends SchemaBasicModel {
  metadata: { assetId: string; networkId: string };
  status: ManagedStatus;
}
