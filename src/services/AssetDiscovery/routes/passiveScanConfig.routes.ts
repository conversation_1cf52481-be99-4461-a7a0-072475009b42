// Dependencies
import { Router } from 'express';
import { body } from 'express-validator';

// Controllers
import {
  getPassiveScanConfig,
  updatePassiveScanConfig,
  runPassiveScan,
  togglePassiveScanStatus,
} from '../controllers';

// Middlewares
import { hasPermission } from '@shared/middleware/auth-middleware';
import { validateBody } from '@shared/middleware/validation-middleware';
import { createRateLimiter } from '@shared/utils/rate-limit';

// Rate Limit for passive scan execution
const runScanLimiter = createRateLimiter(
  // Limit to 1 request every 5 minutes
  5 * 60 * 1000,
  1
);

const router = Router();

router.get('/', hasPermission('read:asset-discovery.passive-scan-config'), getPassiveScanConfig);

router.patch(
  '/',
  hasPermission('modify:asset-discovery.passive-scan-config'),
  [
    body('interval').optional({ values: 'undefined' }).isInt({ min: 3600, max: 604800 }),
    body('batchSize').optional({ values: 'undefined' }).isInt({ min: 100, max: 1000 }),
    body('groups').optional({ values: 'undefined' }).isArray({ min: 1, max: 20 }),
  ],
  validateBody,
  updatePassiveScanConfig
);

router.patch(
  '/toggle',
  hasPermission('modify:asset-discovery.passive-scan-config'),
  [body('status').isBoolean()],
  validateBody,
  togglePassiveScanStatus
);

router.post(
  '/run',
  runScanLimiter,
  hasPermission('execute:asset-discovery.passive-scan-config'),
  runPassiveScan
);

export default router;
