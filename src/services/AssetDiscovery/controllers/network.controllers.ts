import { RequestHand<PERSON> } from 'express';
import { startSession } from 'mongoose';
import IPCIDR from 'ip-cidr';

import { Network, NetworkDocument } from '../schemas/';

import { GetAllQuery } from '@shared/types';

import { ServiceResponse } from '@shared/models/service-response';
import catchAsync from '@shared/utils/catch-async';
import { errors } from '@shared/utils/app-errors';
import { getAssetsFromNetwork } from '../helpers/functions';

export const getNetworkFilters: RequestHandler = catchAsync(async (_, res) => {
  const skippedFilter = ['deleted', 'protected', 'enabled'];
  const [filter, fields] = await Network.createFilter(undefined, skippedFilter);
  return ServiceResponse.get({ filter, fields }).send(res);
});

export const getNetworks: RequestHandler = catchAsync(async (req, res) => {
  const { limit = 100, offset = 0, filter = '', sort = '' } = req.query as GetAllQuery;
  const [query, sortQuery] = Network.parseFilter(filter, sort);

  const [filteredResults, filteredResultsCount] = await Promise.all([
    Network.find(query)
      .collation({ locale: 'en' }) // Case-insensitive sorting
      .sort(sortQuery ?? 'name') // Sort by name by default
      .limit(limit ?? 100)
      .skip(offset ?? 0),
    Network.countDocuments(query),
  ]);

  const finalResults = await Promise.all(
    filteredResults.map(async (network) => {
      try {
        const count = await getAssetsFromNetwork(network._id);
        return { ...network.toObject(), count: count };
      } catch (_) {
        return { ...network.toObject(), count: undefined };
      }
    })
  );

  return ServiceResponse.get({
    meta: {
      count: filteredResultsCount,
      resources: filteredResults.length,
      offset: +(offset ?? 0),
    },
    data: finalResults,
  }).send(res);
});

export const getNetworkById: RequestHandler = catchAsync(async (req, res) => {
  const ruleId = req.params.id;

  const network = await Network.findById(ruleId);
  if (!network) throw errors.not_found('Network');

  const count = await getAssetsFromNetwork(network._id);

  return ServiceResponse.get({ ...network.toObject(), count: count }).send(res);
});

export const createNetwork: RequestHandler = catchAsync(async (req, res) => {
  const { name, description, cidr } = req.body as {
    name: string;
    description?: string;
    cidr: string;
  };

  // Start MongoDB session for transaction
  const session = await startSession();
  session.startTransaction();

  try {
    // Check for duplicate network name within the transaction
    const existingNetwork = await Network.findOne({ name }).session(session);
    if (existingNetwork) throw errors.already_exists('Network');

    // Validate IP Range (CIDR format)
    if (cidr && !IPCIDR.isValidCIDR(cidr)) {
      throw errors.not_valid('IP Range');
    }

    // Create the network within the transaction
    const network = await Network.create(
      [
        {
          name,
          description,
          cidr,
          createdBy: req.user?.email,
          updatedBy: req.user?.email,
        },
      ],
      { session }
    );

    // Commit the transaction
    await session.commitTransaction();

    // Send the response
    return ServiceResponse.post(network[0]).send(res);
  } catch (error: any) {
    // Rollback the transaction in case of an error
    await session.abortTransaction();

    throw error;
  } finally {
    // End the session
    await session.endSession();
  }
});

export const editNetworkById: RequestHandler = catchAsync(async (req, res) => {
  // Get the id from the params
  const networkId = req.params.id;

  // Get the body
  const { name, cidr, description } = req.body as {
    name?: string;
    description?: string;
    cidr?: string;
  };

  // Start MongoDB session for transaction
  const session = await startSession();
  session.startTransaction();

  try {
    // Check for the existence of the network inside the transaction
    const existingNetwork = await Network.findById(networkId).session(session);
    if (!existingNetwork) throw errors.not_found('Network');

    // Collect the fields to update only if they are different from the existing values
    const updateFields: Partial<NetworkDocument> = {};

    if (name && existingNetwork.name !== name) updateFields.name = name;
    if (description !== undefined && existingNetwork.description !== description)
      updateFields.description = description;

    // Handle IP Range update
    if (cidr) {
      if (!IPCIDR.isValidCIDR(cidr)) throw errors.not_valid('IP Range');
      updateFields.cidr = cidr;
    }

    // Apply final updates to the network inside transaction
    const updatedNetwork = await Network.findByIdAndUpdate(
      networkId,
      { ...updateFields, updatedBy: req.user?.email },
      {
        new: true,
        session,
      }
    );
    if (!updatedNetwork) throw errors.not_found('Network');

    const count = await getAssetsFromNetwork(updatedNetwork._id);

    // Commit the transaction
    await session.commitTransaction();

    return ServiceResponse.patch({ ...updatedNetwork.toObject(), count: count }).send(res);
  } catch (error: any) {
    // Rollback the transaction
    await session.abortTransaction();
    throw error;
  } finally {
    // End the session
    await session.endSession();
  }
});

export const removeNetworkById: RequestHandler = catchAsync(async (req, res) => {
  // Get the id from the request
  const networkId = req.params.id;

  const network = await Network.findByIdAndDelete(networkId);
  if (!network) throw errors.not_found('Network');

  return ServiceResponse.delete().send(res);
});

export const removeManyNetwork: RequestHandler = catchAsync(async (req, res) => {
  // Get ids from request params
  const { networks } = req.body as { networks: string[] };

  // Delete the networks
  await Network.deleteMany({ _id: { $in: networks } });

  res.status(204).json({});
});
