import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { ManagedStatus, ScannedHostDailyMetricsModel } from '../helpers/models';

const scannedHostDailyMetricsSchema = schemaFactory(
  {
    metadata: { assetId: String, networkId: String },
    status: { type: String, enum: ManagedStatus, required: true },
  },
  {
    timeseries: {
      timeField: 'createdAt',
      granularity: 'hours',
      metaField: 'networkId',
    },
  }
);

// Document type with Mongoose specifics
export type ScannedHostDailyMetricsDocument = ScannedHostDailyMetricsModel &
  Document & { _id: Types.ObjectId };

// Export the model
export const ScannedHostDailyMetrics = modelFactory<ScannedHostDailyMetricsModel>(
  'ScannedHostDailyMetrics',
  scannedHostDailyMetricsSchema
);
