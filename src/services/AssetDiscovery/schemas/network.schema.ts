import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { NetworkModel } from '../helpers/models';
import { MAX_NETWORK_NAME, MAX_NETWORK_DESCRIPTION } from '../helpers/constants';

const networkSchema = schemaFactory({
  name: {
    type: String,
    required: true,
    maxlength: MAX_NETWORK_NAME,
    minlength: 1,
    unique: true,
  },
  description: { type: String, maxlength: MAX_NETWORK_DESCRIPTION, default: '' },
  cidr: { type: String, required: true, unique: true },
  createdBy: { type: String, required: true },
  updatedBy: { type: String, required: true },
});

export type NetworkDocument = NetworkModel & Document & { _id: Types.ObjectId };
export const Network = modelFactory<NetworkModel>('Network', networkSchema);
