import { Logger } from '@shared/helpers/classes/logger.class';
import { BatchAccumulator } from '@shared/helpers/classes/batch-accumulator.class';
import { BatchMessage } from '@shared/types';
import { AssetDiscoveryMessage, ScanResultsModel } from '../helpers/models';
import { processMacFinding } from '../helpers/functions/scannedHost.functions';
import { SOAR_ID } from '@shared/constants/env';

// Create an instance of the accumulator
const batchAccumulator = new BatchAccumulator();

export const processScanResult = async (message: AssetDiscoveryMessage): Promise<void> => {
  try {
    const { hostId, outputBatch } = message;

    if (!outputBatch || !hostId) {
      Logger.error('Received invalid message format');
      return;
    }

    const batchMessage = message as BatchMessage<ScanResultsModel>;

    // Add the incoming message to the accumulator
    const scanResults = batchAccumulator.addMessage(batchMessage, `${SOAR_ID}-${hostId}`);

    if (scanResults) {
      for (const result of scanResults) {
        await processMacFinding(result, hostId);
      }
    }
  } catch (error) {
    Logger.error('Error processing scan result for Scanned Host:', error);
  }
};
