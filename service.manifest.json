{"modules": [{"name": "Overview", "internalName": "overview", "functionalities": ["rport.clients", "rport.groups", "rport.tags", "rport.os-updates", "rport.platform-templates", "rport.platforms"], "isTelegramPermission": false}, {"name": "Host management", "internalName": "host-management", "functionalities": ["rport.clients", "rport.groups", "rport.tags", "rport.os-updates", "rport.platforms"], "isTelegramPermission": false}, {"name": "Deployment", "internalName": "deployment", "functionalities": ["rport.clients", "rport.platform-templates", "rport.platforms", "rport.jobs"], "isTelegramPermission": false}, {"name": "Response Actions", "internalName": "response-actions", "functionalities": ["rport.clients", "rport.scripts", "rport.script-tags"], "isTelegramPermission": false}, {"name": "V<PERSON><PERSON>", "internalName": "vciso", "functionalities": ["vciso.vciso"], "isTelegramPermission": false}, {"name": "Dashboard", "internalName": "dashboard", "functionalities": ["dashboards.powerBiStore", "dashboards.tags", "dashboards.users"], "isTelegramPermission": false}, {"name": "Compliance", "internalName": "compliance", "functionalities": ["compliance.policy", "compliance.playbook"], "isTelegramPermission": false}, {"name": "Inventory", "internalName": "inventory", "functionalities": ["inventory.host-inventory", "inventory.application", "inventory.scan-policy", "inventory.application-policy", "inventory.application-policy-status"], "isTelegramPermission": false}, {"name": "Cloud", "internalName": "cloud", "functionalities": ["cloud-compliance.companies", "cloud-compliance.providers", "cloud-compliance.operations", "cloud-compliance.reports"], "isTelegramPermission": false}, {"name": "ZeroAPT", "internalName": "zeroApt", "functionalities": ["apt.fixes", "apt.hosts"], "isTelegramPermission": false}, {"name": "Anti-ransomware", "internalName": "anti-ransomware", "functionalities": ["halcyon.device", "halcyon.event", "halcyon.threat", "halcyon.report"], "isTelegramPermission": false}, {"name": "Patching", "internalName": "patching", "functionalities": ["automox.dashboard", "automox.device", "automox.software"], "isTelegramPermission": false}, {"name": "Security in a Box", "internalName": "security-box", "functionalities": ["security-box.overview", "security-box.knowledge-base", "security-box.journey-status"], "isTelegramPermission": false}, {"name": "Queue management", "internalName": "queue-management", "functionalities": ["queues.task"], "isTelegramPermission": false}, {"name": "Batuta Reports", "internalName": "reports", "functionalities": ["reports.reports-management", "reports.scheduled-reports"], "isTelegramPermission": false}, {"name": "Asset Discovery", "internalName": "asset-discovery", "functionalities": ["asset-discovery.passive-scan-config", "asset-discovery.network", "asset-discovery.scanned-hosts"], "isTelegramPermission": false}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "alienvault", "functionalities": ["alienvault.ipv4", "alienvault.ipv6", "alienvault.url", "alienvault.domain", "alienvault.host", "alienvault.cve"], "isTelegramPermission": true}, {"name": "Pentesting", "internalName": "pentestools", "functionalities": ["pentestools.rdp", "pentestools.web", "pentestools.port", "pentestools.port-list", "pentestools.port-range"], "isTelegramPermission": true}, {"name": "Have I Been Pwned", "internalName": "pwned", "functionalities": ["pwned.breached", "pwned.breaches", "pwned.breach", "pwned.paste"], "isTelegramPermission": true}, {"name": "VirusTotal", "internalName": "virustotal", "functionalities": ["virustotal.ip", "virustotal.url", "virustotal.domain", "virustotal.hash"], "isTelegramPermission": true}, {"name": "Miscellaneous", "internalName": "lambdas", "functionalities": ["lambdas.who", "lambdas.base64"], "isTelegramPermission": true}, {"name": "MX Toolbox", "internalName": "mx-tools", "functionalities": ["mx-tools.spf", "mx-tools.dkim", "mx-tools.reverse", "mx-tools.dmarc", "mx-tools.bimi", "mx-tools.dns", "mx-tools.mx", "mx-tools.blacklist", "mx-tools.smtp"], "isTelegramPermission": true}, {"name": "CrowdStrike", "internalName": "crowdstrike", "functionalities": ["crowdstrike.detections", "crowdstrike.rtr", "crowdstrike.contain", "crowdstrike.lift_containment", "crowdstrike.lift_autocont", "crowdstrike.ioc", "crowdstrike.group-rtr", "crowdstrike.group-contain", "crowdstrike.rtr-ad", "crowdstrike.single-rtr", "crowdstrike.rtr-custom", "crowdstrike.ad", "crowdstrike.action", "crowdstrike.console"], "isTelegramPermission": true}, {"name": "Vulnerabilities", "internalName": "vulnerabilities", "functionalities": ["vulnerabilities.vulnerabilities"], "isTelegramPermission": false}], "services": [{"name": "<PERSON><PERSON>", "internalName": "batuta", "type": "PRO", "functionalities": [{"name": "Tokens", "internalName": "tokens", "permissions": ["create", "read", "modify", "delete"]}]}, {"name": "Rport", "internalName": "rport", "type": "PRO", "functionalities": [{"name": "Clients", "internalName": "clients", "permissions": ["read", "create", "delete"], "tours": [{"internalName": "host_batuta_install", "permission": "create"}, {"internalName": "host_management", "permission": "read"}, {"internalName": "host_management_host", "permission": "read"}]}, {"name": "Groups", "internalName": "groups", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Tags", "internalName": "tags", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Platform Templates", "internalName": "platform-templates", "permissions": ["read"]}, {"name": "Configuration", "internalName": "config", "permissions": ["read", "modify"]}, {"name": "Jobs", "internalName": "jobs", "permissions": ["read", "create", "export", "delete"], "tours": [{"internalName": "jobs", "permission": "read"}]}, {"name": "Platforms", "internalName": "platforms", "permissions": ["read", "create", "modify", "delete", "execute"], "tours": [{"internalName": "deployment", "permission": "execute"}]}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "scripts", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Script Tags", "internalName": "script-tags", "permissions": ["read", "create", "modify", "delete"]}, {"name": "OS Updates", "internalName": "os-updates", "permissions": ["read"]}]}, {"name": "Automox", "internalName": "automox", "type": "PRO", "functionalities": [{"name": "Dashboard", "internalName": "dashboard", "permissions": ["read"]}, {"name": "<PERSON><PERSON>", "internalName": "device", "permissions": ["read", "modify", "delete", "execute"]}, {"name": "Software", "internalName": "software", "permissions": ["read", "modify", "delete", "execute"]}]}, {"name": "APT", "internalName": "apt", "type": "API", "enabled": false, "functionalities": [{"name": "Simulations", "internalName": "simulations", "permissions": ["read", "create"]}, {"name": "Fixes", "internalName": "fixes", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Hosts", "internalName": "hosts", "permissions": ["read", "execute"]}, {"name": "Mitigation Policies", "internalName": "mitigation-policies", "permissions": ["read", "create", "modify", "delete", "execute"]}]}, {"name": "LevelOfProactivity", "internalName": "level-of-proactivity", "type": "API", "enabled": true, "functionalities": [{"name": "Scores", "internalName": "scores", "permissions": ["read", "modify", "execute", "export"]}, {"name": "Configurations", "internalName": "configurations", "permissions": ["read", "modify", "export"]}]}, {"name": "Halcyon", "internalName": "halcyon", "type": "PRO", "functionalities": [{"name": "<PERSON><PERSON>", "internalName": "device", "permissions": ["read", "delete"], "tours": [{"internalName": "soar_dashboard", "permission": "read"}, {"internalName": "soar_devices", "permission": "read"}]}, {"name": "Event", "internalName": "event", "permissions": ["read", "modify"], "tours": [{"internalName": "soar_dashboard", "permission": "read"}, {"internalName": "soar_event", "permission": "read"}]}, {"name": "Threat", "internalName": "threat", "permissions": ["read"]}, {"name": "Report", "internalName": "report", "permissions": ["read", "create", "modify", "delete"]}]}, {"name": "[Azure] Entra ID", "internalName": "entra-id", "type": "PRO", "functionalities": [{"name": "Revoke All User Sesions", "internalName": "revoke-sessions", "utility": true, "permissions": ["init", "execute"]}]}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "wazuh", "type": "PRO", "functionalities": [{"name": "Active Response", "internalName": "active-response", "utility": true, "permissions": ["init", "execute"]}, {"name": "Manager", "internalName": "manager", "permissions": ["read", "modify", "execute"]}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "alert", "webhook": true, "requires_permission": true, "permissions": ["read", "modify", "execute"]}, {"name": "Contención", "internalName": "contain", "requires_permission": true, "utility": true, "permissions": ["init", "execute"]}, {"name": "Active response grupal", "internalName": "group-active-response", "utility": true, "permissions": ["init", "execute"]}, {"name": "Contención grupal", "internalName": "group-contain", "utility": true, "permissions": ["init", "execute"]}]}, {"name": "CrowdStrike", "internalName": "crowdstrike", "type": "PRO", "functionalities": [{"name": "Detections", "internalName": "detections", "webhook": true, "permissions": ["execute"]}, {"name": "Incidence Response", "internalName": "rtr", "requires_permission": true, "permissions": ["execute"]}, {"name": "Contención", "internalName": "contain", "requires_permission": true, "permissions": ["execute"]}, {"name": "Liberar Contención", "internalName": "lift_containment", "requires_permission": true, "permissions": ["execute"]}, {"name": "Liberar Autocontención", "internalName": "lift_autocont", "requires_permission": true, "permissions": ["execute"]}, {"name": "Agregar IOC", "internalName": "ioc", "utility": true, "permissions": ["execute"]}, {"name": "RTR por grupos", "internalName": "group-rtr", "requires_permission": true, "utility": true, "permissions": ["execute"]}, {"name": "Contención por grupos", "internalName": "group-contain", "requires_permission": true, "utility": true, "permissions": ["execute"]}, {"name": "RTR AD", "internalName": "rtr-ad", "utility": true, "requires_permission": true, "permissions": ["execute"]}, {"name": "RTR Single Host", "internalName": "single-rtr", "requires_permission": true, "utility": true, "permissions": ["execute"]}, {"name": "RTR Custom command", "internalName": "rtr-custom", "requires_permission": true, "permissions": ["execute"]}, {"name": "Active Response", "internalName": "ad", "permissions": ["execute"]}, {"name": "Action", "internalName": "action", "permissions": ["read", "create", "modify", "delete"]}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "console", "permissions": ["read", "create", "modify", "delete"]}]}, {"name": "Dashboard", "internalName": "dashboards", "type": "PRO", "functionalities": [{"name": "Power Bi Store", "internalName": "powerBiStore", "permissions": ["read", "create", "delete"], "tours": [{"internalName": "dashboard_reports", "permission": "read"}]}, {"name": "Tags", "internalName": "tags", "permissions": ["read", "modify"]}, {"name": "Users", "internalName": "users", "permissions": ["read", "modify"]}]}, {"name": "MXTools", "internalName": "mx-tools", "type": "BASIC", "functionalities": [{"name": "Spf Lookup", "internalName": "spf", "utility": true, "permissions": ["execute"]}, {"name": "<PERSON><PERSON>", "internalName": "dkim", "utility": true, "permissions": ["execute"]}, {"name": "Reverse Lookup", "internalName": "reverse", "utility": true, "permissions": ["execute"]}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "dmarc", "utility": true, "permissions": ["execute"]}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "bimi", "utility": true, "permissions": ["execute"]}, {"name": "DNS Lookup", "internalName": "dns", "utility": true, "permissions": ["execute"]}, {"name": "MX Lookup", "internalName": "mx", "utility": true, "permissions": ["execute"]}, {"name": "Blacklist Check", "internalName": "blacklist", "utility": true, "permissions": ["execute"]}, {"name": "SMTP Lookup", "internalName": "smtp", "utility": true, "permissions": ["execute"]}]}, {"name": "Alienvault", "internalName": "alienvault", "type": "BASIC", "functionalities": [{"name": "IPv4", "internalName": "ipv4", "utility": true, "permissions": ["execute"]}, {"name": "IPv6", "internalName": "ipv6", "utility": true, "permissions": ["execute"]}, {"name": "URL", "internalName": "url", "utility": true, "permissions": ["execute"]}, {"name": "Domain", "internalName": "domain", "utility": true, "permissions": ["execute"]}, {"name": "Host Name", "internalName": "host", "utility": true, "permissions": ["execute"]}, {"name": "CVE", "internalName": "cve", "utility": true, "permissions": ["execute"]}]}, {"name": "Pentesting", "internalName": "pentestools", "type": "BASIC", "functionalities": [{"name": "Rdp <PERSON>an", "internalName": "rdp", "utility": true, "permissions": ["execute"]}, {"name": "<PERSON> Scan", "internalName": "web", "utility": true, "permissions": ["execute"]}, {"name": "Common Ports Scan", "internalName": "port", "utility": true, "permissions": ["execute"]}, {"name": "Custom Port List Scan", "internalName": "port-list", "utility": true, "permissions": ["execute"]}, {"name": "Custom Port Range Scan", "internalName": "port-range", "utility": true, "permissions": ["execute"]}]}, {"name": "Pwned", "internalName": "pwned", "type": "BASIC", "functionalities": [{"name": "Breached Account", "internalName": "breached", "utility": true, "permissions": ["execute"]}, {"name": "Breaches Domain", "internalName": "breaches", "utility": true, "permissions": ["execute"]}, {"name": "Breach Info", "internalName": "breach", "utility": true, "permissions": ["execute"]}, {"name": "Paste Account", "internalName": "paste", "utility": true, "permissions": ["execute"]}]}, {"name": "Virustotal", "internalName": "virustotal", "type": "BASIC", "functionalities": [{"name": "IP Scan", "internalName": "ip", "utility": true, "permissions": ["execute"]}, {"name": "URL Scan", "internalName": "url", "utility": true, "permissions": ["execute"]}, {"name": "Domain Scan", "internalName": "domain", "utility": true, "permissions": ["execute"]}, {"name": "<PERSON><PERSON>", "internalName": "hash", "utility": true, "permissions": ["execute"]}]}, {"name": "Feed", "internalName": "feed", "type": "API", "functionalities": []}, {"name": "<PERSON><PERSON>", "internalName": "cron", "type": "API", "functionalities": []}, {"name": "Otras", "internalName": "lambdas", "type": "BASIC", "functionalities": [{"name": "Who is?", "internalName": "who", "utility": true, "permissions": ["execute"]}, {"name": "Base64", "internalName": "base64", "utility": true, "permissions": ["execute"]}]}, {"name": "SecurityBox", "type": "API", "internalName": "security-box", "functionalities": [{"name": "Overview", "internalName": "overview", "permissions": ["read"]}, {"name": "Knowledge Base", "internalName": "knowledge-base", "permissions": ["read"]}, {"name": "Journey Status", "internalName": "journey-status", "permissions": ["read"]}]}, {"name": "BatutaDoc", "type": "API", "internalName": "batutadoc", "functionalities": [{"name": "Get book", "internalName": "book"}, {"name": "Get page", "internalName": "page"}]}, {"name": "VCISO", "type": "API", "internalName": "vciso", "functionalities": [{"name": "VCISO", "internalName": "vciso", "permissions": ["read", "execute"]}]}, {"name": "Compliance", "type": "PRO", "internalName": "compliance", "functionalities": [{"name": "Policy", "internalName": "policy", "permissions": ["read", "create", "execute", "modify", "delete"]}, {"name": "Playbook", "internalName": "playbook", "permissions": ["read", "create", "modify", "delete"]}]}, {"name": "Queues", "internalName": "queues", "type": "API", "enabled": false, "functionalities": [{"name": "Task", "internalName": "task", "permissions": ["read", "create", "modify", "delete"]}]}, {"name": "Export", "internalName": "export", "type": "API", "functionalities": []}, {"name": "Netskope", "internalName": "netskope", "type": "PRO", "functionalities": []}, {"name": "Rapid7", "internalName": "rapid7", "type": "PRO", "functionalities": []}, {"name": "OCS", "internalName": "ocs", "type": "PRO", "functionalities": []}, {"name": "Ad<PERSON>in", "internalName": "adlumin", "type": "PRO", "functionalities": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "internalName": "sumologic", "type": "PRO", "functionalities": []}, {"name": "Velociraptor", "internalName": "velociraptor", "type": "PRO", "functionalities": []}, {"name": "Qualys", "internalName": "qualys", "type": "PRO", "functionalities": []}, {"name": "Hardening", "internalName": "hardening", "type": "PRO", "functionalities": []}, {"name": "<PERSON><PERSON><PERSON>", "internalName": "sysmon", "type": "PRO", "functionalities": []}, {"name": "Inventory", "internalName": "inventory", "type": "API", "functionalities": [{"name": "Host Inventory", "internalName": "host-inventory", "permissions": ["read", "export"]}, {"name": "Application", "internalName": "application", "permissions": ["read", "export", "execute"]}, {"name": "Scan Policy", "internalName": "scan-policy", "permissions": ["read", "create", "modify", "delete", "execute"]}, {"name": "Application policy", "internalName": "application-policy", "permissions": ["read", "create", "modify", "delete", "execute", "export"]}, {"name": "Application policy status", "internalName": "application-policy-status", "permissions": ["read", "execute", "export"]}]}, {"name": "CloudCompliance", "type": "API", "internalName": "cloud-compliance", "functionalities": [{"name": "Cloud Companies", "internalName": "companies", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Cloud Providers", "internalName": "providers", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Cloud Operations", "internalName": "operations", "permissions": ["read"]}, {"name": "Cloud Reports", "internalName": "reports", "permissions": ["read", "create"]}]}, {"name": "Reports", "type": "API", "internalName": "reports", "enabled": true, "functionalities": [{"name": "Reports Management", "internalName": "reports-management", "permissions": ["read", "create", "delete"]}, {"name": "Scheduled Reports", "internalName": "scheduled-reports", "permissions": ["read", "create", "modify", "delete"]}]}, {"name": "Vulnerabilities", "internalName": "vulnerabilities", "type": "API", "enabled": false, "functionalities": [{"name": "Vulnerabilities", "internalName": "vulnerabilities", "permissions": ["read"]}]}, {"name": "AssetDiscovery", "type": "API", "internalName": "asset-discovery", "enabled": false, "functionalities": [{"name": "Passive <PERSON><PERSON> Config", "internalName": "passive-scan-config", "permissions": ["read", "modify", "execute"]}, {"name": "Network", "internalName": "network", "permissions": ["read", "create", "modify", "delete"]}, {"name": "Scanned Hosts", "internalName": "scanned-hosts", "permissions": ["read", "modify"]}]}], "tours": [{"internalName": "sign_in"}, {"internalName": "sign_out"}, {"internalName": "tenant_change"}, {"internalName": "locale_change"}, {"internalName": "theme_change"}, {"internalName": "profile"}, {"internalName": "profile_2fa"}, {"internalName": "profile_password_change"}, {"internalName": "profile_timezone_change"}, {"internalName": "sidebar"}, {"internalName": "documentation_search"}, {"internalName": "contact_support"}, {"internalName": "soar_dashboard"}, {"internalName": "soar_devices"}, {"internalName": "soar_event"}, {"internalName": "host_batuta_install"}, {"internalName": "host_management"}, {"internalName": "host_management_host", "enabled": false}, {"internalName": "jobs", "enabled": false}, {"internalName": "deployment", "enabled": false}, {"internalName": "dashboard_reports"}, {"internalName": "overview"}], "crontabs": [{"name": "crowdstrike-token", "timelapse": "1500"}, {"name": "crowdstrike-clean_encrypted", "timelapse": "60"}, {"name": "crowdstrike-autocontainment", "timelapse": "60"}, {"name": "crowdstrike-clear_containment_buttons", "timelapse": "60"}, {"name": "temp-clear", "timelapse": "60"}, {"name": "queue-handler", "timelapse": "60"}, {"name": "wazuh-token", "timelapse": "1500", "enabled": false}, {"name": "wazuh-clean_encrypted", "timelapse": "60", "enabled": false}, {"name": "wazuh-clear_containment_buttons", "timelapse": "60", "enabled": false}, {"name": "wazuh-autocontainment", "timelapse": "60", "enabled": false}, {"name": "rport-tokens", "timelapse": "60"}, {"name": "rport-host_validator", "timelapse": "60"}, {"name": "rport-host_expired_process", "timelapse": "60"}, {"name": "rport-host_expired_uninstall", "timelapse": "60"}, {"name": "rport-host_update", "timelapse": "21600"}, {"name": "rport-host_upgrade", "timelapse": "1800", "enabled": true}, {"name": "compliance-policy_execution", "timelapse": "60"}, {"name": "queues-tasks-clear", "timelapse": "43200"}, {"name": "queue-expire-tasks", "timelapse": "86400"}, {"name": "queue-finished-tasks-clear", "timelapse": "86400"}, {"name": "queues-tasks", "timelapse": "60"}, {"name": "compliance-run_validation", "timelapse": "60"}, {"name": "compliance-generate_snapshots", "timelapse": "86400"}, {"name": "cdn-clear", "timelapse": "1500"}, {"name": "inventory-run_scan_policies", "timelapse": "60"}, {"name": "level-of-proactivity-update-scores", "timelapse": "86400"}, {"name": "apt-mitigation-run_scan_policies", "timelapse": "60"}, {"name": "rport-routines", "timelapse": "86400"}, {"name": "scheduled-report_trigger", "timelapse": "60"}, {"name": "application-policy-check-changes", "timelapse": "900"}, {"name": "enable-process-monitoring", "timelapse": "900"}, {"name": "scanned-host-daily-metrics", "timelapse": "60"}, {"name": "passive-network-scan", "timelapse": "60"}], "platform_templates": [{"service": "crowdstrike", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/crowdstrike/windows/Install-Crowdstrike.ps1\" -OutFile \"Install-Crowdstrike.ps1\"; .\\Install-Crowdstrike.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -gt \"{{groupTag}}\"", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/crowdstrike/windows/Install-Crowdstrike.ps1\" -OutFile \"Install-Crowdstrike.ps1\"; .\\Install-Crowdstrike.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -gt \"{{groupTag}}\"", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/crowdstrike/windows/Install-Crowdstrike.ps1\" -OutFile \"Install-Crowdstrike.ps1\"; .\\Install-Crowdstrike.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -gt \"{{groupTag}}\"", "timeout": 900}}, "linux": {"debian": {"commandString": "_cs_script_path=$(mktemp -q /tmp/install-cs-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/crowdstrike/linux/install-crowdstrike.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -k {{accessKey}} -u {{installerDebian}} -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}} -g \"{{groupTag}}\"; rm -rf $_cs_script_path", "timeout": 900}, "rhel": {"commandString": "_cs_script_path=$(mktemp -q /tmp/install-cs-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/crowdstrike/linux/install-crowdstrike.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -k {{accessKey}} -u {{installerRhel}} -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}} -g \"{{groupTag}}\"; rm -rf $_cs_script_path", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/crowdstrike/windows/Uninstall-Crowdstrike.ps1\" -OutFile \"Uninstall-Crowdstrike.ps1\"; .\\Uninstall-Crowdstrike.ps1 -u {{uninstaller}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/crowdstrike/windows/Uninstall-Crowdstrike.ps1\" -OutFile \"Uninstall-Crowdstrike.ps1\"; .\\Uninstall-Crowdstrike.ps1 -u {{uninstaller}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/crowdstrike/windows/Uninstall-Crowdstrike.ps1\" -OutFile \"Uninstall-Crowdstrike.ps1\"; .\\Uninstall-Crowdstrike.ps1 -u {{uninstaller}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}, "linux": {"debian": {"commandString": "_cs_script_path=$(mktemp -q /tmp/uninstall-cs-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/crowdstrike/linux/uninstall-crowdstrike.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_cs_script_path", "timeout": 600}, "rhel": {"commandString": "_cs_script_path=$(mktemp -q /tmp/uninstall-cs-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/crowdstrike/linux/uninstall-crowdstrike.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_cs_script_path", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/crowdstrike/windows/Status-Crowdstrike.ps1\" -OutFile \"Status-Crowdstrike.ps1\"; .\\Status-Crowdstrike.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -tid {{templateId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/crowdstrike/windows/Status-Crowdstrike.ps1\" -OutFile \"Status-Crowdstrike.ps1\"; .\\Status-Crowdstrike.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -tid {{templateId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/crowdstrike/windows/Status-Crowdstrike.ps1\" -OutFile \"Status-Crowdstrike.ps1\"; .\\Status-Crowdstrike.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -tid {{templateId}}", "timeout": 60}}, "linux": {"debian": {"commandString": "_cs_script_path=$(mktemp -q /tmp/status-cs-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/crowdstrike/linux/status-crowdstrike.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_cs_script_path", "timeout": 60}, "rhel": {"commandString": "_cs_script_path=$(mktemp -q /tmp/status-cs-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/crowdstrike/linux/status-crowdstrike.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_cs_script_path", "timeout": 60}}}}, "parameters": {"accessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/crowdstrike/windows/WindowsSensor.exe", "provided": true, "requiresUserAction": false}, "uninstaller": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/crowdstrike/windows/CsUninstallTool.exe", "provided": true, "requiresUserAction": false}, "installerDebian": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/crowdstrike/linux/debian/falcon-sensor_[[arch]]_US1.deb", "provided": true, "requiresUserAction": false}, "installerRhel": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/crowdstrike/linux/rpm/falcon-sensor_[[arch]]_[[rhel_version]]_US1.rpm", "provided": true, "requiresUserAction": false}, "clientId": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "groupTag": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {"url": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "clientId": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "secretId": {"required": true, "type": "string", "value": "", "requiresUserAction": false}}}, {"service": "halcyon", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/halcyon/windows/Install-Halcyon.ps1\" -OutFile \"Install-Halcyon.ps1\"; .\\Install-Halcyon.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/halcyon/windows/Install-Halcyon.ps1\" -OutFile \"Install-Halcyon.ps1\"; .\\Install-Halcyon.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/halcyon/windows/Install-Halcyon.ps1\" -OutFile \"Install-Halcyon.ps1\"; .\\Install-Halcyon.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/halcyon/windows/Uninstall-Halcyon.ps1\" -OutFile \"Uninstall-Halcyon.ps1\"; .\\Uninstall-Halcyon.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/halcyon/windows/Uninstall-Halcyon.ps1\" -OutFile \"Uninstall-Halcyon.ps1\"; .\\Uninstall-Halcyon.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/halcyon/windows/Uninstall-Halcyon.ps1\" -OutFile \"Uninstall-Halcyon.ps1\"; .\\Uninstall-Halcyon.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/halcyon/windows/Status-Halcyon.ps1\" -OutFile \"Status-Halcyon.ps1\"; .\\Status-Halcyon.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/halcyon/windows/Status-Halcyon.ps1\" -OutFile \"Status-Halcyon.ps1\"; .\\Status-Halcyon.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/halcyon/windows/Status-Halcyon.ps1\" -OutFile \"Status-Halcyon.ps1\"; .\\Status-Halcyon.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"accessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/halcyon/windows/HAR.exe", "provided": true, "requiresUserAction": false}, "clientId": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {"username": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "password": {"required": true, "type": "string", "value": "", "requiresUserAction": false}}}, {"service": "automox", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Install-Automox.ps1\" -OutFile \"Install-Automox.ps1\"; .\\Install-Automox.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Install-Automox.ps1\" -OutFile \"Install-Automox.ps1\"; .\\Install-Automox.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Install-Automox.ps1\" -OutFile \"Install-Automox.ps1\"; .\\Install-Automox.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}, "linux": {"debian": {"commandString": "_cs_script_path=$(mktemp -q /tmp/install-au-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/automox/linux/install-automox.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -k {{accessKey}} -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}} -g \"{{groupTag}}\"; rm -rf $_cs_script_path", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Uninstall-Automox.ps1\" -OutFile \"Uninstall-Automox.ps1\"; .\\Uninstall-Automox.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Uninstall-Automox.ps1\" -OutFile \"Uninstall-Automox.ps1\"; .\\Uninstall-Automox.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Uninstall-Automox.ps1\" -OutFile \"Uninstall-Automox.ps1\"; .\\Uninstall-Automox.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}, "linux": {"debian": {"commandString": "_cs_script_path=$(mktemp -q /tmp/uninstall-au-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/automox/linux/uninstall-automox.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_cs_script_path", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Status-Automox.ps1\" -OutFile \"Status-Automox.ps1\"; .\\Status-Automox.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Status-Automox.ps1\" -OutFile \"Status-Automox.ps1\"; .\\Status-Automox.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/automox/windows/Status-Automox.ps1\" -OutFile \"Status-Automox.ps1\"; .\\Status-Automox.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}, "linux": {"debian": {"commandString": "_cs_script_path=$(mktemp -q /tmp/status-au-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/automox/linux/status-automox.sh\" -o $_cs_script_path && sudo bash $_cs_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_cs_script_path", "timeout": 600}}}}, "parameters": {"accessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/automox/windows/Automox_Installer-latest.msi", "provided": true, "requiresUserAction": false}, "groupTag": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "clientId": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {"apiKey": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "orgID": {"required": true, "type": "string", "value": "", "requiresUserAction": false}}}, {"service": "netskope", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/netskope/windows/Install-Netskope.ps1\" -OutFile \"Install-Netskope.ps1\"; .\\Install-Netskope.ps1 -a {{accessKey}} -h {{host}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -at {{enrollauthtoken}} -et {{enrollencryptiontoken}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/netskope/windows/Install-Netskope.ps1\" -OutFile \"Install-Netskope.ps1\"; .\\Install-Netskope.ps1 -a {{accessKey}} -h {{host}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -at {{enrollauthtoken}} -et {{enrollencryptiontoken}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/netskope/windows/Install-Netskope.ps1\" -OutFile \"Install-Netskope.ps1\"; .\\Install-Netskope.ps1 -a {{accessKey}} -h {{host}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -at {{enrollauthtoken}} -et {{enrollencryptiontoken}}", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/netskope/windows/Uninstall-Netskope.ps1\" -OutFile \"Uninstall-Netskope.ps1\"; .\\Uninstall-Netskope.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/netskope/windows/Uninstall-Netskope.ps1\" -OutFile \"Uninstall-Netskope.ps1\"; .\\Uninstall-Netskope.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/netskope/windows/Uninstall-Netskope.ps1\" -OutFile \"Uninstall-Netskope.ps1\"; .\\Uninstall-Netskope.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/netskope/windows/Status-Netskope.ps1\" -OutFile \"Status-Netskope.ps1\"; .\\Status-Netskope.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/netskope/windows/Status-Netskope.ps1\" -OutFile \"Status-Netskope.ps1\"; .\\Status-Netskope.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -<PERSON>ri \"{{cdnBaseUrl}}/netskope/windows/Status-Netskope.ps1\" -OutFile \"Status-Netskope.ps1\"; .\\Status-Netskope.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"accessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "host": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.digitaloceanspaces.com/netskope/windows/Netskope_installer_latest.msi", "provided": true, "requiresUserAction": false}, "clientId": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "enrollauthtoken": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": true}, "enrollencryptiontoken": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {"baseURL": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "token": {"required": true, "type": "string", "value": "", "requiresUserAction": false}}}, {"service": "rapid7", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Install-Rapid7.ps1\" -OutFile \"Install-Rapid7.ps1\"; .\\Install-Rapid7.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Install-Rapid7.ps1\" -OutFile \"Install-Rapid7.ps1\"; .\\Install-Rapid7.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Install-Rapid7.ps1\" -OutFile \"Install-Rapid7.ps1\"; .\\Install-Rapid7.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Uninstall-Rapid7.ps1\" -OutFile \"Uninstall-Rapid7.ps1\"; .\\Uninstall-Rapid7.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Uninstall-Rapid7.ps1\" -OutFile \"Uninstall-Rapid7.ps1\"; .\\Uninstall-Rapid7.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Uninstall-Rapid7.ps1\" -OutFile \"Uninstall-Rapid7.ps1\"; .\\Uninstall-Rapid7.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Status-Rapid7.ps1\" -OutFile \"Status-Rapid7.ps1\"; .\\Status-Rapid7.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Status-Rapid7.ps1\" -OutFile \"Status-Rapid7.ps1\"; .\\Status-Rapid7.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/rapid7/windows/Status-Rapid7.ps1\" -OutFile \"Status-Rapid7.ps1\"; .\\Status-Rapid7.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"accessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/rapid7/windows/AGR7.msi", "provided": true, "requiresUserAction": false}, "groupTag": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "clientId": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {}}, {"service": "qualys", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Install-Qualys.ps1\" -OutFile \"Install-Qualys.ps1\"; .\\Install-Qualys.ps1 -u {{installer}} -cid \"{{customerId}}\" -aid \"{{activationId}}\" -wsu \"{{webserviceUri}}\" -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Install-Qualys.ps1\" -OutFile \"Install-Qualys.ps1\"; .\\Install-Qualys.ps1 -u {{installer}} -cid \"{{customerId}}\" -aid \"{{activationId}}\" -wsu \"{{webserviceUri}}\" -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Install-Qualys.ps1\" -OutFile \"Install-Qualys.ps1\"; .\\Install-Qualys.ps1 -u {{installer}} -cid \"{{customerId}}\" -aid \"{{activationId}}\" -wsu \"{{webserviceUri}}\" -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}, "linux": {"debian": {"commandString": "_qualys_script_path=$(mktemp -q /tmp/install-qualys-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/qualys/linux/install-qualys.sh\" -o $_qualys_script_path && sudo bash $_qualys_script_path -k {{activationId}} -i {{customerId}} -u {{installerLinux}} -a {{baseURL}} -w {{webserviceUri}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_qualys_script_path", "timeout": 900}, "rhel": {"commandString": "_qualys_script_path=$(mktemp -q /tmp/install-qualys-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/qualys/linux/install-qualys.sh\" -o $_qualys_script_path && sudo bash $_qualys_script_path -k {{activationId}} -i {{customerId}} -u {{installerLinux}} -a {{baseURL}} -w {{webserviceUri}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_qualys_script_path", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Uninstall-Qualys.ps1\" -OutFile \"Uninstall-Qualys.ps1\"; .\\Uninstall-Qualys.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Uninstall-Qualys.ps1\" -OutFile \"Uninstall-Qualys.ps1\"; .\\Uninstall-Qualys.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Uninstall-Qualys.ps1\" -OutFile \"Uninstall-Qualys.ps1\"; .\\Uninstall-Qualys.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}, "linux": {"debian": {"commandString": "_qualys_script_path=$(mktemp -q /tmp/uninstall-qualys-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/qualys/linux/uninstall-qualys.sh\" -o $_qualys_script_path && sudo bash $_qualys_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_qualys_script_path", "timeout": 600}, "rhel": {"commandString": "_qualys_script_path=$(mktemp -q /tmp/uninstall-qualys-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/qualys/linux/uninstall-qualys.sh\" -o $_qualys_script_path && sudo bash $_qualys_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_qualys_script_path", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Status-Qualys.ps1\" -OutFile \"Status-Qualys.ps1\"; .\\Status-Qualys.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Status-Qualys.ps1\" -OutFile \"Status-Qualys.ps1\"; .\\Status-Qualys.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/qualys/windows/Status-Qualys.ps1\" -OutFile \"Status-Qualys.ps1\"; .\\Status-Qualys.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}, "linux": {"debian": {"commandString": "_qualys_script_path=$(mktemp -q /tmp/status-qualys-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/qualys/linux/status-qualys.sh\" -o $_qualys_script_path && sudo bash $_qualys_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_qualys_script_path", "timeout": 60}, "rhel": {"commandString": "_qualys_script_path=$(mktemp -q /tmp/status-qualys-XXXXX.sh) && curl -Ls \"{{cdnBaseUrl}}/qualys/linux/status-qualys.sh\" -o $_qualys_script_path && sudo bash $_qualys_script_path -a {{baseURL}} -t {{clientToken}} -c {{soarId}} -p {{platformId}}; rm -rf $_qualys_script_path", "timeout": 60}}}}, "parameters": {"customerId": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "activationId": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "webserviceUri": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/qualys/windows/QualysCloudAgent.exe", "provided": true, "requiresUserAction": false}, "installerLinux": {"required": true, "type": "string", "value": "https://cdn-batuta-staging.nyc3.cdn.digitaloceanspaces.com/qualys/linux/QualysCloudAgent", "provided": true, "requiresUserAction": false}}, "apiConfig": {"baseURL": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "username": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "password": {"required": true, "type": "string", "value": "", "requiresUserAction": false}}}, {"service": "ocs", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Install-OCS.ps1\" -OutFile \"Install-OCS.ps1\"; .\\Install-OCS.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Install-OCS.ps1\" -OutFile \"Install-OCS.ps1\"; .\\Install-OCS.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Install-OCS.ps1\" -OutFile \"Install-OCS.ps1\"; .\\Install-OCS.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Uninstall-OCS.ps1\" -OutFile \"Uninstall-OCS.ps1\"; .\\Uninstall-OCS.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Uninstall-OCS.ps1\" -OutFile \"Uninstall-OCS.ps1\"; .\\Uninstall-OCS.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Uninstall-OCS.ps1\" -OutFile \"Uninstall-OCS.ps1\"; .\\Uninstall-OCS.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Status-OCS.ps1\" -OutFile \"Status-OCS.ps1\"; .\\Status-OCS.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Status-OCS.ps1\" -OutFile \"Status-OCS.ps1\"; .\\Status-OCS.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/ocs/windows/Status-OCS.ps1\" -OutFile \"Status-OCS.ps1\"; .\\Status-OCS.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"installer": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "clientId": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {}}, {"service": "adlumin", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Install-Adlumin.ps1\" -OutFile \"Install-Adlumin.ps1\"; .\\Install-Adlumin.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Install-Adlumin.ps1\" -OutFile \"Install-Adlumin.ps1\"; .\\Install-Adlumin.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Install-Adlumin.ps1\" -OutFile \"Install-Adlumin.ps1\"; .\\Install-Adlumin.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Uninstall-Adlumin.ps1\" -OutFile \"Uninstall-Adlumin.ps1\"; .\\Uninstall-Adlumin.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Uninstall-Adlumin.ps1\" -OutFile \"Uninstall-Adlumin.ps1\"; .\\Uninstall-Adlumin.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Uninstall-Adlumin.ps1\" -OutFile \"Uninstall-Adlumin.ps1\"; .\\Uninstall-Adlumin.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Status-Adlumin.ps1\" -OutFile \"Status-Adlumin.ps1\"; .\\Status-Adlumin.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Status-Adlumin.ps1\" -OutFile \"Status-Adlumin.ps1\"; .\\Status-Adlumin.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/adlumin/windows/Status-Adlumin.ps1\" -OutFile \"Status-Adlumin.ps1\"; .\\Status-Adlumin.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"installer": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "clientId": {"required": false, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {}}, {"service": "sumologic", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Install-SumoLogic.ps1\" -OutFile \"Install-SumoLogic.ps1\"; .\\Install-SumoLogic.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -gt \"batuta/windows/workstations\"", "timeout": 1200}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Install-SumoLogic.ps1\" -OutFile \"Install-SumoLogic.ps1\"; .\\Install-SumoLogic.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -gt \"batuta/windows/workstations\"", "timeout": 1200}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Install-SumoLogic.ps1\" -OutFile \"Install-SumoLogic.ps1\"; .\\Install-SumoLogic.ps1 -a {{accessKey}} -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}} -gt \"batuta/windows/servers\"", "timeout": 1200}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Uninstall-SumoLogic.ps1\" -OutFile \"Uninstall-SumoLogic.ps1\"; .\\Uninstall-SumoLogic.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 300}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Uninstall-SumoLogic.ps1\" -OutFile \"Uninstall-SumoLogic.ps1\"; .\\Uninstall-SumoLogic.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 300}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Uninstall-SumoLogic.ps1\" -OutFile \"Uninstall-SumoLogic.ps1\"; .\\Uninstall-SumoLogic.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 300}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Status-SumoLogic.ps1\" -OutFile \"Status-SumoLogic.ps1\"; .\\Status-SumoLogic.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Status-SumoLogic.ps1\" -OutFile \"Status-SumoLogic.ps1\"; .\\Status-SumoLogic.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/sumologic/windows/Status-SumoLogic.ps1\" -OutFile \"Status-SumoLogic.ps1\"; .\\Status-SumoLogic.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"accessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}, "installer": {"required": true, "type": "string", "value": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/sumologic/windows/SumoCollector_windows-x64.exe", "provided": true, "requiresUserAction": false}}, "apiConfig": {"baseURL": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "accessId": {"required": true, "type": "string", "value": "", "requiresUserAction": false}, "accessKey": {"required": true, "type": "string", "value": "", "requiresUserAction": false}}}, {"service": "velociraptor", "commands": {"install": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Install-Velo.ps1\" -OutFile \"Install-Velo.ps1\"; .\\Install-Velo.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Install-Velo.ps1\" -OutFile \"Install-Velo.ps1\"; .\\Install-Velo.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Install-Velo.ps1\" -OutFile \"Install-Velo.ps1\"; .\\Install-Velo.ps1 -u {{installer}} -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 900}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Uninstall-Velo.ps1\" -OutFile \"Uninstall-Velo.ps1\"; .\\Uninstall-Velo.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Uninstall-Velo.ps1\" -OutFile \"Uninstall-Velo.ps1\"; .\\Uninstall-Velo.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Uninstall-Velo.ps1\" -OutFile \"Uninstall-Velo.ps1\"; .\\Uninstall-Velo.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Status-Velo.ps1\" -OutFile \"Status-Velo.ps1\"; .\\Status-Velo.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows 11": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Status-Velo.ps1\" -OutFile \"Status-Velo.ps1\"; .\\Status-Velo.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}, "Windows Server": {"commandString": "Invoke-WebRequest -Uri \"{{cdnBaseUrl}}/velo/windows/Status-Velo.ps1\" -OutFile \"Status-Velo.ps1\"; .\\Status-Velo.ps1 -gw {{baseURL}} -ct {{clientToken}} -sid {{soarId}} -plid {{platformId}}", "timeout": 60}}}}, "parameters": {"installer": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": false}}, "apiConfig": {}}, {"service": "hardening", "commands": {"Apply - Disable SMBv1 Support": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_smbv1_support.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_smbv1_support.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_smbv1_support.yml\"]}}'}", "timeout": 300}}}, "Apply - Disable WDigest Authentication": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_wdigest_authentication.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_wdigest_authentication.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_wdigest_authentication.yml\"]}}'}", "timeout": 300}}}, "Apply - Disable Clear Text Passwords": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_clear_text_password.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_clear_text_password.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_clear_text_password.yml\"]}}'}", "timeout": 300}}}, "Apply - Block Anonymous Enumeration of SAM Accounts and Shares": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/block_anonymous_enumeration_of_sam_accounts_and_shares.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/block_anonymous_enumeration_of_sam_accounts_and_shares.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/block_anonymous_enumeration_of_sam_accounts_and_shares.yml\"]}}'}", "timeout": 300}}}, "Apply - CredSSP Force Updated Clients": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/credssp_force_updated_clients.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/credssp_force_updated_clients.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/credssp_force_updated_clients.yml\"]}}'}", "timeout": 300}}}, "Apply - Deny Unauthenticated RPC Clients": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/deny_unauthenticated_rpc_clients.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/deny_unauthenticated_rpc_clients.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/deny_unauthenticated_rpc_clients.yml\"]}}'}", "timeout": 300}}}, "Apply - Disable Insecure Guest Logons": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_insecure_guest_logons.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_insecure_guest_logons.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_insecure_guest_logons.yml\"]}}'}", "timeout": 300}}}, "Apply - Disable LAN Manager Store Password Hash": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_lanmanager_store_password_hash.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_lanmanager_store_password_hash.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/disable_lanmanager_store_password_hash.yml\"]}}'}", "timeout": 300}}}, "Apply - Enable LSA Audit Mode": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_lsa_audit_mode.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_lsa_audit_mode.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_lsa_audit_mode.yml\"]}}'}", "timeout": 300}}}, "Apply - Enable PowerShell Script Block Logging": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_powershell_script_block_logging.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_powershell_script_block_logging.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_powershell_script_block_logging.yml\"]}}'}", "timeout": 300}}}, "Apply - Enable UAC for Non-Windows Binaries": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_uac_for_non_windows_binaries.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_uac_for_non_windows_binaries.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/enable_uac_for_non_windows_binaries.yml\"]}}'}", "timeout": 300}}}, "Apply - Filter Access from Network": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/filter_access_from_network.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/filter_access_from_network.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/filter_access_from_network.yml\"]}}'}", "timeout": 300}}}, "Apply - Filter Log on Locally": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/filter_log_on_locally.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/filter_log_on_locally.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/filter_log_on_locally.yml\"]}}'}", "timeout": 300}}}, "Apply - LSA Prevent Code Injection": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/lsa_prevent_code_injection.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/lsa_prevent_code_injection.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/lsa_prevent_code_injection.yml\"]}}'}", "timeout": 300}}}, "Apply - Require Password When Wake Up": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/require_password_when_wake_up.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/require_password_when_wake_up.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/require_password_when_wake_up.yml\"]}}'}", "timeout": 300}}}, "Apply - Set ELAM Boot Start Driver Policy": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/set_elam_boot_start_driver_policy.yml\"]}}'}", "timeout": 300}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/set_elam_boot_start_driver_policy.yml\"]}}'}", "timeout": 300}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": { \"Rules\": [\"BASELINE/rules/windows/set_elam_boot_start_driver_policy.yml\"]}}'}", "timeout": 300}}}, "Apply - Collection Hardening Basics": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": {  \"Collections\": [\"cl_{{ClientFolderName}}/collections/basic_hardening.yml\"]}}'}", "timeout": 600}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": {  \"Collections\": [\"cl_{{ClientFolderName}}/collections/basic_hardening.yml\"]}}'}", "timeout": 600}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://{{S3ExecScriptBucketName}}.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{HardenerScriptBucketName}}\" -ObjectKey \"{{HardenerScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{HardenerScriptAccessKey}}\" -SecretKey \"{{HardenerScriptSecretKey}}\" -Region \"{{HardenerScriptRegion}}\" -ScriptParameters @{ Action = \"Apply\"; HardeningsFromCloud = '{ \"BucketName\": \"{{HardeningRulesBucketName}}\", \"BucketRegion\": \"{{HardeningRulesRegion}}\", \"AccessKey\": \"{{HardeningRulesAccessKey}}\", \"AccessSecret\": \"{{HardeningRulesAccessSecret}}\", \"Hardenings\": {  \"Collections\": [\"cl_{{ClientFolderName}}/collections/basic_hardening.yml\"]}}'}", "timeout": 600}}}}, "parameters": {"S3ExecScriptBucketName": {"required": true, "type": "string", "value": "mbq-soc-scripts-public", "provided": true, "requiresUserAction": false}, "HardenerScriptBucketName": {"required": true, "type": "string", "value": "mbq-soc-scripts", "provided": true, "requiresUserAction": false}, "HardenerScriptKeyPath": {"required": true, "type": "string", "value": "hardening/windows/hardener.ps1", "provided": true, "requiresUserAction": false}, "HardenerScriptAccessKey": {"required": true, "type": "string", "value": "********************", "provided": true, "requiresUserAction": false}, "HardenerScriptSecretKey": {"required": true, "type": "string", "value": "ylLWLXjeq13zeEfrB6HBfpWsuEEgbO9va+nJ0NHy", "provided": true, "requiresUserAction": false}, "HardenerScriptRegion": {"required": true, "type": "string", "value": "us-east-1", "provided": true, "requiresUserAction": false}, "LocalPath": {"required": true, "type": "string", "value": "C:\\Program Files\\batuta\\data\\scripts", "provided": true, "requiresUserAction": false}, "HardeningRulesBucketName": {"required": true, "type": "string", "value": "mbq-soc-hardening-private-prod", "provided": true, "requiresUserAction": false}, "HardeningRulesRegion": {"required": true, "type": "string", "value": "us-east-1", "provided": true, "requiresUserAction": false}, "HardeningRulesAccessKey": {"required": true, "type": "string", "value": "For example: AKIATDQM4P52U47NMS", "provided": false, "requiresUserAction": true}, "HardeningRulesAccessSecret": {"required": true, "type": "string", "value": "For example: l2cnigiEtXQhsadh42LQunKcrQ/PfbDQLyajOI5B4", "provided": false, "requiresUserAction": true}, "ClientFolderName": {"required": true, "type": "string", "value": "For example: STARKINDUSTRIES", "provided": false, "requiresUserAction": true}}, "apiConfig": {}}, {"service": "sysmon", "commands": {"install": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ sysmonExeAccessKey = \"{{SysmonExecutableAccessKey}}\"; sysmonExeSecretKey = \"{{SysmonExecutableSecretKey}}\"; downloadPath = \"{{LocalPath}}\"; sysmonExeBucketName = \"{{SysmonExecutableBucketName}}\"; sysmonExeRegionName = \"{{SysmonExecutableRegionName}}\"; sysmonExeKey = \"{{SysmonExecutableKeyPath}}\"; sysmonConfigAccessKey = \"{{SysmonConfigurationAccessKey}}\"; sysmonConfigSecretKey = \"{{SysmonConfigurationSecretKey}}\"; sysmonConfigBucketName = \"{{SysmonConfigurationBucketName}}\"; sysmonConfigRegionName = \"{{SysmonConfigurationRegionName}}\"; sysmonConfigKey = \"{{SysmonConfigurationKeyPath}}\";  Mode = \"Install\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ sysmonExeAccessKey = \"{{SysmonExecutableAccessKey}}\"; sysmonExeSecretKey = \"{{SysmonExecutableSecretKey}}\"; downloadPath = \"{{LocalPath}}\"; sysmonExeBucketName = \"{{SysmonExecutableBucketName}}\"; sysmonExeRegionName = \"{{SysmonExecutableRegionName}}\"; sysmonExeKey = \"{{SysmonExecutableKeyPath}}\"; sysmonConfigAccessKey = \"{{SysmonConfigurationAccessKey}}\"; sysmonConfigSecretKey = \"{{SysmonConfigurationSecretKey}}\"; sysmonConfigBucketName = \"{{SysmonConfigurationBucketName}}\"; sysmonConfigRegionName = \"{{SysmonConfigurationRegionName}}\"; sysmonConfigKey = \"{{SysmonConfigurationKeyPath}}\";  Mode = \"Install\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = {{clientToken}}; bttTenantId = {{soarId}}; bttPlatformId = {{platformId}}; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ sysmonExeAccessKey = \"{{SysmonExecutableAccessKey}}\"; sysmonExeSecretKey = \"{{SysmonExecutableSecretKey}}\"; downloadPath = \"{{LocalPath}}\"; sysmonExeBucketName = \"{{SysmonExecutableBucketName}}\"; sysmonExeRegionName = \"{{SysmonExecutableRegionName}}\"; sysmonExeKey = \"{{SysmonExecutableKeyPath}}\"; sysmonConfigAccessKey = \"{{SysmonConfigurationAccessKey}}\"; sysmonConfigSecretKey = \"{{SysmonConfigurationSecretKey}}\"; sysmonConfigBucketName = \"{{SysmonConfigurationBucketName}}\"; sysmonConfigRegionName = \"{{SysmonConfigurationRegionName}}\"; sysmonConfigKey = \"{{SysmonConfigurationKeyPath}}\";  Mode = \"Install\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}}}, "uninstall": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ Mode = \"Uninstall\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ Mode = \"Uninstall\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ Mode = \"Uninstall\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}}}, "checkStatus": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ Mode = \"CheckStatus\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 120}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ Mode = \"CheckStatus\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 120}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ Mode = \"CheckStatus\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 120}}}, "updateConfiguration": {"windows": {"Windows 10": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ sysmonExeAccessKey = \"{{SysmonExecutableAccessKey}}\"; sysmonExeSecretKey = \"{{SysmonExecutableSecretKey}}\"; downloadPath = \"{{LocalPath}}\"; sysmonExeBucketName = \"{{SysmonExecutableBucketName}}\"; sysmonExeRegionName = \"{{SysmonExecutableRegionName}}\"; sysmonExeKey = \"{{SysmonExecutableKeyPath}}\"; sysmonConfigAccessKey = \"{{SysmonConfigurationAccessKey}}\"; sysmonConfigSecretKey = \"{{SysmonConfigurationSecretKey}}\"; sysmonConfigBucketName = \"{{SysmonConfigurationBucketName}}\"; sysmonConfigRegionName = \"{{SysmonConfigurationRegionName}}\"; sysmonConfigKey = \"{{SysmonConfigurationKeyPath}}\";  Mode = \"UpdateConfiguration\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}, "Windows 11": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ sysmonExeAccessKey = \"{{SysmonExecutableAccessKey}}\"; sysmonExeSecretKey = \"{{SysmonExecutableSecretKey}}\"; downloadPath = \"{{LocalPath}}\"; sysmonExeBucketName = \"{{SysmonExecutableBucketName}}\"; sysmonExeRegionName = \"{{SysmonExecutableRegionName}}\"; sysmonExeKey = \"{{SysmonExecutableKeyPath}}\"; sysmonConfigAccessKey = \"{{SysmonConfigurationAccessKey}}\"; sysmonConfigSecretKey = \"{{SysmonConfigurationSecretKey}}\"; sysmonConfigBucketName = \"{{SysmonConfigurationBucketName}}\"; sysmonConfigRegionName = \"{{SysmonConfigurationRegionName}}\"; sysmonConfigKey = \"{{SysmonConfigurationKeyPath}}\";  Mode = \"UpdateConfiguration\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}, "Windows Server": {"commandString": "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $path = \"{{LocalPath}}\"; if (-not (Test-Path $path)) { New-Item -ItemType Directory -Path $path | Out-Null }; Set-Location $path; Invoke-WebRequest -Uri \"https://mbq-soc-scripts-public.s3.amazonaws.com/general/windows/s3_exec_script.ps1\" -OutFile \"s3_exec_script.ps1\"; .\\s3_exec_script.ps1 -BucketName \"{{SysmonScriptBucketName}}\" -ObjectKey \"{{SysmonScriptKeyPath}}\" -LocalFolderPath \"{{LocalPath}}\" -AccessKey \"{{SysmonScriptAccessKey}}\" -SecretKey \"{{SysmonScriptSecretKey}}\" -Region \"{{SysmonScriptRegion}}\" -ScriptParameters @{ sysmonExeAccessKey = \"{{SysmonExecutableAccessKey}}\"; sysmonExeSecretKey = \"{{SysmonExecutableSecretKey}}\"; downloadPath = \"{{LocalPath}}\"; sysmonExeBucketName = \"{{SysmonExecutableBucketName}}\"; sysmonExeRegionName = \"{{SysmonExecutableRegionName}}\"; sysmonExeKey = \"{{SysmonExecutableKeyPath}}\"; sysmonConfigAccessKey = \"{{SysmonConfigurationAccessKey}}\"; sysmonConfigSecretKey = \"{{SysmonConfigurationSecretKey}}\"; sysmonConfigBucketName = \"{{SysmonConfigurationBucketName}}\"; sysmonConfigRegionName = \"{{SysmonConfigurationRegionName}}\"; sysmonConfigKey = \"{{SysmonConfigurationKeyPath}}\";  Mode = \"UpdateConfiguration\"; bttCdnBaseUrl = \"{{cdnBaseUrl}}\"; bttApiHostsBaseUrl = \"{{baseURL}}\"; bttClientToken = \"{{clientToken}}\"; bttTenantId = \"{{soarId}}\"; bttPlatformId = \"{{platformId}}\"; bttPlatformTemplateId = \"{{templateId}}\" }", "timeout": 600}}}}, "parameters": {"LocalPath": {"required": true, "type": "string", "value": "C:\\Program Files\\batuta\\data\\scripts", "provided": true, "requiresUserAction": false}, "SysmonScriptBucketName": {"required": true, "type": "string", "value": "mbq-soc-scripts", "provided": true, "requiresUserAction": false}, "SysmonScriptRegion": {"required": true, "type": "string", "value": "us-east-1", "provided": true, "requiresUserAction": false}, "SysmonScriptKeyPath": {"required": true, "type": "string", "value": "monitoring/windows/sysmon/sysmon_manager.ps1", "provided": true, "requiresUserAction": false}, "SysmonScriptAccessKey": {"required": true, "type": "string", "value": "********************", "provided": true, "requiresUserAction": false}, "SysmonScriptSecretKey": {"required": true, "type": "string", "value": "3y85K8mc0J4Aa1i0pZ4ai2KrGgCnQY3i5zn37Z2h", "provided": true, "requiresUserAction": false}, "SysmonExecutableBucketName": {"required": true, "type": "string", "value": "mbq-soc-scripts", "provided": true, "requiresUserAction": false}, "SysmonExecutableRegionName": {"required": true, "type": "string", "value": "us-east-1", "provided": true, "requiresUserAction": false}, "SysmonExecutableKeyPath": {"required": true, "type": "string", "value": "monitoring/windows/sysmon/Sysmon64_v15.12.exe", "provided": true, "requiresUserAction": false}, "SysmonExecutableAccessKey": {"required": true, "type": "string", "value": "********************", "provided": true, "requiresUserAction": false}, "SysmonExecutableSecretKey": {"required": true, "type": "string", "value": "3y85K8mc0J4Aa1i0pZ4ai2KrGgCnQY3i5zn37Z2h", "provided": true, "requiresUserAction": false}, "SysmonConfigurationBucketName": {"required": true, "type": "string", "value": "soc-general", "provided": false, "requiresUserAction": true}, "SysmonConfigurationKeyPath": {"required": true, "type": "string", "value": "CLIENTFOLDER/monitoring/windows/sysmon/workstations_verbose.xml", "provided": false, "requiresUserAction": true}, "SysmonConfigurationRegionName": {"required": true, "type": "string", "value": "us-east-1", "provided": false, "requiresUserAction": true}, "SysmonConfigurationAccessKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": true}, "SysmonConfigurationSecretKey": {"required": true, "type": "string", "value": "", "provided": false, "requiresUserAction": true}}, "apiConfig": {}}], "compliance": [{"platform": "windows", "command": "$tempfile = \"$(Get-Date -Format FileDateTime)-audit-windows.ps1\"; Invoke-WebRequest -Uri \"{{cdnBaseURL}}/audit-windows.ps1\" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -csv {{csvUrl}} -gw {{apiUrl}} -ct {{clientToken}} -sid {{soarId}} -poid {{policyRunId}}; Remove-Item $tempfile 2>&1 > $null", "parameters": {"apiUrl": "/gw/host", "cdnBaseURL": "/compliance", "clientToken": "", "soarId": ""}}], "batuta": {"commands": {"uninstall": {"windows": {"commandString": "$tempfile = \"$(Get-Date -Format FileDateTime)-remove-batuta.ps1\"; Invoke-WebRequest -Uri \"{{cdnBaseURL}}/batuta-agent/windows/uninstall-batuta.ps1\" -OutFile $tempfile; powershell -ExecutionPolicy Bypass -File .\\$tempfile -t {{soarId}} -u {{baseURL}}; Remove-Item $tempfile 2>&1 > $null", "timeout": 90}, "linux": {"commandString": "_bt_script_path=$(mktemp -q /tmp/bat-uninst-XXXXX.sh) && curl -Ls \"{{cdnBaseURL}}/batuta-agent/linux/uninstall-batuta.sh\" -o $_bt_script_path && sudo bash $_bt_script_path -t {{soarId}} -u {{baseURL}}; rm -rf $_bt_script_path", "timeout": 90}}}, "parameters": {}}, "queues": [{"name": "rport", "maximumConcurrency": 10, "tasks": ["run-command-on-client"]}, {"name": "batuta", "maximumConcurrency": 10, "tasks": ["run-batuta-uninstall-on-client"]}, {"name": "inventoryScan", "maximumConcurrency": 10, "tasks": ["run-scan-inventory"]}, {"name": "inventoryScanClient", "maximumConcurrency": 10, "tasks": ["run-scan-inventory-by-client"]}, {"name": "inventorySoftware", "maximumConcurrency": 10, "tasks": ["run-inventory-uninstall-software"]}, {"name": "inventoryScanApplicationPolicies", "maximumConcurrency": 10, "tasks": ["run-inventory-application-policies-notification"]}, {"name": "aptMitigationPolicyScan", "maximumConcurrency": 10, "tasks": ["run-apt-mitigation-policy"]}, {"name": "aptMitigationRunOnHosts", "maximumConcurrency": 10, "tasks": ["run-apt-mitigation-on-hosts"]}, {"name": "aptMitigationRunFixesOnHost", "maximumConcurrency": 10, "tasks": ["run-apt-mitigations-on-host"]}], "queries": [], "groups": [{"name": "Default_Windows", "description": "Batuta Default group for Windows", "type": "DYNAMIC", "protected": true, "rules": {"osKernel": [{"operator": "IS", "value": "windows"}]}}, {"name": "Default_Linux", "description": "Batuta Default group for Linux", "type": "DYNAMIC", "protected": true, "rules": {"osKernel": [{"operator": "IS", "value": "linux"}]}}], "compliancePlaybooks": [{"name": "Check-Hardening-Windows-PC", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/Check_Hardening_v3.csv", "protected": true}, {"name": "CIS-Windows-10", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance%2Ffinding_list_cis_microsoft_windows_10_enterprise_custom_v1.csv", "protected": true}, {"name": "CIS-Windows-Server-2016-Member", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2016_Member.csv", "protected": true}, {"name": "MSFT-Security-Baseline-Windows-10", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/MSFT_Security_Baseline_Windows_10_21h2.csv", "protected": true}, {"name": "MSFT-Security-Baseline-Windows-11", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/MSFT_Security_Baseline_Windows_11_22h2.csv", "protected": true}, {"name": "CIS-Windows-Server-2012-DC", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2012_DC.csv", "protected": true}, {"name": "CIS-Windows-Server-2016-DC", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2016_DC.csv", "protected": true}, {"name": "CIS-Windows-Server-2022-DC", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2022_DC.csv", "protected": true}, {"name": "CIS-Windows-Server-2012-Member", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2012_Member.csv", "protected": true}, {"name": "CIS-Windows-11", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance%2Ffinding_list_cis_microsoft_windows_11_enterprise_custom_v1.csv", "protected": true}, {"name": "CIS-Windows-Server-2019-DC", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2019_DC.csv", "protected": true}, {"name": "MSFT-Security-Baseline-Windows-Server-2022-Member", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance%2FMSFT_Security_Baseline_Windows_Server_2022_21h2_Member.csv", "protected": true}, {"name": "CIS-Windows-Server-2019-Member", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2019_Member.csv", "protected": true}, {"name": "CIS-Windows-Server-2022-Member", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/CIS_Windows_Server_2022_Member.csv", "protected": true}, {"name": "MSFT-Security-Baseline-Windows-Server-2022-DC", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/MSFT_Security_Baseline_Windows_Server_2022_21h2_DC.csv", "protected": true}, {"name": "MBQ-Hardening-Basics", "csvUrl": "https://cdn-batuta.nyc3.cdn.digitaloceanspaces.com/compliance/MBQ_Hardening_Basics.csv", "protected": true}], "compliancePolicies": [{"name": "Default_Windows_Policy", "description": "Batuta Default Compliance Policy for Windows", "group": "Default_Windows", "playbook": "MBQ-Hardening-Basics", "execution_mode": "CRON", "life_cycle": {"start_date": 1712026800000, "end_date": 4073511600000}, "schedule": {"month": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "week_day": [2], "runtime_window": {"start_time": "00:00", "end_time": "23:59"}}, "operating_mode": {"update_results": "PERIODIC", "results_expiration": "7"}, "protected": true}], "levelOfProactivity": {"qualifications": [{"level": "A", "score": 80}, {"level": "B", "score": 60}, {"level": "C", "score": 40}, {"level": "D", "score": 20}, {"level": "E", "score": 0}], "configurations": [{"name": "platform", "configuration": {"categories": [{"name": "EDR", "weight": 60, "relevance": "high", "coverage": true}, {"name": "Vulnerability Management", "weight": 40, "relevance": "high", "coverage": true}], "relevances": [{"relevance": "high", "weight": 100}, {"relevance": "medium", "weight": 50}, {"relevance": "low", "weight": 0}]}}, {"name": "compliance", "configuration": {}}, {"name": "osUpdates", "configuration": {"securityUpdatesScore": 0, "updatesScore": 59}}]}, "fixTags": [{"name": "Powershell"}, {"name": "JSC"}, {"name": "ILASM"}, {"name": "CSC"}, {"name": "CURL"}, {"name": "CERTUTIL"}, {"name": "Lockbit"}], "fixes": [{"name": "BlackBasta-FIX-010", "description": "Los archivos por lotes (p. ej., .bat o .cmd) también proporcionan al shell una lista de comandos secuenciales para ejecutar, así como operaciones normales de secuencias de comandos, como condicionales y bucles. Un uso malicioso de estos archivos es permitir a los adversarios transferir herramientas u otros archivos desde un sistema externo a un entorno comprometido.\n\nEn este escenario, un script en BAT descarga la siguiente etapa usando curl.exe y lo ejecuta desde el disco.\n\nMITRE ID: T1105 Ingress Tool Transfer\nZeroAPT ID: OCE-DW-17 Batcher", "tags": ["CURL"], "importance": "medium", "shellType": "powershell", "scripts": {"test": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n$usuariosAdmin = @()\n$files = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        #Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        #Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        #Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        } else {\n            $usuariosAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} es admin\"\n        }\n    } catch {\n        #Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Rutas a buscar\n$paths = @(\"C:\\Windows\\System32\\ C:\\Windows\\SysWOW64\\ C:\\Windows\\WinSxS\\\")\n$paths = $paths.Split(\" \")\n\n# Buscar archivos en las rutas especificadas\nforeach ($path in $paths) {\n    #Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"curl.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    #Write-Output \"No se encontraron archivos curl.exe en las rutas especificadas.\"\n    exit\n} else {\n    #Write-Output \"Archivos curl.exe encontrados: $($files -join ', ')\"\n}\n\n#comparar los permisos del usuario normal y el admin en los archivos encontrados\n$aplicoFix = $false\n$usuariosNoAdminCopia = @()\nforeach ($filePath in $files) {\n    #$filePath\n    foreach ($usuarioNormal in $usuariosNoAdmin) {\n        #$usuarioNormal\n        $acl = Get-Acl $filePath\n        $permissions = $acl.Access\n        $permissions = $permissions | Where-Object {$_.IdentityReference -like \"*\\$usuarioNormal\"} | Select-Object -ExpandProperty AccessControlType\n        if ($permissions -eq \"Deny\") {\n            $aplicoFix = $true\n            if (-not ($usuariosNoAdminCopia -contains $usuarioNormal)) {\n                $usuariosNoAdminCopia += $usuarioNormal\n            }\n        } else {\n            $aplicoFix = $false\n            Write-Output $aplicoFix\n            return\n        }\n    }\n}\n\nWrite-Output $aplicoFix", "fix": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        } else {\n            Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n        }\n    } catch {\n        Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Verificar que se encontraron usuarios\nif ($usuariosNoAdmin.Count -eq 0) {\n    Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n    exit\n} else {\n    Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n}\n\n# Rutas a buscar\n$paths = @(\"C:\\Windows\\SysWOW64\\ C:\\Windows\\System32\\ C:\\Windows\\WinSxS\\\")\n$paths = $paths.Split(\" \")\n\n# Lista para almacenar las rutas completas de los archivos encontrados\n$files = @()\n\n# Buscar archivos curl.exe en las rutas especificadas\nforeach ($path in $paths) {\n    Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"curl.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    Write-Output \"No se encontraron archivos curl.exe en las rutas especificadas.\"\n    exit\n} else {\n    Write-Output \"Archivos curl.exe encontrados: $($files -join ', ')\"\n}\n\n# Guardar permisos originales\n$aclList = @()\nforeach ($filePath in $files) {\n    Write-Output \"Procesando archivo: ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n        $acl | Add-Member -MemberType NoteProperty -Name Path -Value $filePath\n        $aclList += $acl\n        Write-Output \"Permisos originales guardados para ${filePath}\"\n    } catch {\n        Write-Output \"Error al obtener permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-010.xml\"\n$aclList | Export-Clixml -Path $xmlPath\nWrite-Output \"Permisos originales guardados en ${xmlPath}\"\n\n# Modificar permisos - Denegar ReadAndExecute para los usuarios que no están en 'Administradores'\nforeach ($filePath in $files) {\n    Write-Output \"Modificando permisos para ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n\n        foreach ($usuario in $usuariosNoAdmin) {\n            try {\n                # Crear la regla de acceso usando el nombre del usuario\n                $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($usuario, \"ReadAndExecute\", \"Deny\")\n                Write-Output \"  Modificando permisos para el usuario: ${usuario}\"\n                Write-Output \"  Regla de acceso creada: ${accessRule}\"\n\n                $acl.AddAccessRule($accessRule)\n                Write-Output \"  -> Permisos denegados para ${usuario}\"\n            } catch {\n                Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n            }\n        }\n\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos modificados para ${filePath}\"\n    } catch {\n        Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\"", "rollback": "# Ruta al archivo XML con los permisos originales\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-010.xml\"\n# Cargar los permisos originales desde el archivo XML\ntry {\n    $aclList = Import-Clixml -Path $xmlPath\n}\ncatch {\n    Write-Error \"[!]Error al intentar leer el archivo $xmlPath :$($_.Exception.Message)\"\n    Write-Output \"[!] Aplicando rollback de manera manual\"\n    $usuariosNoAdmin = @()\n\n    # Obtener los nombres de los usuarios locales\n    $usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n    # SID de grupos estándar\n    $sidAdmin = 'S-1-5-32-544'\n    $sidUser = 'S-1-5-32-545'\n\n    # Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\n    foreach ($usuario in $usuarios) {\n        try {\n            Write-Output \"Procesando usuario: ${usuario}\"\n            # Obtener el SID del usuario\n            $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n            Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n            # Obtener los grupos a los que pertenece el usuario\n            $grupos = Get-LocalGroup | Where-Object {\n                $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n            }\n            Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n            # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n            $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n            $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n            if ($esEnUsuarios -and -not $esEnAdministradores) {\n                $usuariosNoAdmin += \"${usuario}\"\n                Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n            }\n            else {\n                Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n            }\n        }\n        catch {\n            Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n        }\n    }\n\n    # Verificar que se encontraron usuarios\n    if ($usuariosNoAdmin.Count -eq 0) {\n        Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n        exit\n    }\n    else {\n        Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n    }\n\n    # Rutas a buscar\n    $paths = @(\"C:\\Windows\\SysWOW64\\ C:\\Windows\\System32\\\")\n    $paths = $paths.Split(\" \")\n\n    # Lista para almacenar las rutas completas de los archivos encontrados\n    $files = @()\n\n    # Buscar archivos curl.exe en las rutas especificadas\n    foreach ($path in $paths) {\n        Write-Output \"Buscando en ruta: ${path}\"\n        $files += Get-ChildItem -Path $path -Filter \"curl.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n    }\n\n    # Verificar que se encontraron archivos\n    if ($files.Count -eq 0) {\n        Write-Output \"No se encontraron archivos curl.exe en las rutas especificadas.\"\n        exit\n    }\n    else {\n        Write-Output \"Archivos curl.exe encontrados: $($files -join ', ')\"\n    }\n\n    # Modificar permisos, quita el DENY de los usuarios que no estan en el grupo 'Administradores'\n    foreach ($filePath in $files) {\n        Write-Output \"Modificando permisos para ${filePath}\"\n        try {\n            $acl = Get-Acl $filePath\n            foreach ($usuario in $usuariosNoAdmin) {\n                try {\n                    # Crear la regla de acceso usando el nombre del usuario\n                    $acl.Access | Where-Object { $_.IdentityReference -eq \"$env:UserDomain\\$usuario\" } | ForEach-Object { $acl.RemoveAccessRule($_) }\n                    Set-Acl -Path $filePath -AclObject $acl\n                }\n                catch {\n                    Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n                }\n            }\n            Write-Output \"Permisos modificados para ${filePath}\"\n        }\n        catch {\n            Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n        }\n    }\n    Write-Output \"Proceso completado.\"\n\n    return\n}\n\nforeach ($acl in $aclList) {\n    $filePath = $acl.Path\n    try {\n        # Intentar restaurar los permisos del archivo\n        Write-Output \"Restaurando permisos para $filePath...\"\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos restaurados para $filePath\"\n    }\n    catch {\n        # Capturar y mostrar el error si no se pueden establecer los permisos\n        Write-Output \"Error al restaurar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso de restauracion de permisos completado.\""}}, {"name": "FIN7-FIX-005", "description": "Los adversarios pueden aprovechar la confianza en el uso de herramientas de desarrollo para ejecutar de forma indirecta (proxy) programas maliciosos. Estas herramientas suelen estar firmadas con certificados legítimos que les permiten ejecutarse en un sistema eludiendo eficazmente las soluciones de control de aplicaciones.\n\nEn este escenario, la herramienta legítima .NET ilasm.exe instalada de forma predeterminada en el ambiente Windows, se utiliza para compilar y ejecutar un programa que descarga malware en el sistema.\n\nMITRE ID: T1027.004 Compile After Delivery\nZeroAPT ID: OCE-DW-05 ILASM", "tags": ["ILASM"], "importance": "medium", "shellType": "powershell", "scripts": {"test": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n$usuariosAdmin = @()\n$files = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        #Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        #Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        #Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        } else {\n            $usuariosAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} es admin\"\n        }\n    } catch {\n        #Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Rutas a buscar\n# Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n$netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n$netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n# Versiones específicas a buscar\n$targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n# Buscar versiones en la carpeta de 32 bits\n$foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n# Buscar versiones en la carpeta de 64 bits\n$foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n# Combina los arrays de 32 y 64 bits en uno solo\n$allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n$paths = $allFoundPaths\n# Buscar archivos en las rutas especificadas\nforeach ($path in $paths) {\n    #Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"ilasm.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    #Write-Output \"No se encontraron archivos ilasm.exe en las rutas especificadas.\"\n    exit\n} else {\n    #Write-Output \"Archivos ilasm.exe encontrados: $($files -join ', ')\"\n}\n\n#comparar los permisos del usuario normal y el admin en los archivos encontrados\n$aplicoFix = $false\n$usuariosNoAdminCopia = @()\nforeach ($filePath in $files) {\n    #$filePath\n    foreach ($usuarioNormal in $usuariosNoAdmin) {\n        #$usuarioNormal\n        $acl = Get-Acl $filePath\n        $permissions = $acl.Access\n        $permissions = $permissions | Where-Object {$_.IdentityReference -like \"*\\$usuarioNormal\"} | Select-Object -ExpandProperty AccessControlType\n        if ($permissions -eq \"Deny\") {\n            $aplicoFix = $true\n            if (-not ($usuariosNoAdminCopia -contains $usuarioNormal)) {\n                $usuariosNoAdminCopia += $usuarioNormal\n            }\n        } else {\n            $aplicoFix = $false\n            Write-Output $aplicoFix\n            return\n        }\n    }\n}\n\nWrite-Output $aplicoFix", "fix": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        }\n        else {\n            Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n        }\n    }\n    catch {\n        Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Verificar que se encontraron usuarios\nif ($usuariosNoAdmin.Count -eq 0) {\n    Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n    exit\n}\nelse {\n    Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n}\n\n# Rutas a buscar\n# Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n$netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n$netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n# Versiones específicas a buscar\n$targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n# Buscar versiones en la carpeta de 32 bits\n$foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n# Buscar versiones en la carpeta de 64 bits\n$foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n# Combina los arrays de 32 y 64 bits en uno solo\n$allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n$paths = $allFoundPaths\n\n# Lista para almacenar las rutas completas de los archivos encontrados\n$files = @()\n\n# Buscar archivos ilasm.exe en las rutas especificadas\nforeach ($path in $paths) {\n    Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"ilasm.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    Write-Output \"No se encontraron archivos ilasm.exe en las rutas especificadas.\"\n    exit\n}\nelse {\n    Write-Output \"Archivos ilasm.exe encontrados: $($files -join ', ')\"\n}\n\n# Guardar permisos originales\n$aclList = @()\nforeach ($filePath in $files) {\n    Write-Output \"Procesando archivo: ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n        $acl | Add-Member -MemberType NoteProperty -Name Path -Value $filePath\n        $aclList += $acl\n        Write-Output \"Permisos originales guardados para ${filePath}\"\n    }\n    catch {\n        Write-Output \"Error al obtener permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-005.xml\"\n$aclList | Export-Clixml -Path $xmlPath\nWrite-Output \"Permisos originales guardados en ${xmlPath}\"\n\n# Modificar permisos - Denegar ReadAndExecute para los usuarios que no están en 'Administradores'\nforeach ($filePath in $files) {\n    Write-Output \"Modificando permisos para ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n\n        foreach ($usuario in $usuariosNoAdmin) {\n            try {\n                # Crear la regla de acceso usando el nombre del usuario\n                $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($usuario, \"ReadAndExecute\", \"Deny\")\n                Write-Output \"  Modificando permisos para el usuario: ${usuario}\"\n                Write-Output \"  Regla de acceso creada: ${accessRule}\"\n\n                $acl.AddAccessRule($accessRule)\n                Write-Output \"  -> Permisos denegados para ${usuario}\"\n            }\n            catch {\n                Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n            }\n        }\n\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos modificados para ${filePath}\"\n    }\n    catch {\n        Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\"", "rollback": "# Ruta al archivo XML con los permisos originales\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-005.xml\"\nWrite-Output $xmlPath\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Cargar los permisos originales desde el archivo XML\ntry {\n    $aclList = Import-Clixml -Path $xmlPath\n}\ncatch {\n    Write-Error \"[!]Error al intentar leer el archivo $xmlPath :$($_.Exception.Message)\"\n    Write-Output \"[!] Aplicando rollback de manera manual\"\n    $usuariosNoAdmin = @()\n\n    # Obtener los nombres de los usuarios locales\n    $usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n    # SID de grupos estándar\n    $sidAdmin = 'S-1-5-32-544'\n    $sidUser = 'S-1-5-32-545'\n\n    # Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\n    foreach ($usuario in $usuarios) {\n        try {\n            Write-Output \"Procesando usuario: ${usuario}\"\n            # Obtener el SID del usuario\n            $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n            Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n            # Obtener los grupos a los que pertenece el usuario\n            $grupos = Get-LocalGroup | Where-Object {\n                $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n            }\n            Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n            # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n            $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n            $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n            if ($esEnUsuarios -and -not $esEnAdministradores) {\n                $usuariosNoAdmin += \"${usuario}\"\n                Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n            }\n            else {\n                Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n            }\n        }\n        catch {\n            Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n        }\n    }\n\n    # Verificar que se encontraron usuarios\n    if ($usuariosNoAdmin.Count -eq 0) {\n        Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n        exit\n    }\n    else {\n        Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n    }\n\n    # Rutas a buscar\n    # Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n    $netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n    $netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n    # Versiones específicas a buscar\n    $targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n    # Buscar versiones en la carpeta de 32 bits\n    $foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n    # Buscar versiones en la carpeta de 64 bits\n    $foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n    # Combina los arrays de 32 y 64 bits en uno solo\n    $allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n    $paths = $allFoundPaths\n\n    # Lista para almacenar las rutas completas de los archivos encontrados\n    $files = @()\n\n    # Buscar archivos ilasm.exe en las rutas especificadas\n    foreach ($path in $paths) {\n        Write-Output \"Buscando en ruta: ${path}\"\n        $files += Get-ChildItem -Path $path -Filter \"ilasm.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n    }\n\n    # Verificar que se encontraron archivos\n    if ($files.Count -eq 0) {\n        Write-Output \"No se encontraron archivos ilasm.exe en las rutas especificadas.\"\n        exit\n    }\n    else {\n        Write-Output \"Archivos ilasm.exe encontrados: $($files -join ', ')\"\n    }\n\n    # Modificar permisos, quita el DENY de los usuarios que no estan en el grupo 'Administradores'\n    foreach ($filePath in $files) {\n        Write-Output \"Modificando permisos para ${filePath}\"\n        try {\n            $acl = Get-Acl $filePath\n            foreach ($usuario in $usuariosNoAdmin) {\n                try {\n                    # Crear la regla de acceso usando el nombre del usuario\n                    $acl.Access | Where-Object { $_.IdentityReference -eq \"$env:UserDomain\\$usuario\" } | ForEach-Object { $acl.RemoveAccessRule($_) }\n                    Set-Acl -Path $filePath -AclObject $acl\n                }\n                catch {\n                    Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n                }\n            }\n            Write-Output \"Permisos modificados para ${filePath}\"\n        }\n        catch {\n            Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n        }\n    }\n    Write-Output \"Proceso completado.\"\n\n    return\n}\n\nforeach ($acl in $aclList) {\n    $filePath = $acl.Path\n    try {\n        # Intentar restaurar los permisos del archivo\n        Write-Output \"Restaurando permisos para $filePath...\"\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos restaurados para $filePath\"\n    }\n    catch {\n        # Capturar y mostrar el error si no se pueden establecer los permisos\n        Write-Output \"Error al restaurar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso de restauracion de permisos completado.\""}}, {"name": "FIN7-FIX-007", "description": "Los adversarios pueden aprovechar la confianza en el uso de herramientas de desarrollo para ejecutar de forma indirecta (proxy) programas maliciosos. Estas herramientas suelen estar firmadas con certificados legítimos que les permiten ejecutarse en un sistema eludiendo eficazmente las soluciones de control de aplicaciones.\n\nEn este escenario, la herramienta legítima .NET csc.exe instalada de forma predeterminada en el ambiente Windows, se utiliza para compilar y ejecutar un programa que descarga malware en el sistema.\n\nMITRE ID: T1027.004 Compile After Delivery\nZeroAPT ID: OCE-DW-04 CosCo", "tags": ["CSC"], "importance": "medium", "shellType": "powershell", "scripts": {"test": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n$usuariosAdmin = @()\n$files = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        #Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        #Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        #Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        }\n        else {\n            $usuariosAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} es admin\"\n        }\n    }\n    catch {\n        #Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Rutas a buscar\n# Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n$netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n$netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n# Versiones específicas a buscar\n$targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n# Buscar versiones en la carpeta de 32 bits\n$foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n# Buscar versiones en la carpeta de 64 bits\n$foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n# Combina los arrays de 32 y 64 bits en uno solo\n$allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n$paths = $allFoundPaths\n\n# Buscar archivos en las rutas especificadas\nforeach ($path in $paths) {\n    #Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"csc.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    #Write-Output \"No se encontraron archivos csc.exe en las rutas especificadas.\"\n    exit\n}\nelse {\n    #Write-Output \"Archivos csc.exe encontrados: $($files -join ', ')\"\n}\n\n#comparar los permisos del usuario normal y el admin en los archivos encontrados\n$aplicoFix = $false\n$usuariosNoAdminCopia = @()\nforeach ($filePath in $files) {\n    #$filePath\n    foreach ($usuarioNormal in $usuariosNoAdmin) {\n        #$usuarioNormal\n        $acl = Get-Acl $filePath\n        $permissions = $acl.Access\n        $permissions = $permissions | Where-Object { $_.IdentityReference -like \"*\\$usuarioNormal\" } | Select-Object -ExpandProperty AccessControlType\n        if ($permissions -eq \"Deny\") {\n            $aplicoFix = $true\n            if (-not ($usuariosNoAdminCopia -contains $usuarioNormal)) {\n                $usuariosNoAdminCopia += $usuarioNormal\n            }\n        }\n        else {\n            $aplicoFix = $false\n            Write-Output $aplicoFix\n            return\n        }\n    }\n}\n\nWrite-Output $aplicoFix", "fix": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        }\n        else {\n            Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n        }\n    }\n    catch {\n        Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Verificar que se encontraron usuarios\nif ($usuariosNoAdmin.Count -eq 0) {\n    Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n    exit\n}\nelse {\n    Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n}\n\n# Rutas a buscar\n# Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n$netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n$netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n# Versiones específicas a buscar\n$targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n# Buscar versiones en la carpeta de 32 bits\n$foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n# Buscar versiones en la carpeta de 64 bits\n$foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n# Combina los arrays de 32 y 64 bits en uno solo\n$allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n$paths = $allFoundPaths\n\n# Lista para almacenar las rutas completas de los archivos encontrados\n$files = @()\n\n# Buscar archivos csc.exe en las rutas especificadas\nforeach ($path in $paths) {\n    Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"csc.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    Write-Output \"No se encontraron archivos csc.exe en las rutas especificadas.\"\n    exit\n}\nelse {\n    Write-Output \"Archivos csc.exe encontrados: $($files -join ', ')\"\n}\n\n# Guardar permisos originales\n$aclList = @()\nforeach ($filePath in $files) {\n    Write-Output \"Procesando archivo: ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n        $acl | Add-Member -MemberType NoteProperty -Name Path -Value $filePath\n        $aclList += $acl\n        Write-Output \"Permisos originales guardados para ${filePath}\"\n    }\n    catch {\n        Write-Output \"Error al obtener permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-007.xml\"\n$aclList | Export-Clixml -Path $xmlPath\nWrite-Output \"Permisos originales guardados en ${xmlPath}\"\n\n# Modificar permisos - Denegar ReadAndExecute para los usuarios que no están en 'Administradores'\nforeach ($filePath in $files) {\n    Write-Output \"Modificando permisos para ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n\n        foreach ($usuario in $usuariosNoAdmin) {\n            try {\n                # Crear la regla de acceso usando el nombre del usuario\n                $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($usuario, \"ReadAndExecute\", \"Deny\")\n                Write-Output \"  Modificando permisos para el usuario: ${usuario}\"\n                Write-Output \"  Regla de acceso creada: ${accessRule}\"\n\n                $acl.AddAccessRule($accessRule)\n                Write-Output \"  -> Permisos denegados para ${usuario}\"\n            }\n            catch {\n                Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n            }\n        }\n\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos modificados para ${filePath}\"\n    }\n    catch {\n        Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\"", "rollback": "# Ruta al archivo XML con los permisos originales\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-007.xml\"\nWrite-Output $xmlPath\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Cargar los permisos originales desde el archivo XML\ntry {\n    $aclList = Import-Clixml -Path $xmlPath\n}\ncatch {\n    Write-Error \"[!]Error al intentar leer el archivo $xmlPath :$($_.Exception.Message)\"\n    Write-Output \"[!] Aplicando rollback de manera manual\"\n    $usuariosNoAdmin = @()\n\n    # Obtener los nombres de los usuarios locales\n    $usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n    # SID de grupos estándar\n    $sidAdmin = 'S-1-5-32-544'\n    $sidUser = 'S-1-5-32-545'\n\n    # Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\n    foreach ($usuario in $usuarios) {\n        try {\n            Write-Output \"Procesando usuario: ${usuario}\"\n            # Obtener el SID del usuario\n            $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n            Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n            # Obtener los grupos a los que pertenece el usuario\n            $grupos = Get-LocalGroup | Where-Object {\n                $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n            }\n            Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n            # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n            $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n            $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n            if ($esEnUsuarios -and -not $esEnAdministradores) {\n                $usuariosNoAdmin += \"${usuario}\"\n                Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n            }\n            else {\n                Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n            }\n        }\n        catch {\n            Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n        }\n    }\n\n    # Verificar que se encontraron usuarios\n    if ($usuariosNoAdmin.Count -eq 0) {\n        Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n        exit\n    }\n    else {\n        Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n    }\n\n    # Rutas a buscar\n    # Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n    $netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n    $netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n    # Versiones específicas a buscar\n    $targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n    # Buscar versiones en la carpeta de 32 bits\n    $foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n    # Buscar versiones en la carpeta de 64 bits\n    $foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n    # Combina los arrays de 32 y 64 bits en uno solo\n    $allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n    $paths = $allFoundPaths\n\n    # Lista para almacenar las rutas completas de los archivos encontrados\n    $files = @()\n\n    # Buscar archivos csc.exe en las rutas especificadas\n    foreach ($path in $paths) {\n        Write-Output \"Buscando en ruta: ${path}\"\n        $files += Get-ChildItem -Path $path -Filter \"csc.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n    }\n\n    # Verificar que se encontraron archivos\n    if ($files.Count -eq 0) {\n        Write-Output \"No se encontraron archivos csc.exe en las rutas especificadas.\"\n        exit\n    }\n    else {\n        Write-Output \"Archivos csc.exe encontrados: $($files -join ', ')\"\n    }\n\n    # Modificar permisos, quita el DENY de los usuarios que no estan en el grupo 'Administradores'\n    foreach ($filePath in $files) {\n        Write-Output \"Modificando permisos para ${filePath}\"\n        try {\n            $acl = Get-Acl $filePath\n            foreach ($usuario in $usuariosNoAdmin) {\n                try {\n                    # Crear la regla de acceso usando el nombre del usuario\n                    $acl.Access | Where-Object { $_.IdentityReference -eq \"$env:UserDomain\\$usuario\" } | ForEach-Object { $acl.RemoveAccessRule($_) }\n                    Set-Acl -Path $filePath -AclObject $acl\n                }\n                catch {\n                    Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n                }\n            }\n            Write-Output \"Permisos modificados para ${filePath}\"\n        }\n        catch {\n            Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n        }\n    }\n    Write-Output \"Proceso completado.\"\n\n    return\n}\n\nforeach ($acl in $aclList) {\n    $filePath = $acl.Path\n    try {\n        # Intentar restaurar los permisos del archivo\n        Write-Output \"Restaurando permisos para $filePath...\"\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos restaurados para $filePath\"\n    }\n    catch {\n        # Capturar y mostrar el error si no se pueden establecer los permisos\n        Write-Output \"Error al restaurar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso de restauracion de permisos completado.\""}}, {"name": "LOCKBIT-FIX-002", "description": "Los adversarios pueden aprovechar la confianza en el uso de herramientas de desarrollo para ejecutar de forma indirecta (proxy) programas maliciosos. Estas herramientas suelen estar firmadas con certificados legítimos que les permiten ejecutarse en un sistema eludiendo eficazmente las soluciones de control de aplicaciones.\n\nEn este escenario, la herramienta legítima .NET jsc.exe instalada de forma predeterminada en el ambiente Windows, se utiliza para compilar y ejecutar un programa que descarga malware en el sistema.\n\nMITRE ID: T1127 - Trusted Developer Utilities Proxy Execution\nZeroAPT ID: OCE-DW-02 JSourcer", "tags": ["JSC", "Lockbit"], "importance": "medium", "shellType": "powershell", "scripts": {"test": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n$usuariosAdmin = @()\n$files = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        #Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        #Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        #Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        }\n        else {\n            $usuariosAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} es admin\"\n        }\n    }\n    catch {\n        #Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Rutas a buscar\n# Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n$netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n$netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n# Versiones específicas a buscar\n$targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n# Buscar versiones en la carpeta de 32 bits\n$foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n# Buscar versiones en la carpeta de 64 bits\n$foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n# Combina los arrays de 32 y 64 bits en uno solo\n$allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n$paths = $allFoundPaths\n\n# Buscar archivos en las rutas especificadas\nforeach ($path in $paths) {\n    #Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"jsc.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    #Write-Output \"No se encontraron archivos jsc.exe en las rutas especificadas.\"\n    exit\n}\nelse {\n    #Write-Output \"Archivos jsc.exe encontrados: $($files -join ', ')\"\n}\n\n#comparar los permisos del usuario normal y el admin en los archivos encontrados\n$aplicoFix = $false\n$usuariosNoAdminCopia = @()\nforeach ($filePath in $files) {\n    #$filePath\n    foreach ($usuarioNormal in $usuariosNoAdmin) {\n        #$usuarioNormal\n        $acl = Get-Acl $filePath\n        $permissions = $acl.Access\n        $permissions = $permissions | Where-Object { $_.IdentityReference -like \"*\\$usuarioNormal\" } | Select-Object -ExpandProperty AccessControlType\n        if ($permissions -eq \"Deny\") {\n            $aplicoFix = $true\n            if (-not ($usuariosNoAdminCopia -contains $usuarioNormal)) {\n                $usuariosNoAdminCopia += $usuarioNormal\n            }\n        }\n        else {\n            $aplicoFix = $false\n            Write-Output $aplicoFix\n            return\n        }\n    }\n}\n\nWrite-Output $aplicoFix", "fix": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        }\n        else {\n            Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n        }\n    }\n    catch {\n        Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Verificar que se encontraron usuarios\nif ($usuariosNoAdmin.Count -eq 0) {\n    Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n    exit\n}\nelse {\n    Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n}\n\n# Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n$netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n$netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n# Versiones específicas a buscar\n$targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n# Buscar versiones en la carpeta de 32 bits\n$foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n# Buscar versiones en la carpeta de 64 bits\n$foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n# Combina los arrays de 32 y 64 bits en uno solo\n$allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n$paths = $allFoundPaths\n\n# Lista para almacenar las rutas completas de los archivos encontrados\n$files = @()\n\n# Buscar archivos jsc.exe en las rutas especificadas\nforeach ($path in $paths) {\n    Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"jsc.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    Write-Output \"No se encontraron archivos jsc.exe en las rutas especificadas.\"\n    exit\n}\nelse {\n    Write-Output \"Archivos jsc.exe encontrados: $($files -join ', ')\"\n}\n\n# Guardar permisos originales\n$aclList = @()\nforeach ($filePath in $files) {\n    Write-Output \"Procesando archivo: ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n        $acl | Add-Member -MemberType NoteProperty -Name Path -Value $filePath\n        $aclList += $acl\n        Write-Output \"Permisos originales guardados para ${filePath}\"\n    }\n    catch {\n        Write-Output \"Error al obtener permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-002.xml\"\n$aclList | Export-Clixml -Path $xmlPath\nWrite-Output \"Permisos originales guardados en ${xmlPath}\"\n\n# Modificar permisos - Denegar ReadAndExecute para los usuarios que no están en 'Administradores'\nforeach ($filePath in $files) {\n    Write-Output \"Modificando permisos para ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n\n        foreach ($usuario in $usuariosNoAdmin) {\n            try {\n                # Crear la regla de acceso usando el nombre del usuario\n                $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($usuario, \"ReadAndExecute\", \"Deny\")\n                Write-Output \"  Modificando permisos para el usuario: ${usuario}\"\n                Write-Output \"  Regla de acceso creada: ${accessRule}\"\n\n                $acl.AddAccessRule($accessRule)\n                Write-Output \"  -> Permisos denegados para ${usuario}\"\n            }\n            catch {\n                Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n            }\n        }\n\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos modificados para ${filePath}\"\n    }\n    catch {\n        Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\"", "rollback": "# Ruta al archivo XML con los permisos originales\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-002.xml\"\nWrite-Output $xmlPath\n# Función para buscar versiones de .NET en un path y devolver los paths en un array\nfunction Get-DotNetVersionPaths {\n    param (\n        [string]$basePath,\n        [string[]]$targetVersions\n    )\n    \n    $foundPaths = @() # Inicializa el array vacío para almacenar los paths\n\n    foreach ($version in $targetVersions) {\n        $versionPath = Join-Path $basePath $version\n        if (Test-Path $versionPath) {\n            $foundPaths += $versionPath # Agrega el path encontrado al array\n        }\n    }\n\n    return $foundPaths # Retorna el array con los paths encontrados\n}\n# Cargar los permisos originales desde el archivo XML\ntry {\n    $aclList = Import-Clixml -Path $xmlPath\n}\ncatch {\n    Write-Error \"[!]Error al intentar leer el archivo $xmlPath :$($_.Exception.Message)\"\n    Write-Output \"[!] Aplicando rollback de manera manual\"\n    $usuariosNoAdmin = @()\n\n    # Obtener los nombres de los usuarios locales\n    $usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n    # SID de grupos estándar\n    $sidAdmin = 'S-1-5-32-544'\n    $sidUser = 'S-1-5-32-545'\n\n    # Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\n    foreach ($usuario in $usuarios) {\n        try {\n            Write-Output \"Procesando usuario: ${usuario}\"\n            # Obtener el SID del usuario\n            $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n            Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n            # Obtener los grupos a los que pertenece el usuario\n            $grupos = Get-LocalGroup | Where-Object {\n                $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n            }\n            Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n            # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n            $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n            $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n            if ($esEnUsuarios -and -not $esEnAdministradores) {\n                $usuariosNoAdmin += \"${usuario}\"\n                Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n            }\n            else {\n                Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n            }\n        }\n        catch {\n            Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n        }\n    }\n\n    # Verificar que se encontraron usuarios\n    if ($usuariosNoAdmin.Count -eq 0) {\n        Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n        exit\n    }\n    else {\n        Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n    }\n\n    # Rutas a buscar\n    # Directorios donde se instalan las versiones de .NET en 32 y 64 bits\n    $netFrameworkPath32 = \"C:\\Windows\\Microsoft.NET\\Framework\"\n    $netFrameworkPath64 = \"C:\\Windows\\Microsoft.NET\\Framework64\"\n\n    # Versiones específicas a buscar\n    $targetVersions = @(\"v2.0.50727\", \"v3.5\", \"v4.0.30319\")\n\n    # Buscar versiones en la carpeta de 32 bits\n    $foundPaths32 = Get-DotNetVersionPaths -basePath $netFrameworkPath32 -targetVersions $targetVersions\n\n    # Buscar versiones en la carpeta de 64 bits\n    $foundPaths64 = Get-DotNetVersionPaths -basePath $netFrameworkPath64 -targetVersions $targetVersions\n\n    # Combina los arrays de 32 y 64 bits en uno solo\n    $allFoundPaths = $foundPaths32 + $foundPaths64 + \"C:\\Windows\\WinSxS\\\"\n    $paths = $allFoundPaths\n\n    # Lista para almacenar las rutas completas de los archivos encontrados\n    $files = @()\n\n    # Buscar archivos jsc.exe en las rutas especificadas\n    foreach ($path in $paths) {\n        Write-Output \"Buscando en ruta: ${path}\"\n        $files += Get-ChildItem -Path $path -Filter \"jsc.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n    }\n\n    # Verificar que se encontraron archivos\n    if ($files.Count -eq 0) {\n        Write-Output \"No se encontraron archivos jsc.exe en las rutas especificadas.\"\n        exit\n    }\n    else {\n        Write-Output \"Archivos jsc.exe encontrados: $($files -join ', ')\"\n    }\n\n    # Modificar permisos, quita el DENY de los usuarios que no estan en el grupo 'Administradores'\n    foreach ($filePath in $files) {\n        Write-Output \"Modificando permisos para ${filePath}\"\n        try {\n            $acl = Get-Acl $filePath\n            foreach ($usuario in $usuariosNoAdmin) {\n                try {\n                    # Crear la regla de acceso usando el nombre del usuario\n                    $acl.Access | Where-Object { $_.IdentityReference -eq \"$env:UserDomain\\$usuario\" } | ForEach-Object { $acl.RemoveAccessRule($_) }\n                    Set-Acl -Path $filePath -AclObject $acl\n                }\n                catch {\n                    Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n                }\n            }\n            Write-Output \"Permisos modificados para ${filePath}\"\n        }\n        catch {\n            Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n        }\n    }\n    Write-Output \"Proceso completado.\"\n\n    return\n}\n\nforeach ($acl in $aclList) {\n    $filePath = $acl.Path\n    try {\n        # Intentar restaurar los permisos del archivo\n        Write-Output \"Restaurando permisos para $filePath...\"\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos restaurados para $filePath\"\n    }\n    catch {\n        # Capturar y mostrar el error si no se pueden establecer los permisos\n        Write-Output \"Error al restaurar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso de restauracion de permisos completado.\""}}, {"name": "MISPADU-FIX-015", "description": "Certutil es una utilidad de línea de comandos que se puede utilizar para obtener información de la autoridad certificadora y configurar los Servicios de certificados. También se ha utilizado para decodificar archivos binarios ocultos dentro de archivos de certificados como información Base64.\n\nEn este escenario, los certificados falsos son decodificados por certutil para ejecutar un Descargador.\n\nMITRE ID:  T1140 Deobfuscate/Decode Files or Information", "tags": ["CERTUTIL"], "importance": "medium", "shellType": "powershell", "scripts": {"test": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n$usuariosAdmin = @()\n$files = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        #Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        #Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        #Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        } else {\n            $usuariosAdmin += \"${usuario}\"\n            #Write-Output \"  -> El usuario ${usuario} es admin\"\n        }\n    } catch {\n        #Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Rutas a buscar\n$paths = @(\"C:\\Windows\\System32\\ C:\\Windows\\SysWOW64\\\")\n$paths = $paths.Split(\" \")\n\n# Buscar archivos en las rutas especificadas\nforeach ($path in $paths) {\n    #Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"certutil.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    #Write-Output \"No se encontraron archivos certutil.exe en las rutas especificadas.\"\n    exit\n} else {\n    #Write-Output \"Archivos certutil.exe encontrados: $($files -join ', ')\"\n}\n\n#comparar los permisos del usuario normal y el admin en los archivos encontrados\n$aplicoFix = $false\n$usuariosNoAdminCopia = @()\nforeach ($filePath in $files) {\n    #$filePath\n    foreach ($usuarioNormal in $usuariosNoAdmin) {\n        #$usuarioNormal\n        $acl = Get-Acl $filePath\n        $permissions = $acl.Access\n        $permissions = $permissions | Where-Object {$_.IdentityReference -like \"*\\$usuarioNormal\"} | Select-Object -ExpandProperty AccessControlType\n        if ($permissions -eq \"Deny\") {\n            $aplicoFix = $true\n            if (-not ($usuariosNoAdminCopia -contains $usuarioNormal)) {\n                $usuariosNoAdminCopia += $usuarioNormal\n            }\n        } else {\n            $aplicoFix = $false\n            Write-Output $aplicoFix\n            return\n        }\n    }\n}\n\nWrite-Output $aplicoFix", "fix": "# Obtener los nombres de los usuarios locales que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores'\n$usuariosNoAdmin = @()\n\n# Obtener los nombres de los usuarios locales\n$usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n# SID de grupos estándar\n$sidAdmin = 'S-1-5-32-544'\n$sidUser = 'S-1-5-32-545'\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        Write-Output \"Procesando usuario: ${usuario}\"\n        # Obtener el SID del usuario\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n        # Obtener los grupos a los que pertenece el usuario\n        $grupos = Get-LocalGroup | Where-Object {\n            $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n        }\n        Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n        # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n        $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n        $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n        if ($esEnUsuarios -and -not $esEnAdministradores) {\n            $usuariosNoAdmin += \"${usuario}\"\n            Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n        } else {\n            Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n        }\n    } catch {\n        Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Verificar que se encontraron usuarios\nif ($usuariosNoAdmin.Count -eq 0) {\n    Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n    exit\n} else {\n    Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n}\n\n# Rutas a buscar\n$paths = @(\"C:\\Windows\\System32\\ C:\\Windows\\SysWOW64\\\")\n$paths = $paths.Split(\" \")\n\n# Lista para almacenar las rutas completas de los archivos encontrados\n$files = @()\n\n# Buscar archivos certutil.exe en las rutas especificadas\nforeach ($path in $paths) {\n    Write-Output \"Buscando en ruta: ${path}\"\n    $files += Get-ChildItem -Path $path -Filter \"certutil.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n}\n\n# Verificar que se encontraron archivos\nif ($files.Count -eq 0) {\n    Write-Output \"No se encontraron archivos certutil.exe en las rutas especificadas.\"\n    exit\n} else {\n    Write-Output \"Archivos certutil.exe encontrados: $($files -join ', ')\"\n}\n\n# Guardar permisos originales\n$aclList = @()\nforeach ($filePath in $files) {\n    Write-Output \"Procesando archivo: ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n        $acl | Add-Member -MemberType NoteProperty -Name Path -Value $filePath\n        $aclList += $acl\n        Write-Output \"Permisos originales guardados para ${filePath}\"\n    } catch {\n        Write-Output \"Error al obtener permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-015.xml\"\n$aclList | Export-Clixml -Path $xmlPath\nWrite-Output \"Permisos originales guardados en ${xmlPath}\"\n\n# Modificar permisos - Denegar ReadAndExecute para los usuarios que no están en 'Administradores'\nforeach ($filePath in $files) {\n    Write-Output \"Modificando permisos para ${filePath}\"\n    try {\n        $acl = Get-Acl $filePath\n\n        foreach ($usuario in $usuariosNoAdmin) {\n            try {\n                # Crear la regla de acceso usando el nombre del usuario\n                $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($usuario, \"ReadAndExecute\", \"Deny\")\n                Write-Output \"  Modificando permisos para el usuario: ${usuario}\"\n                Write-Output \"  Regla de acceso creada: ${accessRule}\"\n\n                $acl.AddAccessRule($accessRule)\n                Write-Output \"  -> Permisos denegados para ${usuario}\"\n            } catch {\n                Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n            }\n        }\n\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos modificados para ${filePath}\"\n    } catch {\n        Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\"", "rollback": "# Ruta al archivo XML con los permisos originales\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-015.xml\"\n# Cargar los permisos originales desde el archivo XML\ntry {\n    $aclList = Import-Clixml -Path $xmlPath\n}\ncatch {\n    Write-Error \"[!]Error al intentar leer el archivo $xmlPath :$($_.Exception.Message)\"\n    Write-Output \"[!] Aplicando rollback de manera manual\"\n    $usuariosNoAdmin = @()\n\n    # Obtener los nombres de los usuarios locales\n    $usuarios = Get-LocalUser | ForEach-Object { $_.Name }\n\n    # SID de grupos estándar\n    $sidAdmin = 'S-1-5-32-544'\n    $sidUser = 'S-1-5-32-545'\n\n    # Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\n    foreach ($usuario in $usuarios) {\n        try {\n            Write-Output \"Procesando usuario: ${usuario}\"\n            # Obtener el SID del usuario\n            $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n            Write-Output \"  SID del usuario: ${usuarioSID}\"\n\n            # Obtener los grupos a los que pertenece el usuario\n            $grupos = Get-LocalGroup | Where-Object {\n                $grupo = $_\n            (Get-LocalGroupMember -Group $grupo.Name | Where-Object { $_.Name -eq $usuario -or $_.SID.Value -eq $usuarioSID }).Count -gt 0\n            }\n            Write-Output \"  Grupos del usuario: $($grupos.Name -join ', ')\"\n\n            # Determinar si el usuario está en el grupo 'Usuarios' pero no en 'Administradores'\n            $esEnUsuarios = $grupos | Where-Object { $_.SID.Value -eq ${sidUser} }\n            $esEnAdministradores = $grupos | Where-Object { $_.SID.Value -eq ${sidAdmin} }\n\n            if ($esEnUsuarios -and -not $esEnAdministradores) {\n                $usuariosNoAdmin += \"${usuario}\"\n                Write-Output \"  -> El usuario ${usuario} está en 'Usuarios' pero no en 'Administradores'.\"\n            }\n            else {\n                Write-Output \"  -> El usuario ${usuario} no cumple con los criterios.\"\n            }\n        }\n        catch {\n            Write-Output \"Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n        }\n    }\n\n    # Verificar que se encontraron usuarios\n    if ($usuariosNoAdmin.Count -eq 0) {\n        Write-Output \"No se encontraron usuarios que pertenezcan al grupo 'Usuarios' pero no al grupo 'Administradores'.\"\n        exit\n    }\n    else {\n        Write-Output \"Usuarios que pertenecen al grupo 'Usuarios' pero no al grupo 'Administradores': $($usuariosNoAdmin -join ', ')\"\n    }\n\n    # Rutas a buscar\n    $paths = @(\"C:\\Windows\\SysWOW64\\ C:\\Windows\\System32\\\")\n    $paths = $paths.Split(\" \")\n\n    # Lista para almacenar las rutas completas de los archivos encontrados\n    $files = @()\n\n    # Buscar archivos certutil.exe en las rutas especificadas\n    foreach ($path in $paths) {\n        Write-Output \"Buscando en ruta: ${path}\"\n        $files += Get-ChildItem -Path $path -Filter \"certutil.exe\" -Recurse -ErrorAction SilentlyContinue | Select-Object -ExpandProperty FullName\n    }\n\n    # Verificar que se encontraron archivos\n    if ($files.Count -eq 0) {\n        Write-Output \"No se encontraron archivos certutil.exe en las rutas especificadas.\"\n        exit\n    }\n    else {\n        Write-Output \"Archivos certutil.exe encontrados: $($files -join ', ')\"\n    }\n\n    # Modificar permisos, quita el DENY de los usuarios que no estan en el grupo 'Administradores'\n    foreach ($filePath in $files) {\n        Write-Output \"Modificando permisos para ${filePath}\"\n        try {\n            $acl = Get-Acl $filePath\n            foreach ($usuario in $usuariosNoAdmin) {\n                try {\n                    # Crear la regla de acceso usando el nombre del usuario\n                    $acl.Access | Where-Object { $_.IdentityReference -eq \"$env:UserDomain\\$usuario\" } | ForEach-Object { $acl.RemoveAccessRule($_) }\n                    Set-Acl -Path $filePath -AclObject $acl\n                }\n                catch {\n                    Write-Output \"Error al crear la regla de acceso para el usuario ${usuario}: $($_.Exception.Message)\"\n                }\n            }\n            Write-Output \"Permisos modificados para ${filePath}\"\n        }\n        catch {\n            Write-Output \"Error al modificar permisos para ${filePath}: $($_.Exception.Message)\"\n        }\n    }\n    Write-Output \"Proceso completado.\"\n\n    return\n}\n\nforeach ($acl in $aclList) {\n    $filePath = $acl.Path\n    try {\n        # Intentar restaurar los permisos del archivo\n        Write-Output \"Restaurando permisos para $filePath...\"\n        Set-Acl -Path $filePath -AclObject $acl\n        Write-Output \"Permisos restaurados para $filePath\"\n    }\n    catch {\n        # Capturar y mostrar el error si no se pueden establecer los permisos\n        Write-Output \"Error al restaurar permisos para ${filePath}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso de restauracion de permisos completado.\""}}, {"name": "WizardSpider-FIX-014", "description": "Los instaladores de programas tipo MSI de Windows, solo pueden ser ejecutados por el usuario Administrador, sin embargo, si el valor AlwaysInstallElevated en el registro de Windows esta habilitado, permitiría a un adversario escalar privilegios, pudiendo instalar programas sin ser Administrador.\n\nMITRE ID:  T1218.007 System Binary Proxy Execution: Msiexec", "tags": ["Powershell"], "importance": "medium", "shellType": "powershell", "scripts": {"test": "# Write-Output \"[-] Comenzando test\"\n# Obtener los nombres de los usuarios locales\n$sidlist = @()\n#$usuarios = Get-LocalUser | ForEach-Object {  {$_.Eanebled }}\n$usuarios = Get-LocalUser | ForEach-Object { if ($_.Enabled -eq \"True\") { $_.Name } }\n\n$hklmValue = (Get-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\").AlwaysInstallElevated\n# Write-Output \"[-] Valor en HKLM $hklmValue\"\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        # Write-Output \"[-] Procesando usuario: $usuario  SID del usuario: $usuarioSID\"\n        $sidlist += $usuarioSID\n    }\n    catch {\n        Write-Error \"[!] Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Cargar registry para todos los usuarios\nNew-PSDrive -PSProvider Registry -Name HKU -Root HKEY_USERS | Out-Null\n\nforeach ($sid in $sidlist) {\n    try {\n        $hkcuValue = (Get-ItemProperty -Path \"HKU:\\$sid\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\").AlwaysInstallElevated\n        # Write-Output \"[-] Valor en HKCU $hkcuValue\"\n    }\n    catch {\n        Write-Error \"[!] Error al obtener valores ${sid}: $($_.Exception.Message)\"\n    }\n\n    if ($hkcuValue -eq 1 -or $hklmValue -eq 1) {\n        <# Action to perform if the condition is true #>\n        # Write-Output \"[-] Test completado\"\n        # Write-Output \"[-] El fix no esta aplicado\"\n        Write-Output \"False\"\n        break\n    }else {\n        <# Action when all if and elseif conditions are false #>\n        #Aqui indica que el fix esta aplicado\n        Write-Output \"True\"\n    }\n}", "fix": "# Obtener los nombres de los usuarios locales\n$sidlist = @()\n$usuarios = Get-LocalUser | ForEach-Object { if ($_.Enabled -eq \"True\") { $_.Name } }\n\n\necho \"Obteniendo valor antes del fix\"\n(Get-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\").AlwaysInstallElevated\necho \"Se establece el valor de las llaves a cero\"\nSet-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -Value 0\necho \"Obteniendo valor despues del fix\"\n(Get-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\").AlwaysInstallElevated\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"[-] Procesando usuario: $usuario  SID del usuario: $usuarioSID\"\n        $sidlist += $usuarioSID\n    }\n    catch {\n        Write-Error \"[!] Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Cargar registry para todos los usuarios\nNew-PSDrive -PSProvider Registry -Name HKU -Root HKEY_USERS | Out-Null\n\n# Guardar permisos originales\nWrite-Output \"[-] Guardando valores originales\"\n$originalValues = @{}\nforeach ($sid in $sidlist) {\n    try {\n        $currentValue = Get-ItemProperty -Path \"HKU:\\$sid\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -ErrorAction Stop\n        $currentValue = $currentValue.AlwaysInstallElevated\n        $originalValues[$sid] = $currentValue\n        Write-Output \"[+] Valores originales guardados para $sid, valor $currentValue\"\n    }\n    catch {\n        Write-Error \"[!] Error al obtener valores para ${sid}: $($_.Exception.Message)\"\n    }\n}\n\n#Guardado de archivo\nWrite-Output \"[-] Guardando archivo\"\n$xmlPath = \"$env:TEMP\\ZAPT-FIX-LM-114.xml\"\n$originalValues | Export-Clixml -Path $xmlPath\nWrite-Output \"[-] Valores originales guardados en $xmlPath\"\n\n#Aplicar nuevos permisos\nWrite-Output \"[-] Aplicando fix\"\nforeach ($sid in $sidlist) {\n    try {\n        Set-ItemProperty -Path \"HKU:\\$sid\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -Value 0 -ErrorAction Stop\n        $currentValue = Get-ItemProperty -Path \"HKU:\\$sid\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -ErrorAction Stop\n        $currentValue = $currentValue.AlwaysInstallElevated\n        Write-Output \"[+] Valores actualizados para $sid, valor $currentValue\"\n    }\n    catch {\n        Write-Error \"[!] Error al establecer valores para ${sid}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\"", "rollback": "#Rollback script\n# Obtener los nombres de los usuarios locales\n$sidlist = @()\n#$usuarios = Get-LocalUser | ForEach-Object {  {$_.Eanebled }}\n$usuarios = Get-LocalUser | ForEach-Object { if ($_.Enabled -eq \"True\") { $_.Name } }\n\necho \"Obteniendo valor antes del rollback\"\n(Get-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\").AlwaysInstallElevated\necho \"Se establece el valor de las llaves a 1\"\nSet-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -Value 1\necho \"Obteniendo valor despues del rollback\"\n(Get-ItemProperty -Path \"HKLM:\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\").AlwaysInstallElevated\n\n# Iterar sobre cada usuario para obtener sus grupos y los SIDs de esos grupos\nforeach ($usuario in $usuarios) {\n    try {\n        $usuarioSID = (Get-LocalUser -Name $usuario).SID.Value\n        Write-Output \"[-] Procesando usuario: $usuario  SID del usuario: $usuarioSID\"\n        $sidlist += $usuarioSID\n    }\n    catch {\n        Write-Error \"[!] Error al procesar el usuario ${usuario}: $($_.Exception.Message)\"\n    }\n}\n\n# Cargar registry para todos los usuarios\nNew-PSDrive -PSProvider Registry -Name HKU -Root HKEY_USERS | Out-Null\n\n#Aplicar rollback\nWrite-Output \"[-] Aplicando fix\"\nforeach ($sid in $sidlist) {\n    try {\n        Set-ItemProperty -Path \"HKU:\\$sid\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -Value 1 -ErrorAction Stop\n        $currentValue = Get-ItemProperty -Path \"HKU:\\$sid\\SOFTWARE\\Policies\\Microsoft\\Windows\\Installer\" -Name \"AlwaysInstallElevated\" -ErrorAction Stop\n        $currentValue = $currentValue.AlwaysInstallElevated\n        Write-Output \"[+] Valores actualizados para $sid, valor $currentValue\"\n    }\n    catch {\n        Write-Error \"[!] Error al establecer valores para ${sid}: $($_.Exception.Message)\"\n    }\n}\n\nWrite-Output \"Proceso completado.\""}}], "passiveScanRules": [{"name": "Default_Windows_Rule", "description": "", "affectedGroups": ["Default_Windows"]}, {"name": "Default_Linux_Rule", "description": "", "affectedGroups": ["Default_Linux"], "enabled": false}]}