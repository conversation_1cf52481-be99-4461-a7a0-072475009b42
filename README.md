# Batuta API

## Migraciones

- Crear nueva migración `yarn run migrations create Name`
- Se aplican automaticamente al iniciar el servidor

## Entorno de desarrollo

- Crear un archivo `.env` a partir de `.env.dist`

### Instalar y correr la API

- Podes usarlo local con yarn y node 16.10
- Podes levantarlo medienta un container de docker

### Correr en docker

- Existe el archivo `Dockerfile.dev` que instala las dependencias, copia el codigo en `/app` y corre el comando `yarn dev`
- Para no tener que instalar o usar una base mongo externa, podes usar el docker-compose:

```bash
# Armado de la imagen de la API
docker-compose build

# Levantar los servicios db, mongoadmin y API
## db -> mongo:5
## api -> Dockerfile.dev
## mongoadmin -> adicom/admin-mongo:latest
docker-compose up -d
```

- En el servicio **api** se monta el codigo desde la raiz en `/app` del container para actualizar automaticamente los cambios en el desarrollo
- Si tenes un dump de una base, podes ponerlo en la carpeta `dump` en el raiz del proyecto y ejecutar:

```
# El container de db mongo la carpeta ./dump/ en /data/dump/ del container
# soc-batuta es el nombre de la base en este ejemplo
docker-compose exec db mongorestore -d soc-batuta /data/dump/
```
